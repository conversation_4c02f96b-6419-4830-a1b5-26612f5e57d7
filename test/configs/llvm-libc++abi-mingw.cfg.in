# This testing configuration handles running the test suite against LLVM's libc++abi
# using either a DLL or a static library, with MinGW/Clang on Windows.

lit_config.load_config(config, '@CMAKE_CURRENT_BINARY_DIR@/cmake-bridge.cfg')

config.substitutions.append(('%{flags}', ''))
config.substitutions.append(('%{compile_flags}',
    '-nostdinc++ -I %{include} -I %{cxx-include} -I %{cxx-target-include} %{maybe-include-libunwind} -I %{libcxx}/test/support -I %{libcxx}/src -D_LIBCPP_ENABLE_CXX17_REMOVED_UNEXPECTED_FUNCTIONS'
))
config.substitutions.append(('%{link_flags}',
    '-nostdlib++ -L %{lib} -lc++ -lc++abi'
))
config.substitutions.append(('%{exec}',
    '%{executor} --execdir %T --env PATH=%{lib} -- '
))

import os, site
site.addsitedir(os.path.join('@LIBCXXABI_LIBCXX_PATH@', 'utils'))
import libcxx.test.params, libcxx.test.newconfig
libcxx.test.newconfig.configure(
    libcxx.test.params.DEFAULT_PARAMETERS,
    libcxx.test.features.DEFAULT_FEATURES,
    config,
    lit_config
)
