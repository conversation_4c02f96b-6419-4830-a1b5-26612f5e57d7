# Testing configuration for libc++abi when merged into a libc++ shared library.

lit_config.load_config(config, '@CMAKE_CURRENT_BINARY_DIR@/cmake-bridge.cfg')

config.substitutions.append(('%{flags}',
    '-isysroot {}'.format('@CMAKE_OSX_SYSROOT@') if '@CMAKE_OSX_SYSROOT@' else ''
))
config.substitutions.append(('%{compile_flags}',
    '-nostdinc++ -I %{include} -I %{cxx-include} -I %{cxx-target-include} %{maybe-include-libunwind} -D_LIBCPP_ENABLE_CXX17_REMOVED_UNEXPECTED_FUNCTIONS ' +
    '-I %{libcxx}/test/support -I %{libcxx}/src'
))
config.substitutions.append(('%{link_flags}',
    '-nostdlib++ -L %{lib} -Wl,-rpath,%{lib} -lc++ -pthread'
))
config.substitutions.append(('%{exec}',
    '%{executor} --execdir %T -- '
))

import os, site
site.addsitedir(os.path.join('@LIBCXXABI_LIBCXX_PATH@', 'utils'))
import libcxx.test.params, libcxx.test.newconfig, libcxx.test.newconfig
libcxx.test.newconfig.configure(
    libcxx.test.params.DEFAULT_PARAMETERS,
    libcxx.test.features.DEFAULT_FEATURES,
    config,
    lit_config
)
