@AUTO_GEN_COMMENT@

@SERIALIZED_LIT_PARAMS@

#
# This file performs the bridge between the CMake configuration and the Lit
# configuration files by setting up the LitConfig object and various Lit
# substitutions from CMake variables.
#
# Individual configuration files can take advantage of this bridge by
# loading the file and then setting up the remaining Lit substitutions.
#

import os, site
site.addsitedir(os.path.join('@LIBCXXABI_LIBCXX_PATH@', 'utils'))
site.addsitedir(os.path.join('@LIBCXXABI_SOURCE_DIR@', 'test'))
import libcxx.test.format

# Basic configuration of the test suite
config.name = os.path.basename('@LIBCXXABI_TEST_CONFIG@')
config.test_source_root = os.path.join('@LIBCXXABI_SOURCE_DIR@', 'test')
config.test_format = libcxx.test.format.CxxStandardLibraryTest()
config.recursiveExpansionLimit = 10
config.test_exec_root = '@CMAKE_BINARY_DIR@'

# TODO: This is a non-standard Lit attribute and we should have another way of accessing this.
config.host_triple = '@LLVM_HOST_TRIPLE@'

config.substitutions.append(('%{cxx}', '@CMAKE_CXX_COMPILER@'))
config.substitutions.append(('%{libcxx}', '@LIBCXXABI_LIBCXX_PATH@'))
config.substitutions.append(('%{include}', '@LIBCXXABI_SOURCE_DIR@/include'))
config.substitutions.append(('%{cxx-include}', '@LIBCXXABI_HEADER_DIR@/include/c++/v1'))
config.substitutions.append(('%{cxx-target-include}', '@LIBCXXABI_HEADER_DIR@/include/%{triple}/c++/v1'))
config.substitutions.append(('%{lib}', '@LIBCXXABI_LIBRARY_DIR@'))
config.substitutions.append(('%{executor}', '@LIBCXXABI_EXECUTOR@'))

if @LIBCXXABI_USE_LLVM_UNWINDER@:
    config.substitutions.append(('%{maybe-include-libunwind}', '-I "@LIBCXXABI_LIBUNWIND_INCLUDES_INTERNAL@"'))
else:
    config.substitutions.append(('%{maybe-include-libunwind}', ''))
