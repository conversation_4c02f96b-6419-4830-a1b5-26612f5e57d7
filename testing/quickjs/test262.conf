[config]
# general settings for test262 ES6 version

# framework style: old, new
style=new

# handle tests tagged as [noStrict]: yes, no, skip
nostrict=yes

# handle tests tagged as [strictOnly]: yes, no, skip
strict=yes

# test mode: default, default-nostrict, default-strict, strict, nostrict, both, all
mode=default

# handle tests flagged as [async]: yes, no, skip
# for these, load 'harness/doneprintHandle.js' prior to test
# and expect `print('Test262:AsyncTestComplete')` to be called for 
# successful termination
async=yes

# handle tests flagged as [module]: yes, no, skip
module=yes

# output error messages: yes, no
verbose=yes

# load harness files from this directory
harnessdir=test262/harness

# names of harness include files to skip
#harnessexclude=

# name of the error file for known errors
errorfile=test262_errors.txt

# exclude tests enumerated in this file (see also [exclude] section)
#excludefile=test262_exclude.txt

# report test results to this file
reportfile=test262_report.txt

# enumerate tests from this directory
testdir=test262/test

[features]
# Standard language features and proposed extensions
# list the features that are included
# skipped features are tagged as such to avoid warnings

AggregateError
align-detached-buffer-semantics-with-web-reality
arbitrary-module-namespace-names
Array.prototype.at
Array.prototype.flat
Array.prototype.flatMap
Array.prototype.flatten
Array.prototype.values
ArrayBuffer
array-find-from-last
arrow-function
async-functions
async-iteration
Atomics=skip #cause stuck
Atomics.waitAsync
BigInt
caller
class
class-fields-private
class-fields-private-in
class-fields-public
class-methods-private
class-static-block
class-static-fields-public
class-static-fields-private
class-static-methods-private
coalesce-expression
computed-property-names
const
cross-realm
DataView
DataView.prototype.getFloat32
DataView.prototype.getFloat64
DataView.prototype.getInt16
DataView.prototype.getInt32
DataView.prototype.getInt8
DataView.prototype.getUint16
DataView.prototype.getUint32
DataView.prototype.setUint8
default-arg
default-parameters
destructuring-assignment
destructuring-binding
dynamic-import=skip #this causes shape npe crash
error-cause
export-star-as-namespace-from-module
FinalizationGroup
FinalizationRegistry
FinalizationRegistry.prototype.cleanupSome
Float32Array
Float64Array
for-in-order
for-of
generators
globalThis
hashbang
host-gc-required
import.meta
import-assertions
Uint32Array
Int32Array
Int16Array
Int8Array
IsHTMLDDA
json-modules
json-superset
legacy-regexp
let
logical-assignment-operators
Map
new.target
numeric-separator-literal
object-rest
object-spread
Object.fromEntries
Object.is
Object.hasOwn
optional-catch-binding
optional-chaining
Promise
Promise.allSettled
Promise.any
Promise.prototype.finally
Proxy
proxy-missing-checks
Reflect
Reflect.construct
Reflect.set
Reflect.setPrototypeOf
regexp-dotall
regexp-lookbehind
regexp-match-indices
regexp-named-groups
regexp-unicode-property-escapes
regexp-v-flag
resizable-arraybuffer
rest-parameters
Set
ShadowRealm
SharedArrayBuffer
string-trimming
String.fromCodePoint
String.prototype.at
String.prototype.endsWith
String.prototype.includes
String.prototype.matchAll
String.prototype.replaceAll
String.prototype.trimEnd
String.prototype.trimStart
super
Symbol
Symbol.asyncIterator
Symbol.hasInstance
Symbol.isConcatSpreadable
Symbol.iterator
Symbol.match
Symbol.matchAll
Symbol.prototype.description
Symbol.replace
Symbol.search
Symbol.species
Symbol.split
Symbol.toPrimitive
Symbol.toStringTag
Symbol.unscopables
tail-call-optimization
template
top-level-await
Temporal=skip #cause test262 exit
TypedArray
TypedArray.prototype.at
u180e
Uint32Array
Uint16Array
Uint8Array
Uint8ClampedArray
WeakMap
WeakRef
WeakSet
well-formed-json-stringify
__getter__
__setter__
__proto__

[exclude]
# # list excluded tests and directories here

#fixme: these cases will cause segv fault now


# # these unsupported list is expired. some of them maybe already supported

# # intl not supported
# test262/test/intl402/

# # these builtins are not supported:
# test262/test/built-ins/BigInt/
# test262/test/built-ins/FinalizationRegistry/

# # incompatible with the "caller" feature
# test262/test/built-ins/Function/prototype/restricted-property-caller.js
# test262/test/built-ins/ThrowTypeError/unique-per-realm-function-proto.js
# test262/test/built-ins/Function/prototype/restricted-property-caller.js

# # bigint
# test262/test/built-ins/Object/seal/seal-bigint64array.js
# test262/test/built-ins/Object/seal/seal-biguint64array.js
# test262/test/built-ins/TypedArrayConstructors/BigUint64Array/is-a-constructor.js

# # built-in object name property
# test262/test/built-ins/Promise/executor-function-name.js
# test262/test/language/expressions/class/name.js

# # no debugger keyword support
# test262/test/language/statements/debugger/statement.js

# # bogus html close comment test with syntax error
# test262/test/annexB/built-ins/Function/createdynfn-html-close-comment-params.js

# # bogus class tests
# test262/test/language/statements/class/elements/syntax/valid/export-default-grammar-static-ctor-accessor-meth-valid.js
# test262/test/language/statements/class/elements/syntax/valid/export-default-grammar-static-ctor-async-gen-meth-valid.js
# test262/test/language/statements/class/elements/syntax/valid/export-default-grammar-static-ctor-async-meth-valid.js
# test262/test/language/statements/class/elements/syntax/valid/export-default-grammar-static-ctor-gen-meth-valid.js
# test262/test/language/statements/class/elements/syntax/valid/export-default-grammar-static-ctor-meth-valid.js

# # slow tests
# #test262/test/built-ins/RegExp/CharacterClassEscapes/
# #test262/test/built-ins/RegExp/property-escapes/

# # strict mode length property issue, Symbol. fixed in realm patch as a side-effect
# test262/test/built-ins/Array/length/define-own-prop-length-coercion-order-set.js
# test262/test/built-ins/Array/length/define-own-prop-length-coercion-order.js
# test262/test/built-ins/Array/length/define-own-prop-length-overflow-order.js

# # AsyncFromSyncIteratorPrototype
# test262/test/built-ins/AsyncFromSyncIteratorPrototype/next/absent-value-not-passed.js
# test262/test/built-ins/AsyncFromSyncIteratorPrototype/return/absent-value-not-passed.js

# # Date related set argument coersion order
# test262/test/built-ins/Date/prototype/setDate/arg-coercion-order.js
# test262/test/built-ins/Date/prototype/setHours/arg-coercion-order.js
# test262/test/built-ins/Date/prototype/setMilliseconds/arg-coercion-order.js
# test262/test/built-ins/Date/prototype/setMinutes/arg-coercion-order.js
# test262/test/built-ins/Date/prototype/setMonth/arg-coercion-order.js
# test262/test/built-ins/Date/prototype/setSeconds/arg-coercion-order.js
# test262/test/built-ins/Date/prototype/setUTCDate/arg-coercion-order.js
# test262/test/built-ins/Date/prototype/setUTCHours/arg-coercion-order.js
# test262/test/built-ins/Date/prototype/setUTCMilliseconds/arg-coercion-order.js
# test262/test/built-ins/Date/prototype/setUTCMinutes/arg-coercion-order.js
# test262/test/built-ins/Date/prototype/setUTCMonth/arg-coercion-order.js
# test262/test/built-ins/Date/prototype/setUTCSeconds/arg-coercion-order.js

# # Get own property descriptor bug
# test262/test/built-ins/Object/getOwnPropertyDescriptor/primitive-string.js

# # Proxy key related
# test262/test/built-ins/Proxy/ownKeys/trap-is-undefined-target-is-proxy.js
# test262/test/built-ins/Proxy/set/trap-is-missing-target-is-proxy.js
# test262/test/language/expressions/object/dstr/object-rest-proxy-get-not-called-on-dontenum-keys.js
# test262/test/language/expressions/object/object-spread-proxy-get-not-called-on-dontenum-keys.js

# # Reflect.apply empty
# test262/test/built-ins/Reflect/apply/arguments-list-is-not-array-like.js

# # Global Property Issue
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--1.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--2.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--3.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--4.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--5.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--6.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--7.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--8.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--9.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--10.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--11.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--12.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--13.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--14.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--15.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--16.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--17.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--18.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--19.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--20.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v--21.js
# test262/test/language/expressions/compound-assignment/compound-assignment-operator-calls-putvalue-lref--v-.js
# test262/test/language/expressions/postfix-decrement/operator-x-postfix-decrement-calls-putvalue-lhs-newvalue--1.js
# test262/test/language/expressions/postfix-decrement/operator-x-postfix-decrement-calls-putvalue-lhs-newvalue-.js
# test262/test/language/expressions/postfix-increment/operator-x-postfix-increment-calls-putvalue-lhs-newvalue--1.js
# test262/test/language/expressions/postfix-increment/operator-x-postfix-increment-calls-putvalue-lhs-newvalue-.js
# test262/test/language/expressions/prefix-decrement/operator-prefix-decrement-x-calls-putvalue-lhs-newvalue--1.js
# test262/test/language/expressions/prefix-decrement/operator-prefix-decrement-x-calls-putvalue-lhs-newvalue-.js
# test262/test/language/expressions/prefix-increment/operator-prefix-increment-x-calls-putvalue-lhs-newvalue--1.js
# test262/test/language/expressions/prefix-increment/operator-prefix-increment-x-calls-putvalue-lhs-newvalue-.js

# # Realm - not supported
# test262/test/built-ins/TypedArrayConstructors/internals/DefineOwnProperty/detached-buffer-throws-realm.js
# test262/test/harness/assert-throws-same-realm.js

# # not supported feature / unimportant tiny bugs (maybe v8 also failed QAQ~)

# test262/test/built-ins/Proxy/create-handler-is-revoked-proxy.js
# test262/test/built-ins/Proxy/create-target-is-revoked-function-proxy.js
# test262/test/built-ins/Proxy/create-target-is-revoked-proxy.js
# test262/test/built-ins/Proxy/revocable/handler-is-revoked-proxy.js
# test262/test/built-ins/Proxy/revocable/target-is-revoked-function-proxy.js
# test262/test/built-ins/Proxy/revocable/target-is-revoked-proxy.js

# test262/test/built-ins/TypedArray/prototype/every/callbackfn-detachbuffer.js
# test262/test/built-ins/TypedArray/prototype/filter/callbackfn-detachbuffer.js
# test262/test/built-ins/TypedArray/prototype/forEach/callbackfn-detachbuffer.js
# test262/test/built-ins/TypedArray/prototype/map/callbackfn-detachbuffer.js
# test262/test/built-ins/TypedArray/prototype/reduce/callbackfn-detachbuffer.js
# test262/test/built-ins/TypedArray/prototype/reduceRight/callbackfn-detachbuffer.js
# test262/test/built-ins/TypedArray/prototype/some/callbackfn-detachbuffer.js
# test262/test/built-ins/TypedArray/prototype/sort/sorted-values.js
# test262/test/built-ins/TypedArrayConstructors/internals/DefineOwnProperty/detached-buffer.js
# test262/test/built-ins/TypedArrayConstructors/internals/DefineOwnProperty/key-is-numericindex-desc-configurable.js
# test262/test/built-ins/TypedArrayConstructors/internals/DefineOwnProperty/key-is-numericindex-desc-not-configurable-throws.js
# test262/test/built-ins/TypedArrayConstructors/internals/DefineOwnProperty/key-is-numericindex.js
# test262/test/built-ins/TypedArrayConstructors/internals/DefineOwnProperty/tonumber-value-detached-buffer.js
# test262/test/built-ins/TypedArrayConstructors/internals/GetOwnProperty/index-prop-desc.js
# test262/test/built-ins/TypedArrayConstructors/internals/Set/detached-buffer.js
# test262/test/built-ins/TypedArrayConstructors/internals/Set/key-is-not-integer.js
# test262/test/built-ins/TypedArrayConstructors/internals/Set/key-is-out-of-bounds.js
# test262/test/built-ins/TypedArrayConstructors/internals/Set/tonumber-value-detached-buffer.js

# test262/test/language/computed-property-names/class/static/method-number-order.js
# test262/test/language/computed-property-names/class/static/method-string-order.js
# test262/test/language/computed-property-names/class/static/method-symbol-order.js
# test262/test/language/expressions/assignment/assignment-operator-calls-putvalue-lref--rval--1.js
# test262/test/language/expressions/assignment/assignment-operator-calls-putvalue-lref--rval-.js
# test262/test/language/expressions/assignment/target-member-computed-reference-null.js
# test262/test/language/expressions/assignment/target-member-computed-reference-undefined.js
# test262/test/language/expressions/delete/super-property-null-base.js
# test262/test/language/expressions/object/dstr/object-rest-proxy-gopd-not-called-on-excluded-keys.js
# test262/test/language/expressions/super/call-proto-not-ctor.js
# test262/test/language/expressions/template-literal/invalid-legacy-octal-escape-sequence-8.js
# test262/test/language/expressions/template-literal/invalid-legacy-octal-escape-sequence-9.js
# test262/test/language/literals/string/legacy-non-octal-escape-sequence-8-strict.js
# test262/test/language/literals/string/legacy-non-octal-escape-sequence-9-strict.js
# test262/test/language/literals/string/legacy-non-octal-escape-sequence-8-strict-explicit-pragma.js
# test262/test/language/literals/string/legacy-non-octal-escape-sequence-9-strict-explicit-pragma.js
# test262/test/language/statements/for-of/head-lhs-async-invalid.js
# test262/test/language/module-code/eval-gtbndng-indirect-update-dflt.js
# test262/test/language/module-code/export-default-asyncfunction-declaration-binding.js
# test262/test/language/module-code/export-default-asyncgenerator-declaration-binding.js
# test262/test/language/module-code/export-default-function-declaration-binding.js
# test262/test/language/module-code/export-default-generator-declaration-binding.js
# test262/test/language/module-code/export-expname-binding-index.js
# test262/test/language/module-code/export-expname-binding-string.js
# test262/test/language/module-code/export-expname-from-binding-string.js
# test262/test/language/module-code/export-expname-from-star-string.js
# test262/test/language/module-code/export-expname-from-star.js
# test262/test/language/module-code/export-expname-from-string-binding.js
# test262/test/language/module-code/export-expname-from-string-string.js
# test262/test/language/module-code/export-expname-from-string.js
# test262/test/language/module-code/export-expname-import-string-binding.js
# test262/test/language/module-code/instn-iee-bndng-fun.js
# test262/test/language/module-code/instn-iee-bndng-gen.js
# test262/test/language/module-code/instn-local-bndng-export-fun.js
# test262/test/language/module-code/instn-local-bndng-export-gen.js
# test262/test/language/module-code/instn-local-bndng-fun.js
# test262/test/language/module-code/instn-local-bndng-gen.js
# test262/test/language/module-code/instn-named-bndng-dflt-fun-anon.js
# test262/test/language/module-code/instn-named-bndng-dflt-fun-named.js
# test262/test/language/module-code/instn-named-bndng-dflt-gen-anon.js
# test262/test/language/module-code/instn-named-bndng-dflt-gen-named.js
# test262/test/language/module-code/instn-named-bndng-fun.js
# test262/test/language/module-code/instn-named-bndng-gen.js
# test262/test/language/module-code/instn-uniq-env-rec.js
# test262/test/language/module-code/namespace/internals/define-own-property.js
# test262/test/language/statements/class/definition/fn-length-static-precedence-order.js
# test262/test/language/statements/class/definition/fn-name-static-precedence-order.js
# test262/test/language/statements/for-await-of/async-gen-decl-dstr-array-elem-iter-rtrn-close-null.js
# test262/test/built-ins/RegExp/prototype/Symbol.replace/coerce-lastindex.js
# test262/test/built-ins/RegExp/prototype/Symbol.replace/poisoned-stdlib.js
# test262/test/built-ins/RegExp/prototype/Symbol.replace/result-coerce-groups-err.js
# test262/test/built-ins/RegExp/quantifier-integer-limit.js
# test262/test/built-ins/RegExp/duplicate-flags.js
# test262/test/built-ins/RegExp/named-groups/non-unicode-property-names-valid.js
# test262/test/built-ins/RegExp/property-escapes/generated/

[tests]
# list test files or use config.testdir
