# Testing Reference

<!--* toc_depth: 3 *-->

This page lists the facilities provided by GoogleTest for writing test programs.
To use them, include the header `gtest/gtest.h`.

## Macros

GoogleTest defines the following macros for writing tests.

### TEST {#TEST}

<pre>
TEST(<em>TestSuiteName</em>, <em>TestName</em>) {
  ... <em>statements</em> ...
}
</pre>

Defines an individual test named *`TestName`* in the test suite
*`TestSuiteName`*, consisting of the given statements.

Both arguments *`TestSuiteName`* and *`TestName`* must be valid C++ identifiers
and must not contain underscores (`_`). Tests in different test suites can have
the same individual name.

The statements within the test body can be any code under test.
[Assertions](assertions.md) used within the test body determine the outcome of
the test.

### TEST_F {#TEST_F}

<pre>
TEST_F(<em>TestFixtureName</em>, <em>TestName</em>) {
  ... <em>statements</em> ...
}
</pre>

Defines an individual test named *`TestName`* that uses the test fixture class
*`TestFixtureName`*. The test suite name is *`TestFixtureName`*.

Both arguments *`TestFixtureName`* and *`TestName`* must be valid C++
identifiers and must not contain underscores (`_`). *`TestFixtureName`* must be
the name of a test fixture class—see
[Test Fixtures](../primer.md#same-data-multiple-tests).

The statements within the test body can be any code under test.
[Assertions](assertions.md) used within the test body determine the outcome of
the test.

### TEST_P {#TEST_P}

<pre>
TEST_P(<em>TestFixtureName</em>, <em>TestName</em>) {
  ... <em>statements</em> ...
}
</pre>

Defines an individual value-parameterized test named *`TestName`* that uses the
test fixture class *`TestFixtureName`*. The test suite name is
*`TestFixtureName`*.

Both arguments *`TestFixtureName`* and *`TestName`* must be valid C++
identifiers and must not contain underscores (`_`). *`TestFixtureName`* must be
the name of a value-parameterized test fixture class—see
[Value-Parameterized Tests](../advanced.md#value-parameterized-tests).

The statements within the test body can be any code under test. Within the test
body, the test parameter can be accessed with the `GetParam()` function (see
[`WithParamInterface`](#WithParamInterface)). For example:

```cpp
TEST_P(MyTestSuite, DoesSomething) {
  ...
  EXPECT_TRUE(DoSomething(GetParam()));
  ...
}
```

[Assertions](assertions.md) used within the test body determine the outcome of
the test.

See also [`INSTANTIATE_TEST_SUITE_P`](#INSTANTIATE_TEST_SUITE_P).

### INSTANTIATE_TEST_SUITE_P {#INSTANTIATE_TEST_SUITE_P}

`INSTANTIATE_TEST_SUITE_P(`*`InstantiationName`*`,`*`TestSuiteName`*`,`*`param_generator`*`)`
\
`INSTANTIATE_TEST_SUITE_P(`*`InstantiationName`*`,`*`TestSuiteName`*`,`*`param_generator`*`,`*`name_generator`*`)`

Instantiates the value-parameterized test suite *`TestSuiteName`* (defined with
[`TEST_P`](#TEST_P)).

The argument *`InstantiationName`* is a unique name for the instantiation of the
test suite, to distinguish between multiple instantiations. In test output, the
instantiation name is added as a prefix to the test suite name
*`TestSuiteName`*.

The argument *`param_generator`* is one of the following GoogleTest-provided
functions that generate the test parameters, all defined in the `::testing`
namespace:

<span id="param-generators"></span>

| Parameter Generator | Behavior                                             |
| ------------------- | ---------------------------------------------------- |
| `Range(begin, end [, step])` | Yields values `{begin, begin+step, begin+step+step, ...}`. The values do not include `end`. `step` defaults to 1. |
| `Values(v1, v2, ..., vN)`    | Yields values `{v1, v2, ..., vN}`.          |
| `ValuesIn(container)` or `ValuesIn(begin,end)` | Yields values from a C-style array, an STL-style container, or an iterator range `[begin, end)`. |
| `Bool()`                     | Yields sequence `{false, true}`.            |
| `Combine(g1, g2, ..., gN)`   | Yields as `std::tuple` *n*-tuples all combinations (Cartesian product) of the values generated by the given *n* generators `g1`, `g2`, ..., `gN`. |
| `ConvertGenerator<T>(g)`     | Yields values generated by generator `g`, `static_cast` to `T`. |

The optional last argument *`name_generator`* is a function or functor that
generates custom test name suffixes based on the test parameters. The function
must accept an argument of type
[`TestParamInfo<class ParamType>`](#TestParamInfo) and return a `std::string`.
The test name suffix can only contain alphanumeric characters and underscores.
GoogleTest provides [`PrintToStringParamName`](#PrintToStringParamName), or a
custom function can be used for more control:

```cpp
INSTANTIATE_TEST_SUITE_P(
    MyInstantiation, MyTestSuite,
    ::testing::Values(...),
    [](const ::testing::TestParamInfo<MyTestSuite::ParamType>& info) {
      // Can use info.param here to generate the test suffix
      std::string name = ...
      return name;
    });
```

For more information, see
[Value-Parameterized Tests](../advanced.md#value-parameterized-tests).

See also
[`GTEST_ALLOW_UNINSTANTIATED_PARAMETERIZED_TEST`](#GTEST_ALLOW_UNINSTANTIATED_PARAMETERIZED_TEST).

### TYPED_TEST_SUITE {#TYPED_TEST_SUITE}

`TYPED_TEST_SUITE(`*`TestFixtureName`*`,`*`Types`*`)`

Defines a typed test suite based on the test fixture *`TestFixtureName`*. The
test suite name is *`TestFixtureName`*.

The argument *`TestFixtureName`* is a fixture class template, parameterized by a
type, for example:

```cpp
template <typename T>
class MyFixture : public ::testing::Test {
 public:
  ...
  using List = std::list<T>;
  static T shared_;
  T value_;
};
```

The argument *`Types`* is a [`Types`](#Types) object representing the list of
types to run the tests on, for example:

```cpp
using MyTypes = ::testing::Types<char, int, unsigned int>;
TYPED_TEST_SUITE(MyFixture, MyTypes);
```

The type alias (`using` or `typedef`) is necessary for the `TYPED_TEST_SUITE`
macro to parse correctly.

See also [`TYPED_TEST`](#TYPED_TEST) and
[Typed Tests](../advanced.md#typed-tests) for more information.

### TYPED_TEST {#TYPED_TEST}

<pre>
TYPED_TEST(<em>TestSuiteName</em>, <em>TestName</em>) {
  ... <em>statements</em> ...
}
</pre>

Defines an individual typed test named *`TestName`* in the typed test suite
*`TestSuiteName`*. The test suite must be defined with
[`TYPED_TEST_SUITE`](#TYPED_TEST_SUITE).

Within the test body, the special name `TypeParam` refers to the type parameter,
and `TestFixture` refers to the fixture class. See the following example:

```cpp
TYPED_TEST(MyFixture, Example) {
  // Inside a test, refer to the special name TypeParam to get the type
  // parameter.  Since we are inside a derived class template, C++ requires
  // us to visit the members of MyFixture via 'this'.
  TypeParam n = this->value_;

  // To visit static members of the fixture, add the 'TestFixture::'
  // prefix.
  n += TestFixture::shared_;

  // To refer to typedefs in the fixture, add the 'typename TestFixture::'
  // prefix. The 'typename' is required to satisfy the compiler.
  typename TestFixture::List values;

  values.push_back(n);
  ...
}
```

For more information, see [Typed Tests](../advanced.md#typed-tests).

### TYPED_TEST_SUITE_P {#TYPED_TEST_SUITE_P}

`TYPED_TEST_SUITE_P(`*`TestFixtureName`*`)`

Defines a type-parameterized test suite based on the test fixture
*`TestFixtureName`*. The test suite name is *`TestFixtureName`*.

The argument *`TestFixtureName`* is a fixture class template, parameterized by a
type. See [`TYPED_TEST_SUITE`](#TYPED_TEST_SUITE) for an example.

See also [`TYPED_TEST_P`](#TYPED_TEST_P) and
[Type-Parameterized Tests](../advanced.md#type-parameterized-tests) for more
information.

### TYPED_TEST_P {#TYPED_TEST_P}

<pre>
TYPED_TEST_P(<em>TestSuiteName</em>, <em>TestName</em>) {
  ... <em>statements</em> ...
}
</pre>

Defines an individual type-parameterized test named *`TestName`* in the
type-parameterized test suite *`TestSuiteName`*. The test suite must be defined
with [`TYPED_TEST_SUITE_P`](#TYPED_TEST_SUITE_P).

Within the test body, the special name `TypeParam` refers to the type parameter,
and `TestFixture` refers to the fixture class. See [`TYPED_TEST`](#TYPED_TEST)
for an example.

See also [`REGISTER_TYPED_TEST_SUITE_P`](#REGISTER_TYPED_TEST_SUITE_P) and
[Type-Parameterized Tests](../advanced.md#type-parameterized-tests) for more
information.

### REGISTER_TYPED_TEST_SUITE_P {#REGISTER_TYPED_TEST_SUITE_P}

`REGISTER_TYPED_TEST_SUITE_P(`*`TestSuiteName`*`,`*`TestNames...`*`)`

Registers the type-parameterized tests *`TestNames...`* of the test suite
*`TestSuiteName`*. The test suite and tests must be defined with
[`TYPED_TEST_SUITE_P`](#TYPED_TEST_SUITE_P) and [`TYPED_TEST_P`](#TYPED_TEST_P).

For example:

```cpp
// Define the test suite and tests.
TYPED_TEST_SUITE_P(MyFixture);
TYPED_TEST_P(MyFixture, HasPropertyA) { ... }
TYPED_TEST_P(MyFixture, HasPropertyB) { ... }

// Register the tests in the test suite.
REGISTER_TYPED_TEST_SUITE_P(MyFixture, HasPropertyA, HasPropertyB);
```

See also [`INSTANTIATE_TYPED_TEST_SUITE_P`](#INSTANTIATE_TYPED_TEST_SUITE_P) and
[Type-Parameterized Tests](../advanced.md#type-parameterized-tests) for more
information.

### INSTANTIATE_TYPED_TEST_SUITE_P {#INSTANTIATE_TYPED_TEST_SUITE_P}

`INSTANTIATE_TYPED_TEST_SUITE_P(`*`InstantiationName`*`,`*`TestSuiteName`*`,`*`Types`*`)`

Instantiates the type-parameterized test suite *`TestSuiteName`*. The test suite
must be registered with
[`REGISTER_TYPED_TEST_SUITE_P`](#REGISTER_TYPED_TEST_SUITE_P).

The argument *`InstantiationName`* is a unique name for the instantiation of the
test suite, to distinguish between multiple instantiations. In test output, the
instantiation name is added as a prefix to the test suite name
*`TestSuiteName`*.

The argument *`Types`* is a [`Types`](#Types) object representing the list of
types to run the tests on, for example:

```cpp
using MyTypes = ::testing::Types<char, int, unsigned int>;
INSTANTIATE_TYPED_TEST_SUITE_P(MyInstantiation, MyFixture, MyTypes);
```

The type alias (`using` or `typedef`) is necessary for the
`INSTANTIATE_TYPED_TEST_SUITE_P` macro to parse correctly.

For more information, see
[Type-Parameterized Tests](../advanced.md#type-parameterized-tests).

### FRIEND_TEST {#FRIEND_TEST}

`FRIEND_TEST(`*`TestSuiteName`*`,`*`TestName`*`)`

Within a class body, declares an individual test as a friend of the class,
enabling the test to access private class members.

If the class is defined in a namespace, then in order to be friends of the
class, test fixtures and tests must be defined in the exact same namespace,
without inline or anonymous namespaces.

For example, if the class definition looks like the following:

```cpp
namespace my_namespace {

class MyClass {
  friend class MyClassTest;
  FRIEND_TEST(MyClassTest, HasPropertyA);
  FRIEND_TEST(MyClassTest, HasPropertyB);
  ... definition of class MyClass ...
};

}  // namespace my_namespace
```

Then the test code should look like:

```cpp
namespace my_namespace {

class MyClassTest : public ::testing::Test {
  ...
};

TEST_F(MyClassTest, HasPropertyA) { ... }
TEST_F(MyClassTest, HasPropertyB) { ... }

}  // namespace my_namespace
```

See [Testing Private Code](../advanced.md#testing-private-code) for more
information.

### SCOPED_TRACE {#SCOPED_TRACE}

`SCOPED_TRACE(`*`message`*`)`

Causes the current file name, line number, and the given message *`message`* to
be added to the failure message for each assertion failure that occurs in the
scope.

For more information, see
[Adding Traces to Assertions](../advanced.md#adding-traces-to-assertions).

See also the [`ScopedTrace` class](#ScopedTrace).

### GTEST_SKIP {#GTEST_SKIP}

`GTEST_SKIP()`

Prevents further test execution at runtime.

Can be used in individual test cases or in the `SetUp()` methods of test
environments or test fixtures (classes derived from the
[`Environment`](#Environment) or [`Test`](#Test) classes). If used in a global
test environment `SetUp()` method, it skips all tests in the test program. If
used in a test fixture `SetUp()` method, it skips all tests in the corresponding
test suite.

Similar to assertions, `GTEST_SKIP` allows streaming a custom message into it.

See [Skipping Test Execution](../advanced.md#skipping-test-execution) for more
information.

### GTEST_ALLOW_UNINSTANTIATED_PARAMETERIZED_TEST {#GTEST_ALLOW_UNINSTANTIATED_PARAMETERIZED_TEST}

`GTEST_ALLOW_UNINSTANTIATED_PARAMETERIZED_TEST(`*`TestSuiteName`*`)`

Allows the value-parameterized test suite *`TestSuiteName`* to be
uninstantiated.

By default, every [`TEST_P`](#TEST_P) call without a corresponding
[`INSTANTIATE_TEST_SUITE_P`](#INSTANTIATE_TEST_SUITE_P) call causes a failing
test in the test suite `GoogleTestVerification`.
`GTEST_ALLOW_UNINSTANTIATED_PARAMETERIZED_TEST` suppresses this failure for the
given test suite.

## Classes and types

GoogleTest defines the following classes and types to help with writing tests.

### AssertionResult {#AssertionResult}

`::testing::AssertionResult`

A class for indicating whether an assertion was successful.

When the assertion wasn't successful, the `AssertionResult` object stores a
non-empty failure message that can be retrieved with the object's `message()`
method.

To create an instance of this class, use one of the factory functions
[`AssertionSuccess()`](#AssertionSuccess) or
[`AssertionFailure()`](#AssertionFailure).

### AssertionException {#AssertionException}

`::testing::AssertionException`

Exception which can be thrown from
[`TestEventListener::OnTestPartResult`](#TestEventListener::OnTestPartResult).

### EmptyTestEventListener {#EmptyTestEventListener}

`::testing::EmptyTestEventListener`

Provides an empty implementation of all methods in the
[`TestEventListener`](#TestEventListener) interface, such that a subclass only
needs to override the methods it cares about.

### Environment {#Environment}

`::testing::Environment`

Represents a global test environment. See
[Global Set-Up and Tear-Down](../advanced.md#global-set-up-and-tear-down).

#### Protected Methods {#Environment-protected}

##### SetUp {#Environment::SetUp}

`virtual void Environment::SetUp()`

Override this to define how to set up the environment.

##### TearDown {#Environment::TearDown}

`virtual void Environment::TearDown()`

Override this to define how to tear down the environment.

### ScopedTrace {#ScopedTrace}

`::testing::ScopedTrace`

An instance of this class causes a trace to be included in every test failure
message generated by code in the scope of the lifetime of the `ScopedTrace`
instance. The effect is undone with the destruction of the instance.

The `ScopedTrace` constructor has the following form:

```cpp
template <typename T>
ScopedTrace(const char* file, int line, const T& message)
```

Example usage:

```cpp
::testing::ScopedTrace trace("file.cc", 123, "message");
```

The resulting trace includes the given source file path and line number, and the
given message. The `message` argument can be anything streamable to
`std::ostream`.

See also [`SCOPED_TRACE`](#SCOPED_TRACE).

### Test {#Test}

`::testing::Test`

The abstract class that all tests inherit from. `Test` is not copyable.

#### Public Methods {#Test-public}

##### SetUpTestSuite {#Test::SetUpTestSuite}

`static void Test::SetUpTestSuite()`

Performs shared setup for all tests in the test suite. GoogleTest calls
`SetUpTestSuite()` before running the first test in the test suite.

##### TearDownTestSuite {#Test::TearDownTestSuite}

`static void Test::TearDownTestSuite()`

Performs shared teardown for all tests in the test suite. GoogleTest calls
`TearDownTestSuite()` after running the last test in the test suite.

##### HasFatalFailure {#Test::HasFatalFailure}

`static bool Test::HasFatalFailure()`

Returns true if and only if the current test has a fatal failure.

##### HasNonfatalFailure {#Test::HasNonfatalFailure}

`static bool Test::HasNonfatalFailure()`

Returns true if and only if the current test has a nonfatal failure.

##### HasFailure {#Test::HasFailure}

`static bool Test::HasFailure()`

Returns true if and only if the current test has any failure, either fatal or
nonfatal.

##### IsSkipped {#Test::IsSkipped}

`static bool Test::IsSkipped()`

Returns true if and only if the current test was skipped.

##### RecordProperty {#Test::RecordProperty}

`static void Test::RecordProperty(const std::string& key, const std::string&
value)` \
`static void Test::RecordProperty(const std::string& key, int value)`

Logs a property for the current test, test suite, or entire invocation of the
test program. Only the last value for a given key is logged.

The key must be a valid XML attribute name, and cannot conflict with the ones
already used by GoogleTest (`name`, `file`, `line`, `status`, `time`,
`classname`, `type_param`, and `value_param`).

`RecordProperty` is `public static` so it can be called from utility functions
that are not members of the test fixture.

Calls to `RecordProperty` made during the lifespan of the test (from the moment
its constructor starts to the moment its destructor finishes) are output in XML
as attributes of the `<testcase>` element. Properties recorded from a fixture's
`SetUpTestSuite` or `TearDownTestSuite` methods are logged as attributes of the
corresponding `<testsuite>` element. Calls to `RecordProperty` made in the
global context (before or after invocation of `RUN_ALL_TESTS` or from the
`SetUp`/`TearDown` methods of registered `Environment` objects) are output as
attributes of the `<testsuites>` element.

#### Protected Methods {#Test-protected}

##### SetUp {#Test::SetUp}

`virtual void Test::SetUp()`

Override this to perform test fixture setup. GoogleTest calls `SetUp()` before
running each individual test.

##### TearDown {#Test::TearDown}

`virtual void Test::TearDown()`

Override this to perform test fixture teardown. GoogleTest calls `TearDown()`
after running each individual test.

### TestWithParam {#TestWithParam}

`::testing::TestWithParam<T>`

A convenience class which inherits from both [`Test`](#Test) and
[`WithParamInterface<T>`](#WithParamInterface).

### TestSuite {#TestSuite}

Represents a test suite. `TestSuite` is not copyable.

#### Public Methods {#TestSuite-public}

##### name {#TestSuite::name}

`const char* TestSuite::name() const`

Gets the name of the test suite.

##### type_param {#TestSuite::type_param}

`const char* TestSuite::type_param() const`

Returns the name of the parameter type, or `NULL` if this is not a typed or
type-parameterized test suite. See [Typed Tests](../advanced.md#typed-tests) and
[Type-Parameterized Tests](../advanced.md#type-parameterized-tests).

##### should_run {#TestSuite::should_run}

`bool TestSuite::should_run() const`

Returns true if any test in this test suite should run.

##### successful_test_count {#TestSuite::successful_test_count}

`int TestSuite::successful_test_count() const`

Gets the number of successful tests in this test suite.

##### skipped_test_count {#TestSuite::skipped_test_count}

`int TestSuite::skipped_test_count() const`

Gets the number of skipped tests in this test suite.

##### failed_test_count {#TestSuite::failed_test_count}

`int TestSuite::failed_test_count() const`

Gets the number of failed tests in this test suite.

##### reportable_disabled_test_count {#TestSuite::reportable_disabled_test_count}

`int TestSuite::reportable_disabled_test_count() const`

Gets the number of disabled tests that will be reported in the XML report.

##### disabled_test_count {#TestSuite::disabled_test_count}

`int TestSuite::disabled_test_count() const`

Gets the number of disabled tests in this test suite.

##### reportable_test_count {#TestSuite::reportable_test_count}

`int TestSuite::reportable_test_count() const`

Gets the number of tests to be printed in the XML report.

##### test_to_run_count {#TestSuite::test_to_run_count}

`int TestSuite::test_to_run_count() const`

Get the number of tests in this test suite that should run.

##### total_test_count {#TestSuite::total_test_count}

`int TestSuite::total_test_count() const`

Gets the number of all tests in this test suite.

##### Passed {#TestSuite::Passed}

`bool TestSuite::Passed() const`

Returns true if and only if the test suite passed.

##### Failed {#TestSuite::Failed}

`bool TestSuite::Failed() const`

Returns true if and only if the test suite failed.

##### elapsed_time {#TestSuite::elapsed_time}

`TimeInMillis TestSuite::elapsed_time() const`

Returns the elapsed time, in milliseconds.

##### start_timestamp {#TestSuite::start_timestamp}

`TimeInMillis TestSuite::start_timestamp() const`

Gets the time of the test suite start, in ms from the start of the UNIX epoch.

##### GetTestInfo {#TestSuite::GetTestInfo}

`const TestInfo* TestSuite::GetTestInfo(int i) const`

Returns the [`TestInfo`](#TestInfo) for the `i`-th test among all the tests. `i`
can range from 0 to `total_test_count() - 1`. If `i` is not in that range,
returns `NULL`.

##### ad_hoc_test_result {#TestSuite::ad_hoc_test_result}

`const TestResult& TestSuite::ad_hoc_test_result() const`

Returns the [`TestResult`](#TestResult) that holds test properties recorded
during execution of `SetUpTestSuite` and `TearDownTestSuite`.

### TestInfo {#TestInfo}

`::testing::TestInfo`

Stores information about a test.

#### Public Methods {#TestInfo-public}

##### test_suite_name {#TestInfo::test_suite_name}

`const char* TestInfo::test_suite_name() const`

Returns the test suite name.

##### name {#TestInfo::name}

`const char* TestInfo::name() const`

Returns the test name.

##### type_param {#TestInfo::type_param}

`const char* TestInfo::type_param() const`

Returns the name of the parameter type, or `NULL` if this is not a typed or
type-parameterized test. See [Typed Tests](../advanced.md#typed-tests) and
[Type-Parameterized Tests](../advanced.md#type-parameterized-tests).

##### value_param {#TestInfo::value_param}

`const char* TestInfo::value_param() const`

Returns the text representation of the value parameter, or `NULL` if this is not
a value-parameterized test. See
[Value-Parameterized Tests](../advanced.md#value-parameterized-tests).

##### file {#TestInfo::file}

`const char* TestInfo::file() const`

Returns the file name where this test is defined.

##### line {#TestInfo::line}

`int TestInfo::line() const`

Returns the line where this test is defined.

##### is_in_another_shard {#TestInfo::is_in_another_shard}

`bool TestInfo::is_in_another_shard() const`

Returns true if this test should not be run because it's in another shard.

##### should_run {#TestInfo::should_run}

`bool TestInfo::should_run() const`

Returns true if this test should run, that is if the test is not disabled (or it
is disabled but the `also_run_disabled_tests` flag has been specified) and its
full name matches the user-specified filter.

GoogleTest allows the user to filter the tests by their full names. Only the
tests that match the filter will run. See
[Running a Subset of the Tests](../advanced.md#running-a-subset-of-the-tests)
for more information.

##### is_reportable {#TestInfo::is_reportable}

`bool TestInfo::is_reportable() const`

Returns true if and only if this test will appear in the XML report.

##### result {#TestInfo::result}

`const TestResult* TestInfo::result() const`

Returns the result of the test. See [`TestResult`](#TestResult).

### TestParamInfo {#TestParamInfo}

`::testing::TestParamInfo<T>`

Describes a parameter to a value-parameterized test. The type `T` is the type of
the parameter.

Contains the fields `param` and `index` which hold the value of the parameter
and its integer index respectively.

### UnitTest {#UnitTest}

`::testing::UnitTest`

This class contains information about the test program.

`UnitTest` is a singleton class. The only instance is created when
`UnitTest::GetInstance()` is first called. This instance is never deleted.

`UnitTest` is not copyable.

#### Public Methods {#UnitTest-public}

##### GetInstance {#UnitTest::GetInstance}

`static UnitTest* UnitTest::GetInstance()`

Gets the singleton `UnitTest` object. The first time this method is called, a
`UnitTest` object is constructed and returned. Consecutive calls will return the
same object.

##### original_working_dir {#UnitTest::original_working_dir}

`const char* UnitTest::original_working_dir() const`

Returns the working directory when the first [`TEST()`](#TEST) or
[`TEST_F()`](#TEST_F) was executed. The `UnitTest` object owns the string.

##### current_test_suite {#UnitTest::current_test_suite}

`const TestSuite* UnitTest::current_test_suite() const`

Returns the [`TestSuite`](#TestSuite) object for the test that's currently
running, or `NULL` if no test is running.

##### current_test_info {#UnitTest::current_test_info}

`const TestInfo* UnitTest::current_test_info() const`

Returns the [`TestInfo`](#TestInfo) object for the test that's currently
running, or `NULL` if no test is running.

##### random_seed {#UnitTest::random_seed}

`int UnitTest::random_seed() const`

Returns the random seed used at the start of the current test run.

##### successful_test_suite_count {#UnitTest::successful_test_suite_count}

`int UnitTest::successful_test_suite_count() const`

Gets the number of successful test suites.

##### failed_test_suite_count {#UnitTest::failed_test_suite_count}

`int UnitTest::failed_test_suite_count() const`

Gets the number of failed test suites.

##### total_test_suite_count {#UnitTest::total_test_suite_count}

`int UnitTest::total_test_suite_count() const`

Gets the number of all test suites.

##### test_suite_to_run_count {#UnitTest::test_suite_to_run_count}

`int UnitTest::test_suite_to_run_count() const`

Gets the number of all test suites that contain at least one test that should
run.

##### successful_test_count {#UnitTest::successful_test_count}

`int UnitTest::successful_test_count() const`

Gets the number of successful tests.

##### skipped_test_count {#UnitTest::skipped_test_count}

`int UnitTest::skipped_test_count() const`

Gets the number of skipped tests.

##### failed_test_count {#UnitTest::failed_test_count}

`int UnitTest::failed_test_count() const`

Gets the number of failed tests.

##### reportable_disabled_test_count {#UnitTest::reportable_disabled_test_count}

`int UnitTest::reportable_disabled_test_count() const`

Gets the number of disabled tests that will be reported in the XML report.

##### disabled_test_count {#UnitTest::disabled_test_count}

`int UnitTest::disabled_test_count() const`

Gets the number of disabled tests.

##### reportable_test_count {#UnitTest::reportable_test_count}

`int UnitTest::reportable_test_count() const`

Gets the number of tests to be printed in the XML report.

##### total_test_count {#UnitTest::total_test_count}

`int UnitTest::total_test_count() const`

Gets the number of all tests.

##### test_to_run_count {#UnitTest::test_to_run_count}

`int UnitTest::test_to_run_count() const`

Gets the number of tests that should run.

##### start_timestamp {#UnitTest::start_timestamp}

`TimeInMillis UnitTest::start_timestamp() const`

Gets the time of the test program start, in ms from the start of the UNIX epoch.

##### elapsed_time {#UnitTest::elapsed_time}

`TimeInMillis UnitTest::elapsed_time() const`

Gets the elapsed time, in milliseconds.

##### Passed {#UnitTest::Passed}

`bool UnitTest::Passed() const`

Returns true if and only if the unit test passed (i.e. all test suites passed).

##### Failed {#UnitTest::Failed}

`bool UnitTest::Failed() const`

Returns true if and only if the unit test failed (i.e. some test suite failed or
something outside of all tests failed).

##### GetTestSuite {#UnitTest::GetTestSuite}

`const TestSuite* UnitTest::GetTestSuite(int i) const`

Gets the [`TestSuite`](#TestSuite) object for the `i`-th test suite among all
the test suites. `i` can range from 0 to `total_test_suite_count() - 1`. If `i`
is not in that range, returns `NULL`.

##### ad_hoc_test_result {#UnitTest::ad_hoc_test_result}

`const TestResult& UnitTest::ad_hoc_test_result() const`

Returns the [`TestResult`](#TestResult) containing information on test failures
and properties logged outside of individual test suites.

##### listeners {#UnitTest::listeners}

`TestEventListeners& UnitTest::listeners()`

Returns the list of event listeners that can be used to track events inside
GoogleTest. See [`TestEventListeners`](#TestEventListeners).

### TestEventListener {#TestEventListener}

`::testing::TestEventListener`

The interface for tracing execution of tests. The methods below are listed in
the order the corresponding events are fired.

#### Public Methods {#TestEventListener-public}

##### OnTestProgramStart {#TestEventListener::OnTestProgramStart}

`virtual void TestEventListener::OnTestProgramStart(const UnitTest& unit_test)`

Fired before any test activity starts.

##### OnTestIterationStart {#TestEventListener::OnTestIterationStart}

`virtual void TestEventListener::OnTestIterationStart(const UnitTest& unit_test,
int iteration)`

Fired before each iteration of tests starts. There may be more than one
iteration if `GTEST_FLAG(repeat)` is set. `iteration` is the iteration index,
starting from 0.

##### OnEnvironmentsSetUpStart {#TestEventListener::OnEnvironmentsSetUpStart}

`virtual void TestEventListener::OnEnvironmentsSetUpStart(const UnitTest&
unit_test)`

Fired before environment set-up for each iteration of tests starts.

##### OnEnvironmentsSetUpEnd {#TestEventListener::OnEnvironmentsSetUpEnd}

`virtual void TestEventListener::OnEnvironmentsSetUpEnd(const UnitTest&
unit_test)`

Fired after environment set-up for each iteration of tests ends.

##### OnTestSuiteStart {#TestEventListener::OnTestSuiteStart}

`virtual void TestEventListener::OnTestSuiteStart(const TestSuite& test_suite)`

Fired before the test suite starts.

##### OnTestStart {#TestEventListener::OnTestStart}

`virtual void TestEventListener::OnTestStart(const TestInfo& test_info)`

Fired before the test starts.

##### OnTestPartResult {#TestEventListener::OnTestPartResult}

`virtual void TestEventListener::OnTestPartResult(const TestPartResult&
test_part_result)`

Fired after a failed assertion or a `SUCCEED()` invocation. If you want to throw
an exception from this function to skip to the next test, it must be an
[`AssertionException`](#AssertionException) or inherited from it.

##### OnTestEnd {#TestEventListener::OnTestEnd}

`virtual void TestEventListener::OnTestEnd(const TestInfo& test_info)`

Fired after the test ends.

##### OnTestSuiteEnd {#TestEventListener::OnTestSuiteEnd}

`virtual void TestEventListener::OnTestSuiteEnd(const TestSuite& test_suite)`

Fired after the test suite ends.

##### OnEnvironmentsTearDownStart {#TestEventListener::OnEnvironmentsTearDownStart}

`virtual void TestEventListener::OnEnvironmentsTearDownStart(const UnitTest&
unit_test)`

Fired before environment tear-down for each iteration of tests starts.

##### OnEnvironmentsTearDownEnd {#TestEventListener::OnEnvironmentsTearDownEnd}

`virtual void TestEventListener::OnEnvironmentsTearDownEnd(const UnitTest&
unit_test)`

Fired after environment tear-down for each iteration of tests ends.

##### OnTestIterationEnd {#TestEventListener::OnTestIterationEnd}

`virtual void TestEventListener::OnTestIterationEnd(const UnitTest& unit_test,
int iteration)`

Fired after each iteration of tests finishes.

##### OnTestProgramEnd {#TestEventListener::OnTestProgramEnd}

`virtual void TestEventListener::OnTestProgramEnd(const UnitTest& unit_test)`

Fired after all test activities have ended.

### TestEventListeners {#TestEventListeners}

`::testing::TestEventListeners`

Lets users add listeners to track events in GoogleTest.

#### Public Methods {#TestEventListeners-public}

##### Append {#TestEventListeners::Append}

`void TestEventListeners::Append(TestEventListener* listener)`

Appends an event listener to the end of the list. GoogleTest assumes ownership
of the listener (i.e. it will delete the listener when the test program
finishes).

##### Release {#TestEventListeners::Release}

`TestEventListener* TestEventListeners::Release(TestEventListener* listener)`

Removes the given event listener from the list and returns it. It then becomes
the caller's responsibility to delete the listener. Returns `NULL` if the
listener is not found in the list.

##### default_result_printer {#TestEventListeners::default_result_printer}

`TestEventListener* TestEventListeners::default_result_printer() const`

Returns the standard listener responsible for the default console output. Can be
removed from the listeners list to shut down default console output. Note that
removing this object from the listener list with
[`Release()`](#TestEventListeners::Release) transfers its ownership to the
caller and makes this function return `NULL` the next time.

##### default_xml_generator {#TestEventListeners::default_xml_generator}

`TestEventListener* TestEventListeners::default_xml_generator() const`

Returns the standard listener responsible for the default XML output controlled
by the `--gtest_output=xml` flag. Can be removed from the listeners list by
users who want to shut down the default XML output controlled by this flag and
substitute it with custom one. Note that removing this object from the listener
list with [`Release()`](#TestEventListeners::Release) transfers its ownership to
the caller and makes this function return `NULL` the next time.

### TestPartResult {#TestPartResult}

`::testing::TestPartResult`

A copyable object representing the result of a test part (i.e. an assertion or
an explicit `FAIL()`, `ADD_FAILURE()`, or `SUCCESS()`).

#### Public Methods {#TestPartResult-public}

##### type {#TestPartResult::type}

`Type TestPartResult::type() const`

Gets the outcome of the test part.

The return type `Type` is an enum defined as follows:

```cpp
enum Type {
  kSuccess,          // Succeeded.
  kNonFatalFailure,  // Failed but the test can continue.
  kFatalFailure,     // Failed and the test should be terminated.
  kSkip              // Skipped.
};
```

##### file_name {#TestPartResult::file_name}

`const char* TestPartResult::file_name() const`

Gets the name of the source file where the test part took place, or `NULL` if
it's unknown.

##### line_number {#TestPartResult::line_number}

`int TestPartResult::line_number() const`

Gets the line in the source file where the test part took place, or `-1` if it's
unknown.

##### summary {#TestPartResult::summary}

`const char* TestPartResult::summary() const`

Gets the summary of the failure message.

##### message {#TestPartResult::message}

`const char* TestPartResult::message() const`

Gets the message associated with the test part.

##### skipped {#TestPartResult::skipped}

`bool TestPartResult::skipped() const`

Returns true if and only if the test part was skipped.

##### passed {#TestPartResult::passed}

`bool TestPartResult::passed() const`

Returns true if and only if the test part passed.

##### nonfatally_failed {#TestPartResult::nonfatally_failed}

`bool TestPartResult::nonfatally_failed() const`

Returns true if and only if the test part non-fatally failed.

##### fatally_failed {#TestPartResult::fatally_failed}

`bool TestPartResult::fatally_failed() const`

Returns true if and only if the test part fatally failed.

##### failed {#TestPartResult::failed}

`bool TestPartResult::failed() const`

Returns true if and only if the test part failed.

### TestProperty {#TestProperty}

`::testing::TestProperty`

A copyable object representing a user-specified test property which can be
output as a key/value string pair.

#### Public Methods {#TestProperty-public}

##### key {#key}

`const char* key() const`

Gets the user-supplied key.

##### value {#value}

`const char* value() const`

Gets the user-supplied value.

##### SetValue {#SetValue}

`void SetValue(const std::string& new_value)`

Sets a new value, overriding the previous one.

### TestResult {#TestResult}

`::testing::TestResult`

Contains information about the result of a single test.

`TestResult` is not copyable.

#### Public Methods {#TestResult-public}

##### total_part_count {#TestResult::total_part_count}

`int TestResult::total_part_count() const`

Gets the number of all test parts. This is the sum of the number of successful
test parts and the number of failed test parts.

##### test_property_count {#TestResult::test_property_count}

`int TestResult::test_property_count() const`

Returns the number of test properties.

##### Passed {#TestResult::Passed}

`bool TestResult::Passed() const`

Returns true if and only if the test passed (i.e. no test part failed).

##### Skipped {#TestResult::Skipped}

`bool TestResult::Skipped() const`

Returns true if and only if the test was skipped.

##### Failed {#TestResult::Failed}

`bool TestResult::Failed() const`

Returns true if and only if the test failed.

##### HasFatalFailure {#TestResult::HasFatalFailure}

`bool TestResult::HasFatalFailure() const`

Returns true if and only if the test fatally failed.

##### HasNonfatalFailure {#TestResult::HasNonfatalFailure}

`bool TestResult::HasNonfatalFailure() const`

Returns true if and only if the test has a non-fatal failure.

##### elapsed_time {#TestResult::elapsed_time}

`TimeInMillis TestResult::elapsed_time() const`

Returns the elapsed time, in milliseconds.

##### start_timestamp {#TestResult::start_timestamp}

`TimeInMillis TestResult::start_timestamp() const`

Gets the time of the test case start, in ms from the start of the UNIX epoch.

##### GetTestPartResult {#TestResult::GetTestPartResult}

`const TestPartResult& TestResult::GetTestPartResult(int i) const`

Returns the [`TestPartResult`](#TestPartResult) for the `i`-th test part result
among all the results. `i` can range from 0 to `total_part_count() - 1`. If `i`
is not in that range, aborts the program.

##### GetTestProperty {#TestResult::GetTestProperty}

`const TestProperty& TestResult::GetTestProperty(int i) const`

Returns the [`TestProperty`](#TestProperty) object for the `i`-th test property.
`i` can range from 0 to `test_property_count() - 1`. If `i` is not in that
range, aborts the program.

### TimeInMillis {#TimeInMillis}

`::testing::TimeInMillis`

An integer type representing time in milliseconds.

### Types {#Types}

`::testing::Types<T...>`

Represents a list of types for use in typed tests and type-parameterized tests.

The template argument `T...` can be any number of types, for example:

```
::testing::Types<char, int, unsigned int>
```

See [Typed Tests](../advanced.md#typed-tests) and
[Type-Parameterized Tests](../advanced.md#type-parameterized-tests) for more
information.

### WithParamInterface {#WithParamInterface}

`::testing::WithParamInterface<T>`

The pure interface class that all value-parameterized tests inherit from.

A value-parameterized test fixture class must inherit from both [`Test`](#Test)
and `WithParamInterface`. In most cases that just means inheriting from
[`TestWithParam`](#TestWithParam), but more complicated test hierarchies may
need to inherit from `Test` and `WithParamInterface` at different levels.

This interface defines the type alias `ParamType` for the parameter type `T` and
has support for accessing the test parameter value via the `GetParam()` method:

```
static const ParamType& GetParam()
```

For more information, see
[Value-Parameterized Tests](../advanced.md#value-parameterized-tests).

## Functions

GoogleTest defines the following functions to help with writing and running
tests.

### InitGoogleTest {#InitGoogleTest}

`void ::testing::InitGoogleTest(int* argc, char** argv)` \
`void ::testing::InitGoogleTest(int* argc, wchar_t** argv)` \
`void ::testing::InitGoogleTest()`

Initializes GoogleTest. This must be called before calling
[`RUN_ALL_TESTS()`](#RUN_ALL_TESTS). In particular, it parses the command line
for the flags that GoogleTest recognizes. Whenever a GoogleTest flag is seen, it
is removed from `argv`, and `*argc` is decremented.

No value is returned. Instead, the GoogleTest flag variables are updated.

The `InitGoogleTest(int* argc, wchar_t** argv)` overload can be used in Windows
programs compiled in `UNICODE` mode.

The argument-less `InitGoogleTest()` overload can be used on Arduino/embedded
platforms where there is no `argc`/`argv`.

### AddGlobalTestEnvironment {#AddGlobalTestEnvironment}

`Environment* ::testing::AddGlobalTestEnvironment(Environment* env)`

Adds a test environment to the test program. Must be called before
[`RUN_ALL_TESTS()`](#RUN_ALL_TESTS) is called. See
[Global Set-Up and Tear-Down](../advanced.md#global-set-up-and-tear-down) for
more information.

See also [`Environment`](#Environment).

### RegisterTest {#RegisterTest}

```cpp
template <typename Factory>
TestInfo* ::testing::RegisterTest(const char* test_suite_name, const char* test_name,
                                  const char* type_param, const char* value_param,
                                  const char* file, int line, Factory factory)
```

Dynamically registers a test with the framework.

The `factory` argument is a factory callable (move-constructible) object or
function pointer that creates a new instance of the `Test` object. It handles
ownership to the caller. The signature of the callable is `Fixture*()`, where
`Fixture` is the test fixture class for the test. All tests registered with the
same `test_suite_name` must return the same fixture type. This is checked at
runtime.

The framework will infer the fixture class from the factory and will call the
`SetUpTestSuite` and `TearDownTestSuite` methods for it.

Must be called before [`RUN_ALL_TESTS()`](#RUN_ALL_TESTS) is invoked, otherwise
behavior is undefined.

See
[Registering tests programmatically](../advanced.md#registering-tests-programmatically)
for more information.

### RUN_ALL_TESTS {#RUN_ALL_TESTS}

`int RUN_ALL_TESTS()`

Use this function in `main()` to run all tests. It returns `0` if all tests are
successful, or `1` otherwise.

`RUN_ALL_TESTS()` should be invoked after the command line has been parsed by
[`InitGoogleTest()`](#InitGoogleTest).

This function was formerly a macro; thus, it is in the global namespace and has
an all-caps name.

### AssertionSuccess {#AssertionSuccess}

`AssertionResult ::testing::AssertionSuccess()`

Creates a successful assertion result. See
[`AssertionResult`](#AssertionResult).

### AssertionFailure {#AssertionFailure}

`AssertionResult ::testing::AssertionFailure()`

Creates a failed assertion result. Use the `<<` operator to store a failure
message:

```cpp
::testing::AssertionFailure() << "My failure message";
```

See [`AssertionResult`](#AssertionResult).

### StaticAssertTypeEq {#StaticAssertTypeEq}

`::testing::StaticAssertTypeEq<T1, T2>()`

Compile-time assertion for type equality. Compiles if and only if `T1` and `T2`
are the same type. The value it returns is irrelevant.

See [Type Assertions](../advanced.md#type-assertions) for more information.

### PrintToString {#PrintToString}

`std::string ::testing::PrintToString(x)`

Prints any value `x` using GoogleTest's value printer.

See
[Teaching GoogleTest How to Print Your Values](../advanced.md#teaching-googletest-how-to-print-your-values)
for more information.

### PrintToStringParamName {#PrintToStringParamName}

`std::string ::testing::PrintToStringParamName(TestParamInfo<T>& info)`

A built-in parameterized test name generator which returns the result of
[`PrintToString`](#PrintToString) called on `info.param`. Does not work when the
test parameter is a `std::string` or C string. See
[Specifying Names for Value-Parameterized Test Parameters](../advanced.md#specifying-names-for-value-parameterized-test-parameters)
for more information.

See also [`TestParamInfo`](#TestParamInfo) and
[`INSTANTIATE_TEST_SUITE_P`](#INSTANTIATE_TEST_SUITE_P).
