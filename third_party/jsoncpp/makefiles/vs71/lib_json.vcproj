<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.10"
	Name="lib_json"
	ProjectGUID="{B84F7231-16CE-41D8-8C08-7B523FF4225B}"
	Keyword="Win32Proj">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="../../build/vs71/debug/lib_json"
			IntermediateDirectory="../../build/vs71/debug/lib_json"
			ConfigurationType="4"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="../../include"
				PreprocessorDefinitions="WIN32;_DEBUG;_LIB"
				StringPooling="TRUE"
				MinimalRebuild="TRUE"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				EnableFunctionLevelLinking="TRUE"
				DisableLanguageExtensions="TRUE"
				ForceConformanceInForLoopScope="FALSE"
				RuntimeTypeInfo="TRUE"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="TRUE"
				DebugInformationFormat="4"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)/json_vc71_libmtd.lib"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="../../build/vs71/release/lib_json"
			IntermediateDirectory="../../build/vs71/release/lib_json"
			ConfigurationType="4"
			CharacterSet="2"
			WholeProgramOptimization="TRUE">
			<Tool
				Name="VCCLCompilerTool"
				GlobalOptimizations="TRUE"
				EnableIntrinsicFunctions="TRUE"
				AdditionalIncludeDirectories="../../include"
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB"
				StringPooling="TRUE"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="TRUE"
				DisableLanguageExtensions="TRUE"
				ForceConformanceInForLoopScope="FALSE"
				RuntimeTypeInfo="TRUE"
				UsePrecompiledHeader="0"
				AssemblerOutput="4"
				WarningLevel="3"
				Detect64BitPortabilityProblems="TRUE"
				DebugInformationFormat="3"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)/json_vc71_libmt.lib"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="dummy|Win32"
			OutputDirectory="$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="2"
			CharacterSet="2"
			WholeProgramOptimization="TRUE">
			<Tool
				Name="VCCLCompilerTool"
				GlobalOptimizations="TRUE"
				EnableIntrinsicFunctions="TRUE"
				AdditionalIncludeDirectories="../../include"
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB"
				StringPooling="TRUE"
				RuntimeLibrary="4"
				EnableFunctionLevelLinking="TRUE"
				DisableLanguageExtensions="TRUE"
				ForceConformanceInForLoopScope="FALSE"
				RuntimeTypeInfo="TRUE"
				UsePrecompiledHeader="0"
				AssemblerOutput="4"
				WarningLevel="3"
				Detect64BitPortabilityProblems="TRUE"
				DebugInformationFormat="3"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				GenerateDebugInformation="TRUE"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<File
			RelativePath="..\..\include\json\autolink.h">
		</File>
		<File
			RelativePath="..\..\include\json\config.h">
		</File>
		<File
			RelativePath="..\..\include\json\features.h">
		</File>
		<File
			RelativePath="..\..\include\json\forwards.h">
		</File>
		<File
			RelativePath="..\..\include\json\json.h">
		</File>
		<File
			RelativePath="..\..\src\lib_json\json_reader.cpp">
		</File>
		<File
			RelativePath="..\..\src\lib_json\json_value.cpp">
		</File>
		<File
			RelativePath="..\..\src\lib_json\json_valueiterator.inl">
		</File>
		<File
			RelativePath="..\..\src\lib_json\json_writer.cpp">
		</File>
		<File
			RelativePath="..\..\include\json\reader.h">
		</File>
		<File
			RelativePath="..\..\include\json\value.h">
		</File>
		<File
			RelativePath="..\..\include\json\writer.h">
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
