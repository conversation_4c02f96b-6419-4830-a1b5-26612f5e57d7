{"Fuchsia x64": {"master": "chromium.linux", "port_name": "fuchsia", "specifiers": ["Fuchsia", "Release"]}, "Linux Tests": {"master": "chromium.linux", "port_name": "linux-trusty", "specifiers": ["Trusty", "Release"]}, "Linux Tests (dbg)(1)": {"master": "chromium.linux", "port_name": "linux-trusty", "specifiers": ["Trusty", "Debug"]}, "Mac10.12 Tests": {"master": "chromium.mac", "port_name": "mac-mac10.12", "specifiers": ["Mac10.12", "Release"]}, "Mac10.13 Tests": {"master": "chromium.mac", "port_name": "mac-mac10.13", "specifiers": ["Mac10.13", "Release"]}, "Mac10.13 Tests (dbg)": {"master": "chromium.mac", "port_name": "mac-mac10.13", "specifiers": ["Mac10.13", "Debug"]}, "Mac10.14 Tests": {"master": "chromium.mac", "port_name": "mac-mac10.14", "specifiers": ["Mac10.14", "Release"]}, "Mac10.15 Tests": {"master": "chromium.mac", "port_name": "mac-mac10.15", "specifiers": ["Mac10.15", "Release"]}, "Mac10.15 Tests (dbg)": {"master": "chromium.mac", "port_name": "mac-mac10.15", "specifiers": ["Mac10.15", "Debug"]}, "Mac11.0 Tests": {"master": "chromium.fyi", "port_name": "mac-mac11.0", "specifiers": ["Mac11.0", "Release"]}, "mac-arm64-rel-tests": {"master": "chromium.fyi", "port_name": "mac-mac-arm11.0", "specifiers": ["Mac-arm11.0", "Release"]}, "Win7 Tests (1)": {"master": "chromium.win", "port_name": "win-win7", "specifiers": ["Win7", "Release"]}, "Win10 Tests x64": {"master": "chromium.win", "port_name": "win-win10", "specifiers": ["Win10", "Release"]}, "Win7 Tests (dbg)(1)": {"master": "chromium.win", "port_name": "win-win7", "specifiers": ["Win7", "Debug"]}, "fuchsia_x64": {"master": "tryserver.chromium.linux", "port_name": "fuchsia", "specifiers": ["Fuchsia", "Release"], "is_try_builder": true}, "linux-wpt-identity-fyi-rel": {"master": "tryserver.chromium.linux", "port_name": "linux-trusty", "specifiers": ["Trusty", "Release"], "is_try_builder": true}, "linux-wpt-input-fyi-rel": {"master": "tryserver.chromium.linux", "port_name": "linux-trusty", "specifiers": ["Trusty", "Release"], "is_try_builder": true}, "linux-blink-rel": {"master": "tryserver.blink", "port_name": "linux-trusty", "specifiers": ["Trusty", "Release"], "has_webdriver_tests": true, "is_try_builder": true}, "mac10.12-blink-rel": {"master": "tryserver.blink", "port_name": "mac-mac10.12", "specifiers": ["Mac10.12", "Release"], "is_try_builder": true}, "mac10.13-blink-rel": {"master": "tryserver.blink", "port_name": "mac-mac10.13", "specifiers": ["Mac10.13", "Release"], "is_try_builder": true}, "mac10.14-blink-rel": {"master": "tryserver.blink", "port_name": "mac-mac10.14", "specifiers": ["Mac10.14", "Release"], "is_try_builder": true}, "mac10.15-blink-rel": {"master": "tryserver.blink", "port_name": "mac-mac10.15", "specifiers": ["Mac10.15", "Release"], "is_try_builder": true}, "mac11.0-blink-rel": {"master": "tryserver.blink", "port_name": "mac-mac11.0", "specifiers": ["Mac11.0", "Release"], "is_try_builder": true}, "win7-blink-rel": {"master": "tryserver.blink", "port_name": "win-win7", "specifiers": ["Win7", "Release"], "is_try_builder": true}, "win10-blink-rel": {"master": "tryserver.blink", "port_name": "win-win10", "specifiers": ["Win10", "Release"], "is_try_builder": true}, "linux-rel": {"master": "tryserver.chromium.linux", "port_name": "linux-trusty", "specifiers": ["Trusty", "Release"], "has_webdriver_tests": true, "is_try_builder": true, "is_cq_builder": true}, "mac-rel": {"master": "tryserver.chromium.mac", "port_name": "mac-mac10.13", "specifiers": ["Mac10.13", "Release"], "is_try_builder": true, "is_cq_builder": true}, "win10_chromium_x64_rel_ng": {"master": "tryserver.chromium.win", "port_name": "win-win10", "specifiers": ["Win10", "Release"], "is_try_builder": true, "is_cq_builder": true}, "android-pie-arm64-wpt-rel-non-cq": {"port_name": "android-android-pie", "specifiers": ["Android", "android_weblayer", "android_webview", "chrome_android", "Release"], "is_try_builder": true}}