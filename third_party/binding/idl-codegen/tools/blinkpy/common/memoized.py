# Copyright (c) 2010 Google Inc. All rights reserved.
#
# Redistribution and use in source and binary forms, with or without
# modification, are permitted provided that the following conditions are
# met:
#
#     * Redistributions of source code must retain the above copyright
# notice, this list of conditions and the following disclaimer.
#     * Redistributions in binary form must reproduce the above
# copyright notice, this list of conditions and the following disclaimer
# in the documentation and/or other materials provided with the
# distribution.
#     * Neither the name of Google Inc. nor the names of its
# contributors may be used to endorse or promote products derived from
# this software without specific prior written permission.
#
# THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
# "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
# LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
# A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
# OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
# SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
# LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
# DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
# THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
# (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
# OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

# Python does not (yet) seem to provide automatic memoization.  So we've
# written a small decorator to do so.

import functools


class memoized(object):
    def __init__(self, function):
        self._function = function
        self._results_cache = {}

    def __call__(self, *args):
        try:
            return self._results_cache[args]
        except KeyError:
            result = self._function(*args)
            self._results_cache[args] = result
            return result
        except TypeError as error:
            raise TypeError(
                'Cannot call memoized function %s with unhashable '
                'arguments: %s' % (self._function.__name__, error.message))

    # Use python "descriptor" protocol __get__ to appear
    # invisible during property access.
    def __get__(self, instance, owner):
        # Imagine we have a class, Foo, that has a @memoized method, bar(). So
        # that foo.bar() works we need to bind the underlying instance via
        # functools.partial, but we also want cache_clear() to work so we
        # monkey-patch it on top.
        wrapper = functools.partial(self.__call__, instance)
        wrapper.cache_clear = self.cache_clear
        return wrapper

    def cache_clear(self):
        self._results_cache = {}
