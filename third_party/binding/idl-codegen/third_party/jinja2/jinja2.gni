# DO NOT EDIT
# This is generated from get_jinja2.sh.
jinja2_sources = [
  "//third_party/jinja2/__init__.py",
  "//third_party/jinja2/_compat.py",
  "//third_party/jinja2/_identifier.py",
  "//third_party/jinja2/asyncfilters.py",
  "//third_party/jinja2/asyncsupport.py",
  "//third_party/jinja2/bccache.py",
  "//third_party/jinja2/compiler.py",
  "//third_party/jinja2/constants.py",
  "//third_party/jinja2/debug.py",
  "//third_party/jinja2/defaults.py",
  "//third_party/jinja2/environment.py",
  "//third_party/jinja2/exceptions.py",
  "//third_party/jinja2/ext.py",
  "//third_party/jinja2/filters.py",
  "//third_party/jinja2/idtracking.py",
  "//third_party/jinja2/lexer.py",
  "//third_party/jinja2/loaders.py",
  "//third_party/jinja2/meta.py",
  "//third_party/jinja2/nativetypes.py",
  "//third_party/jinja2/nodes.py",
  "//third_party/jinja2/optimizer.py",
  "//third_party/jinja2/parser.py",
  "//third_party/jinja2/runtime.py",
  "//third_party/jinja2/sandbox.py",
  "//third_party/jinja2/tests.py",
  "//third_party/jinja2/utils.py",
  "//third_party/jinja2/visitor.py",
]
