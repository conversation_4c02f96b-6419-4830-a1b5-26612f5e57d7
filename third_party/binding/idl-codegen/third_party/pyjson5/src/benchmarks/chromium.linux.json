{"Android Arm64 Builder (dbg)": {"additional_compile_targets": ["all"]}, "Android Builder": {"additional_compile_targets": ["cronet_test_instrumentation_apk"]}, "Android Clang Builder (dbg)": {"additional_compile_targets": ["all"]}, "Android Tests": {"gtest_tests": [{"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "base_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "base_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "blink_heap_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 180, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "blink_heap_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "boringssl_crypto_tests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "boringssl_crypto_tests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "boringssl_ssl_tests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "boringssl_ssl_tests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "breakpad_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "breakpad_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "capture_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "capture_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "cc_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "cc_unittests"}, {"args": ["--gs-results-bucket=chromium-result-details"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "chrome_public_test_apk"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 1500, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}], "shards": 12}, "test": "chrome_public_test_apk"}, {"args": ["--gs-results-bucket=chromium-result-details"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "chrome_sync_shell_test_apk"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 960, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "chrome_sync_shell_test_apk"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "components_browsertests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "components_browsertests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "components_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 900, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}], "shards": 2}, "test": "components_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "content_browsertests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 1500, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}], "shards": 8}, "test": "content_browsertests"}, {"args": ["--gs-results-bucket=chromium-result-details"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "content_shell_test_apk"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 960, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}], "shards": 3}, "test": "content_shell_test_apk"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "content_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 960, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "content_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "device_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "device_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "events_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "events_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "gl_tests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "gl_tests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "gl_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 60, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "gl_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "gpu_ipc_service_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "gpu_ipc_service_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "gpu_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "gpu_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "ipc_tests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "ipc_tests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "libjingle_xmpp_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "libjingle_xmpp_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "media_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "media_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "mojo_common_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 60, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "mojo_common_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "mojo_public_bindings_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 60, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "mojo_public_bindings_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "mojo_public_system_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 60, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "mojo_public_system_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "mojo_system_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 180, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "mojo_system_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "mojo_test_apk"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 300, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "mojo_test_apk"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "net_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 900, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}], "shards": 3}, "test": "net_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "sandbox_linux_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "sandbox_linux_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "services_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 120, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "services_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "sql_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "sql_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "storage_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 180, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "storage_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "ui_android_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "ui_android_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "ui_base_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "ui_base_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "ui_touch_selection_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "ui_touch_selection_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "unit_tests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 900, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "unit_tests"}, {"swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "viz_unittests"}], "junit_tests": [{"test": "base_junit_tests"}, {"test": "chrome_junit_tests"}, {"test": "components_background_task_scheduler_junit_tests"}, {"test": "components_gcm_driver_junit_tests"}, {"test": "components_invalidation_impl_junit_tests"}, {"test": "components_policy_junit_tests"}, {"test": "components_web_restrictions_junit_tests"}, {"test": "components_variations_junit_tests"}, {"test": "content_junit_tests"}, {"test": "device_junit_tests"}, {"test": "junit_unit_tests"}, {"test": "net_junit_tests"}, {"test": "services_junit_tests"}, {"test": "ui_junit_tests"}, {"test": "webapk_client_junit_tests"}, {"test": "webapk_shell_apk_junit_tests"}]}, "Android Tests (dbg)": {"gtest_tests": [{"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "base_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "base_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "blink_heap_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 180, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "blink_heap_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "boringssl_crypto_tests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "boringssl_crypto_tests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "boringssl_ssl_tests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "boringssl_ssl_tests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "breakpad_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "breakpad_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "capture_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "capture_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "cc_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "cc_unittests"}, {"args": ["--gs-results-bucket=chromium-result-details"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "chrome_public_test_apk"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 1500, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}], "shards": 20}, "test": "chrome_public_test_apk"}, {"args": ["--gs-results-bucket=chromium-result-details"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "chrome_sync_shell_test_apk"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 960, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}], "shards": 2}, "test": "chrome_sync_shell_test_apk"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "components_browsertests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "components_browsertests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "components_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 900, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}], "shards": 2}, "test": "components_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "content_browsertests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 1500, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}], "shards": 8}, "test": "content_browsertests"}, {"args": ["--gs-results-bucket=chromium-result-details"], "merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "content_shell_test_apk"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 960, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}], "shards": 3}, "test": "content_shell_test_apk"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "content_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 960, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "content_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "device_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "device_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "events_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "events_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "gl_tests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "gl_tests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "gl_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 60, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "gl_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "gpu_ipc_service_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "gpu_ipc_service_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "gpu_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "gpu_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "ipc_tests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "ipc_tests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "libjingle_xmpp_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "libjingle_xmpp_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "media_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "media_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "mojo_common_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 60, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "mojo_common_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "mojo_public_bindings_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 60, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "mojo_public_bindings_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "mojo_public_system_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 60, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "mojo_public_system_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "mojo_system_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 180, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "mojo_system_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "mojo_test_apk"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 300, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "mojo_test_apk"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "net_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 900, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}], "shards": 3}, "test": "net_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "sandbox_linux_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "sandbox_linux_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "services_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 120, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "services_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "sql_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "sql_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "storage_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 180, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "storage_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "ui_android_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "ui_android_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "ui_base_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "ui_base_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "ui_touch_selection_unittests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "ui_touch_selection_unittests"}, {"merge": {"args": ["--bucket", "chromium-result-details", "--test-name", "unit_tests"], "script": "//build/android/pylib/results/presentation/test_results_presentation.py"}, "swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "hard_timeout": 900, "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "unit_tests"}, {"swarming": {"can_use_on_swarming_builders": true, "cipd_packages": [{"cipd_package": "infra/tools/luci/logdog/butler/${platform}", "location": "bin", "revision": "git_revision:ff387eadf445b24c935f1cf7d6ddd279f8a6b04c"}], "dimension_sets": [{"android_devices": "1", "device_os": "KTU84P", "device_type": "hammerhead"}], "output_links": [{"link": ["https://luci-logdog.appspot.com/v/?s", "=android%2Fswarming%2Flogcats%2F", "${TASK_ID}%2F%2B%2Funified_logcats"], "name": "shard #${SHARD_INDEX} logcats"}]}, "test": "viz_unittests"}], "junit_tests": [{"test": "base_junit_tests"}, {"test": "chrome_junit_tests"}, {"test": "components_gcm_driver_junit_tests"}, {"test": "components_invalidation_impl_junit_tests"}, {"test": "components_policy_junit_tests"}, {"test": "components_web_restrictions_junit_tests"}, {"test": "components_variations_junit_tests"}, {"test": "content_junit_tests"}, {"test": "device_junit_tests"}, {"test": "junit_unit_tests"}, {"test": "net_junit_tests"}, {"test": "services_junit_tests"}, {"test": "ui_junit_tests"}, {"test": "webapk_client_junit_tests"}, {"test": "webapk_shell_apk_junit_tests"}]}, "Cast Android (dbg)": {"additional_compile_targets": ["cast_shell_apk"]}, "Cast Audio Linux": {"additional_compile_targets": ["cast_shell"], "gtest_tests": [{"swarming": {"can_use_on_swarming_builders": true}, "test": "base_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "boringssl_crypto_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "boringssl_ssl_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cacheinvalidation_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "capture_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cast_alsa_cma_backend_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cast_base_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cast_crash_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cast_media_unittests"}, {"args": ["--enable-local-file-accesses", "--ozone-platform=headless", "--no-sandbox", "--test-launcher-jobs=1"], "swarming": {"can_use_on_swarming_builders": false}, "test": "cast_shell_browsertests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cast_shell_unittests"}, {"args": ["--test-launcher-filter-file=src/testing/buildbot/filters/cast-linux.content_browsertests.filter"], "swarming": {"can_use_on_swarming_builders": false}, "test": "content_browsertests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "content_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "crypto_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gpu_ipc_service_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gpu_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "ipc_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "jingle_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "libjingle_xmpp_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "media_blink_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "media_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "midi_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "net_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "sandbox_linux_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "sql_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "storage_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "ui_base_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "url_unittests"}]}, "Cast Linux": {"additional_compile_targets": ["cast_shell"], "gtest_tests": [{"swarming": {"can_use_on_swarming_builders": true}, "test": "base_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "boringssl_crypto_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "boringssl_ssl_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cacheinvalidation_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "capture_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cast_alsa_cma_backend_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cast_base_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cast_crash_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cast_graphics_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cast_media_unittests"}, {"args": ["--enable-local-file-accesses", "--ozone-platform=cast", "--no-sandbox", "--test-launcher-jobs=1"], "swarming": {"can_use_on_swarming_builders": false}, "test": "cast_shell_browsertests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cast_shell_unittests"}, {"args": ["--test-launcher-filter-file=src/testing/buildbot/filters/cast-linux.content_browsertests.filter"], "swarming": {"can_use_on_swarming_builders": false}, "test": "content_browsertests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "content_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "crypto_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gpu_ipc_service_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gpu_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "ipc_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "jingle_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "libjingle_xmpp_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "media_blink_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "media_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "midi_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "net_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "sandbox_linux_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "sql_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "storage_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "ui_base_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "url_unittests"}]}, "Linux Builder": {"additional_compile_targets": ["all"]}, "Linux Builder (dbg)": {"additional_compile_targets": ["all"]}, "Linux Builder (dbg)(32)": {"additional_compile_targets": ["google_apis_unittests", "sync_integration_tests"]}, "Linux Tests": {"gtest_tests": [{"swarming": {"can_use_on_swarming_builders": true}, "test": "accessibility_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "app_shell_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "aura_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "base_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "battor_agent_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "blink_heap_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "blink_platform_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "boringssl_crypto_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "boringssl_ssl_tests"}, {"swarming": {"can_use_on_swarming_builders": true, "shards": 5}, "test": "browser_tests", "upload_to_flake_predictor": true}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_browser_tests", "swarming": {"can_use_on_swarming_builders": true, "shards": 5}, "test": "browser_tests"}, {"args": ["--site-per-process", "--test-launcher-filter-file=../../testing/buildbot/filters/site-per-process.browser_tests.filter"], "name": "site_per_process_browser_tests", "swarming": {"can_use_on_swarming_builders": true, "shards": 5}, "test": "browser_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cacheinvalidation_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "capture_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cast_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cc_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "chrome_app_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "chromedriver_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "components_browsertests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_components_browsertests", "swarming": {"can_use_on_swarming_builders": true}, "test": "components_browsertests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "components_unittests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_components_unittests", "swarming": {"can_use_on_swarming_builders": true}, "test": "components_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "compositor_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "content_browsertests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_content_browsertests", "swarming": {"can_use_on_swarming_builders": true}, "test": "content_browsertests"}, {"args": ["--site-per-process", "--test-launcher-filter-file=../../testing/buildbot/filters/site-per-process.content_browsertests.filter"], "name": "site_per_process_content_browsertests", "swarming": {"can_use_on_swarming_builders": true}, "test": "content_browsertests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "content_unittests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_content_unittests", "swarming": {"can_use_on_swarming_builders": true}, "test": "content_unittests"}, {"args": ["--site-per-process"], "name": "site_per_process_content_unittests", "swarming": {"can_use_on_swarming_builders": true}, "test": "content_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "crypto_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "dbus_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "device_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "display_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "events_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "extensions_browsertests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_extensions_browsertests", "swarming": {"can_use_on_swarming_builders": true}, "test": "extensions_browsertests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "extensions_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "filesystem_service_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gcm_unit_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gfx_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gin_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gn_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "google_apis_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gpu_ipc_service_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gpu_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "headless_browsertests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_headless_browsertests", "swarming": {"can_use_on_swarming_builders": true}, "test": "headless_browsertests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "headless_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "interactive_ui_tests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_interactive_ui_tests", "swarming": {"can_use_on_swarming_builders": true}, "test": "interactive_ui_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "ipc_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "jingle_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "leveldb_service_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "libjingle_xmpp_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "media_blink_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "media_service_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "media_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "midi_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "mojo_common_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "mojo_js_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "mojo_public_bindings_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "mojo_public_system_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "mojo_system_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "nacl_loader_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "native_theme_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "net_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "pdf_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "ppapi_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "printing_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "remoting_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "sandbox_linux_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "service_manager_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "services_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "skia_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "snapshot_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "sql_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "storage_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "sync_integration_tests"}, {"swarming": {"can_use_on_swarming_builders": false}, "test": "traffic_annotation_auditor_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "ui_base_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "ui_touch_selection_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "unit_tests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_unit_tests", "swarming": {"can_use_on_swarming_builders": true}, "test": "unit_tests"}, {"args": ["--site-per-process"], "name": "site_per_process_unit_tests", "swarming": {"can_use_on_swarming_builders": true}, "test": "unit_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "url_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "views_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "viz_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "webkit_unit_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "wm_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "wtf_unittests"}], "isolated_scripts": [{"isolate_name": "content_shell_crash_test", "name": "content_shell_crash_test", "swarming": {"can_use_on_swarming_builders": true}}, {"isolate_name": "devtools_closure_compile", "name": "devtools_closure_compile", "swarming": {"can_use_on_swarming_builders": true}}, {"isolate_name": "devtools_eslint", "name": "devtools_eslint", "swarming": {"can_use_on_swarming_builders": true}}, {"isolate_name": "telemetry_gpu_unittests", "name": "telemetry_gpu_unittests", "swarming": {"can_use_on_swarming_builders": true}}, {"args": ["--xvfb"], "isolate_name": "telemetry_perf_unittests", "name": "telemetry_perf_unittests", "swarming": {"can_use_on_swarming_builders": true, "hard_timeout": 960, "shards": 12}}, {"args": ["--jobs=1"], "isolate_name": "telemetry_unittests", "name": "telemetry_unittests", "swarming": {"can_use_on_swarming_builders": true, "shards": 4}}, {"isolate_name": "webkit_layout_tests_exparchive", "merge": {"args": ["--verbose"], "script": "//third_party/WebKit/Tools/Scripts/merge-layout-test-results"}, "name": "webkit_layout_tests", "results_handler": "layout tests", "swarming": {"can_use_on_swarming_builders": true, "dimension_sets": [{"os": "Ubuntu-14.04"}], "shards": 6}}], "scripts": [{"name": "checkdeps", "script": "checkdeps.py"}, {"name": "webkit_lint", "script": "webkit_lint.py"}, {"name": "webkit_python_tests", "script": "webkit_python_tests.py"}, {"name": "checkperms", "script": "checkperms.py"}]}, "Linux Tests (dbg)(1)": {"gtest_tests": [{"swarming": {"can_use_on_swarming_builders": true}, "test": "accessibility_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "app_shell_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "aura_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "base_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "battor_agent_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "boringssl_crypto_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "boringssl_ssl_tests"}, {"swarming": {"can_use_on_swarming_builders": true, "shards": 10}, "test": "browser_tests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_browser_tests", "swarming": {"can_use_on_swarming_builders": true, "shards": 10}, "test": "browser_tests"}, {"args": ["--site-per-process", "--test-launcher-filter-file=../../testing/buildbot/filters/site-per-process.browser_tests.filter"], "name": "site_per_process_browser_tests", "swarming": {"can_use_on_swarming_builders": true, "shards": 10}, "test": "browser_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cacheinvalidation_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "capture_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cast_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cc_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "chrome_app_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "chromedriver_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "components_browsertests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_components_browsertests", "swarming": {"can_use_on_swarming_builders": true}, "test": "components_browsertests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "components_unittests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_components_unittests", "swarming": {"can_use_on_swarming_builders": true}, "test": "components_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "compositor_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "content_browsertests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_content_browsertests", "swarming": {"can_use_on_swarming_builders": true}, "test": "content_browsertests"}, {"args": ["--site-per-process", "--test-launcher-filter-file=../../testing/buildbot/filters/site-per-process.content_browsertests.filter"], "name": "site_per_process_content_browsertests", "swarming": {"can_use_on_swarming_builders": true}, "test": "content_browsertests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "content_unittests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_content_unittests", "swarming": {"can_use_on_swarming_builders": true}, "test": "content_unittests"}, {"args": ["--site-per-process"], "name": "site_per_process_content_unittests", "swarming": {"can_use_on_swarming_builders": true}, "test": "content_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "crypto_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "dbus_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "device_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "display_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "events_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "extensions_browsertests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_extensions_browsertests", "swarming": {"can_use_on_swarming_builders": true}, "test": "extensions_browsertests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "extensions_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "filesystem_service_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gcm_unit_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gfx_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gin_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gn_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "google_apis_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gpu_ipc_service_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gpu_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "headless_browsertests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_headless_browsertests", "swarming": {"can_use_on_swarming_builders": true}, "test": "headless_browsertests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "headless_unittests"}, {"swarming": {"can_use_on_swarming_builders": true, "shards": 3}, "test": "interactive_ui_tests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_interactive_ui_tests", "swarming": {"can_use_on_swarming_builders": true, "shards": 3}, "test": "interactive_ui_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "ipc_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "jingle_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "leveldb_service_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "libjingle_xmpp_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "media_blink_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "media_service_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "media_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "midi_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "mojo_common_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "mojo_js_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "mojo_public_bindings_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "mojo_public_system_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "mojo_system_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "nacl_loader_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "native_theme_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "net_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "pdf_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "ppapi_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "printing_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "remoting_unittests"}, {"args": ["--test-launcher-print-test-stdio=always"], "swarming": {"can_use_on_swarming_builders": true}, "test": "sandbox_linux_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "service_manager_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "services_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "skia_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "snapshot_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "sql_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "storage_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "sync_integration_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "ui_base_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "ui_touch_selection_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "unit_tests"}, {"args": ["--enable-browser-side-navigation"], "name": "browser_side_navigation_unit_tests", "swarming": {"can_use_on_swarming_builders": true}, "test": "unit_tests"}, {"args": ["--site_per_process"], "name": "site_per_process_unit_tests", "swarming": {"can_use_on_swarming_builders": true}, "test": "unit_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "url_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "views_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "viz_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "wm_unittests"}], "isolated_scripts": [{"isolate_name": "telemetry_gpu_unittests", "name": "telemetry_gpu_unittests", "swarming": {"can_use_on_swarming_builders": true}}, {"args": ["--jobs=1"], "isolate_name": "telemetry_unittests", "name": "telemetry_unittests", "swarming": {"can_use_on_swarming_builders": true, "shards": 4}}]}, "Linux Tests (dbg)(1)(32)": {"gtest_tests": [{"swarming": {"can_use_on_swarming_builders": true}, "test": "accessibility_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "app_shell_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "aura_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "base_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "battor_agent_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "boringssl_crypto_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "boringssl_ssl_tests"}, {"swarming": {"can_use_on_swarming_builders": true, "shards": 10}, "test": "browser_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cacheinvalidation_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "capture_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cast_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "cc_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "chrome_app_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "chromedriver_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "components_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "compositor_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "content_browsertests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "content_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "crypto_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "device_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "display_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "events_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "extensions_browsertests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "extensions_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gcm_unit_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gfx_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gn_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "google_apis_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "gpu_unittests"}, {"swarming": {"can_use_on_swarming_builders": true, "shards": 3}, "test": "interactive_ui_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "ipc_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "jingle_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "leveldb_service_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "libjingle_xmpp_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "media_blink_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "media_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "midi_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "mojo_common_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "mojo_public_bindings_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "mojo_public_system_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "mojo_system_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "nacl_loader_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "net_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "pdf_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "ppapi_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "printing_unittests"}, {"args": ["--test-launcher-print-test-stdio=always"], "swarming": {"can_use_on_swarming_builders": true}, "test": "sandbox_linux_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "services_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "skia_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "snapshot_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "sql_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "storage_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "sync_integration_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "ui_base_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "ui_touch_selection_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "unit_tests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "url_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "viz_unittests"}, {"swarming": {"can_use_on_swarming_builders": true}, "test": "wm_unittests"}], "isolated_scripts": [{"isolate_name": "telemetry_gpu_unittests", "name": "telemetry_gpu_unittests", "swarming": {"can_use_on_swarming_builders": true}}, {"args": ["--jobs=1"], "isolate_name": "telemetry_unittests", "name": "telemetry_unittests", "swarming": {"can_use_on_swarming_builders": true, "shards": 4}}]}}