{"configs": {"android_cast_debug_static_bot": ["android", "cast", "clang", "debug_static_bot"], "android_clang_asan_errorprone_findbugs_debug_bot_minimal_symbols": ["android", "clang", "asan", "errorprone", "findbugs", "debug_bot", "minimal_symbols"], "android_clang_asan_errorprone_findbugs_debug_trybot": ["android", "clang", "asan", "errorprone", "findbugs", "debug_trybot"], "android_clang_tot_asan": ["android_without_codecs", "clang_tot", "asan", "shared", "debug", "minimal_symbols"], "android_clang_tot_dbg": ["android_without_codecs", "clang_tot", "shared", "debug"], "android_clang_tot_release": ["android_without_codecs", "clang_tot", "release"], "android_clang_tot_release_arm64": ["android_without_codecs", "clang_tot", "release", "arm64"], "android_clang_tot_x64": ["android_without_codecs", "clang_tot", "shared", "x64", "release", "dcheck_always_on"], "android_cronet_data_reduction_proxy_release_bot_minimal_symbols_arm_no_neon": ["android", "cronet", "release_bot", "minimal_symbols", "arm_no_neon"], "android_cronet_debug_static_bot_arm64": ["android", "cronet", "debug_static_bot", "arm64"], "android_cronet_debug_static_bot_arm_no_neon": ["android", "cronet", "debug_static_bot", "arm_no_neon"], "android_cronet_debug_static_bot_x86": ["android", "cronet", "debug_static_bot", "x86"], "android_cronet_release_bot_minimal_symbols_arm64": ["android", "cronet", "official_optimize", "release_bot", "minimal_symbols", "arm64"], "android_cronet_release_bot_minimal_symbols_arm_no_neon": ["android", "cronet", "official_optimize", "release_bot", "minimal_symbols", "arm_no_neon"], "android_cronet_release_bot_minimal_symbols_arm_no_neon_clang_asan": ["android", "cronet", "release_bot", "minimal_symbols", "arm_no_neon", "clang", "asan"], "android_cronet_release_bot_minimal_symbols_armv6": ["android", "cronet", "official_optimize", "release_bot", "minimal_symbols", "armv6"], "android_cronet_release_bot_minimal_symbols_mipsel": ["android", "cronet", "official_optimize", "release_bot", "minimal_symbols", "mipsel"], "android_cronet_release_bot_minimal_symbols_x86": ["android", "cronet", "official_optimize", "release_bot", "minimal_symbols", "x86"], "android_cronet_release_trybot_arm_no_neon": ["android", "cronet", "release_trybot", "arm_no_neon"], "android_debug_bot": ["android", "debug_bot"], "android_debug_static_bot": ["android", "debug_static_bot"], "android_debug_static_bot_arm64": ["android", "debug_static_bot", "arm64"], "android_debug_static_bot_mipsel": ["android", "debug_static_bot", "minimal_symbols", "mipsel"], "android_debug_static_bot_vrdata": ["android", "debug_static_bot", "include_vr_data"], "android_debug_static_bot_x64": ["android", "debug_static_bot", "x64"], "android_debug_static_bot_x86": ["android", "debug_static_bot", "x86"], "android_debug_trybot": ["android", "debug_trybot"], "android_debug_trybot_arm64": ["android", "debug_trybot", "arm64"], "android_debug_trybot_java_coverage": ["android", "debug_trybot", "java_coverage"], "android_debug_trybot_mipsel": ["android", "debug_trybot", "mipsel"], "android_debug_trybot_x64": ["android", "debug_trybot", "x64"], "android_debug_trybot_x86": ["android", "debug_trybot", "x86"], "android_ndk_next_release_bot_minimal_symbols": ["android", "ndk_next", "release_bot", "minimal_symbols"], "android_ndk_next_release_bot_minimal_symbols_arm64": ["android", "ndk_next", "release_bot", "minimal_symbols", "arm64"], "android_ndk_next_release_bot_minimal_symbols_mipsel": ["android", "ndk_next", "release_bot", "minimal_symbols", "mipsel"], "android_ndk_next_release_bot_minimal_symbols_x64": ["android", "ndk_next", "release_bot", "minimal_symbols", "x64"], "android_ndk_next_release_bot_minimal_symbols_x86": ["android", "ndk_next", "release_bot", "minimal_symbols", "x86"], "android_release_bot_minimal_symbols": ["android", "release_bot", "minimal_symbols"], "android_release_bot_minimal_symbols_arm64": ["android", "release_bot", "minimal_symbols", "arm64"], "android_release_thumb_bot": ["android", "release_bot", "arm_thumb"], "android_release_trybot": ["android", "release_trybot"], "android_release_trybot_arm64": ["android", "release_trybot", "arm64"], "android_shared_release_bot_x64": ["android_without_codecs", "shared_release_bot", "x64", "dcheck_always_on"], "android_without_codecs_release_bot_minimal_symbols": ["android_without_codecs", "release_bot", "minimal_symbols"], "asan_clang_edge_fuzzer_static_v8_heap_x86_full_symbols_release": ["asan", "clang_tot", "edge", "fuzzer", "static", "v8_heap", "full_symbols", "release", "x86"], "asan_clang_fuzzer_static_v8_heap_x86_full_symbols_release": ["asan", "clang_tot", "fuzzer", "static", "v8_heap", "full_symbols", "release", "x86"], "asan_clang_shared_v8_heap_x86_full_symbols_release": ["asan", "clang_tot", "shared", "v8_heap", "full_symbols", "release", "x86"], "asan_dcheck_disable_nacl_release_bot": ["asan", "dcheck_always_on", "disable_nacl", "release_bot"], "asan_disable_nacl_clang_tot_full_symbols_static_release": ["asan", "disable_nacl", "clang_tot", "full_symbols", "static", "release"], "asan_disable_nacl_edge_fuzzer_v8_heap_chrome_with_codecs_release_bot": ["asan", "disable_nacl", "edge", "fuzzer", "v8_heap", "chrome_with_codecs", "release_bot"], "asan_disable_nacl_edge_fuzzer_v8_heap_debug_symbols_static_bot": ["asan", "disable_nacl", "edge", "fuzzer", "v8_heap", "debug_symbols_static_bot"], "asan_disable_nacl_edge_fuzzer_v8_heap_release_bot": ["asan", "disable_nacl", "edge", "fuzzer", "v8_heap", "release_bot"], "asan_edge_fuzzer_v8_heap_chromeos_codecs_release_bot_hybrid": ["asan", "edge", "fuzzer", "v8_heap", "chromeos_codecs", "release_bot", "hybrid"], "asan_edge_fuzzer_v8_heap_release_bot_hybrid": ["asan", "edge", "fuzzer", "v8_heap", "release_bot", "hybrid"], "asan_edge_fuzzer_v8_heap_release_bot_x86": ["clang", "asan", "edge", "fuzzer", "v8_heap", "release_bot", "x86"], "asan_edge_v8_heap_debug_bot_hybrid": ["asan", "edge", "v8_heap", "debug_bot", "hybrid"], "asan_full_symbols_disable_nacl_release_bot_dcheck_always_on": ["asan", "full_symbols", "disable_nacl", "release_bot", "dcheck_always_on"], "asan_fuzzer_v8_heap_chrome_with_codecs_release_bot_x86": ["clang", "asan", "fuzzer", "v8_heap", "chrome_with_codecs", "release_bot", "x86"], "asan_fuzzer_v8_heap_release_bot_x86": ["clang", "asan", "fuzzer", "v8_heap", "release_bot", "x86"], "asan_lsan_chromeos_release_trybot": ["asan", "lsan", "chromeos", "release_trybot"], "asan_lsan_edge_debug_bot": ["asan", "lsan", "edge", "debug_bot"], "asan_lsan_edge_fuzzer_v8_heap_chromeos_codecs_release_bot": ["asan", "lsan", "edge", "v8_heap", "chromeos_codecs", "release_bot"], "asan_lsan_edge_fuzzer_v8_heap_release_bot": ["asan", "lsan", "edge", "fuzzer", "v8_heap", "release_bot"], "asan_lsan_release_bot": ["asan", "lsan", "release_bot"], "asan_lsan_release_trybot": ["asan", "lsan", "release_trybot"], "cast_audio_release_bot": ["cast", "cast_audio", "release_bot"], "cast_audio_release_trybot": ["cast", "cast_audio", "release_trybot"], "cast_release_bot": ["cast", "release_bot"], "cast_release_trybot": ["cast", "release_trybot"], "cfi_full_cfi_diag_recover_release_static": ["cfi_full", "cfi_diag", "cfi_recover", "release", "static"], "cfi_full_cfi_diag_thin_lto_release_static_dcheck_always_on_goma": ["cfi_full", "cfi_diag", "thin_lto", "release", "static", "dcheck_always_on", "goma"], "chromeos_asan_lsan_edge_fuzzer_v8_heap_release_bot": ["chromeos", "asan", "lsan", "edge", "fuzzer", "v8_heap", "release_bot"], "chromeos_msan_release_bot": ["chromeos", "msan", "release_bot"], "chromeos_with_codecs_debug_bot": ["chromeos_with_codecs", "debug_bot"], "chromeos_with_codecs_debug_trybot": ["chromeos_with_codecs", "debug_trybot"], "chromeos_with_codecs_release_bot": ["chromeos_with_codecs", "release_bot"], "chromeos_with_codecs_release_trybot": ["chromeos_with_codecs", "release_trybot"], "clang_debug_bot_minimal_symbols_x86": ["no_clang", "debug_bot", "minimal_symbols", "x86"], "clang_debug_trybot_x86": ["clang", "debug_trybot", "x86"], "clang_minimal_symbols_shared_release_bot_x86_dcheck": ["no_clang", "minimal_symbols", "shared_release_bot", "x86", "dcheck_always_on"], "clang_official_optimize_release_bot_minimal_symbols_x86": ["clang", "official_optimize", "release_bot", "minimal_symbols", "x86"], "clang_official_release_bot_minimal_symbols": ["no_clang", "official", "release_bot", "minimal_symbols"], "clang_official_release_bot_minimal_symbols_x86": ["no_clang", "official", "release_bot", "minimal_symbols", "x86"], "clang_official_release_trybot": ["clang", "official", "release_trybot"], "clang_official_release_trybot_x86": ["clang", "official", "release_trybot", "x86"], "clang_release_bot_minimal_symbols_x86": ["clang", "release_bot", "minimal_symbols", "x86"], "clang_shared_release_bot_dcheck": ["no_clang", "shared_release_bot", "dcheck_always_on"], "clang_tot_asan_lsan_static_release": ["clang_tot", "asan", "lsan", "static", "release"], "clang_tot_cfi_full_cfi_diag_thin_lto_release_static_dcheck_always_on": ["clang_tot", "cfi_full", "cfi_diag", "thin_lto", "release", "static", "dcheck_always_on"], "clang_tot_edge_ubsan_no_recover_hack_static_release": ["clang_tot", "edge", "ubsan_no_recover_hack", "static", "release"], "clang_tot_full_symbols_shared_debug_use_lld": ["clang_tot", "full_symbols", "shared", "debug", "use_lld"], "clang_tot_full_symbols_shared_debug_use_lld_x86": ["clang_tot", "full_symbols", "shared", "debug", "use_lld", "x86"], "clang_tot_linux_full_symbols_shared_release": ["clang_tot", "full_symbols", "shared", "release"], "clang_tot_lld_release_shared": ["clang_tot", "release", "shared", "use_lld"], "clang_tot_minimal_symbols_shared_release": ["clang_tot", "minimal_symbols", "shared", "release"], "clang_tot_minimal_symbols_shared_release_x86_dcheck": ["clang_tot", "minimal_symbols", "shared", "release", "x86", "dcheck_always_on"], "clang_tot_official_minimal_symbols_static_release": ["clang_tot", "official", "minimal_symbols", "static", "release"], "clang_tot_official_minimal_symbols_static_release_x86": ["clang_tot", "official", "minimal_symbols", "static", "release", "x86"], "clang_tot_official_static_use_lld_x86": ["clang_tot", "no_symbols", "official", "static", "release", "use_lld", "x86"], "clang_tot_shared_debug": ["clang_tot", "shared", "debug"], "clang_tot_shared_debug_x86": ["clang_tot", "shared", "debug", "x86"], "clang_tot_shared_release_dcheck": ["clang_tot", "shared", "release", "dcheck_always_on"], "clang_tot_shared_release_use_lld_dcheck": ["clang_tot", "no_symbols", "shared", "release", "use_lld", "dcheck_always_on"], "closure_compilation": ["error"], "codesearch": ["error"], "codesearch_gen_chromium_android_bot": ["goma", "clang", "shared", "debug", "minimal_symbols", "arm", "android_without_codecs"], "codesearch_gen_chromium_chromiumos_bot": ["goma", "clang", "shared", "debug", "minimal_symbols", "x64", "chromeos"], "codesearch_gen_chromium_linux_bot": ["goma", "clang", "shared", "debug", "minimal_symbols", "x64"], "cros_chrome_sdk": ["cros_chrome_sdk"], "debug_bot": ["debug_bot"], "debug_bot_arm": ["debug_bot", "arm"], "debug_bot_arm64": ["debug_bot", "arm64"], "debug_bot_fuchsia": ["debug_bot", "fuchsia"], "debug_bot_minimal_symbols": ["debug_bot", "minimal_symbols"], "debug_bot_x86": ["debug_bot", "x86"], "debug_bot_x86_minimal_symbols": ["debug_bot", "x86", "minimal_symbols"], "debug_libfuzzer_asan": ["debug", "libfuzzer", "asan", "chromeos_codecs", "pdf_xfa", "disable_nacl"], "debug_trybot": ["debug_trybot"], "debug_trybot_arm": ["debug_trybot", "arm"], "debug_trybot_arm64": ["debug_trybot", "arm64"], "debug_trybot_x86": ["debug_trybot", "x86"], "debug_trybot_x86_minimal_symbols": ["debug_trybot", "x86"], "gn_linux_upload": ["gn_linux_upload", "official", "goma"], "gpu_fyi_tests_chromeos_cros_release_trybot": ["gpu_fyi_tests", "chromeos", "release_trybot"], "gpu_fyi_tests_debug_trybot": ["gpu_fyi_tests", "debug_trybot"], "gpu_fyi_tests_ozone_linux_system_gbm_libdrm_release_trybot": ["gpu_fyi_tests", "ozone_linux", "system_gbm_libdrm", "release_trybot"], "gpu_fyi_tests_release_trybot": ["gpu_fyi_tests", "release_trybot"], "gpu_fyi_tests_release_trybot_asan": ["gpu_fyi_tests", "release_trybot", "asan", "full_symbols", "disable_nacl"], "gpu_fyi_tests_release_trybot_tsan": ["gpu_fyi_tests", "release_trybot", "tsan", "full_symbols", "disable_nacl"], "gpu_fyi_tests_win_clang_debug_bot": ["gpu_tests", "internal_gles2_conform_tests", "clang", "debug_bot", "minimal_symbols"], "gpu_tests_debug_trybot": ["gpu_tests", "debug_trybot"], "gpu_tests_debug_trybot_x86_minimal_symbols": ["gpu_tests", "debug_trybot", "x86", "minimal_symbols"], "gpu_tests_deqp_android_release_trybot_arm64": ["gpu_tests", "angle_deqp_tests", "android", "release_trybot", "arm64"], "gpu_tests_deqp_gles_debug_trybot": ["gpu_fyi_tests", "debug_trybot"], "gpu_tests_deqp_gles_debug_trybot_x86": ["gpu_fyi_tests", "debug_trybot", "x86"], "gpu_tests_deqp_gles_release_trybot": ["gpu_fyi_tests", "release_trybot", "no_clang"], "gpu_tests_deqp_gles_release_trybot_x86": ["gpu_fyi_tests", "release_trybot", "x86", "no_clang"], "gpu_tests_release_bot": ["gpu_tests", "release_bot"], "gpu_tests_release_trybot": ["gpu_tests", "release_trybot"], "gpu_tests_release_trybot_cm": ["gpu_tests", "release_trybot", "v8_concurrent_marking"], "gpu_tests_release_trybot_x86_minimal_symbols": ["gpu_tests", "release_trybot", "x86", "minimal_symbols"], "headless_linux_debug_bot": ["debug_bot", "headless"], "headless_linux_release_trybot": ["release_trybot", "headless"], "ios": ["error"], "linux_chromium_analysis": ["analysis"], "mac_views_browser_release_bot": ["mac_views_browser", "release_bot"], "mac_views_browser_release_trybot": ["mac_views_browser", "release_trybot"], "msan_edge_release_bot": ["msan", "edge", "release_bot"], "msan_no_origins_edge_release_bot": ["msan_no_origins", "edge", "release_bot"], "msan_release_bot": ["msan", "release_bot"], "official_goma": ["official", "goma"], "official_goma_chromeos": ["official", "goma", "chromeos"], "official_goma_minimal_symbols_android": ["official", "goma", "minimal_symbols", "android"], "official_goma_minimal_symbols_android_arm64": ["official", "goma", "minimal_symbols", "android", "arm64"], "official_goma_minimal_symbols_clang": ["official", "goma", "minimal_symbols", "clang"], "official_goma_perf": ["official", "goma", "no_gnome_keyring"], "official_goma_x86": ["official", "goma", "x86"], "official_optimize_chrome_pgo_phase_1": ["official_optimize", "chrome_pgo_phase_1"], "official_optimize_chrome_pgo_phase_1_x86": ["official_optimize", "chrome_pgo_phase_1", "x86"], "official_optimize_chrome_pgo_phase_2": ["official_optimize", "chrome_pgo_phase_2"], "official_optimize_chrome_pgo_phase_2_x86": ["official_optimize", "chrome_pgo_phase_2", "x86"], "ozone_linux_release_bot": ["ozone_linux", "release_bot"], "ozone_linux_release_trybot": ["ozone_linux", "release_trybot"], "presubmit": ["error"], "release_afl_asan": ["release", "afl", "asan", "chromeos_codecs", "pdf_xfa", "disable_nacl"], "release_bot": ["release_bot"], "release_bot_arm": ["release_bot", "arm"], "release_bot_arm64": ["release_bot", "arm64"], "release_bot_chrome_with_codecs": ["release_bot", "chrome_with_codecs"], "release_bot_fuchsia": ["release_bot", "fuchsia"], "release_bot_mac_new_sdk": ["release_bot", "mac_new_sdk"], "release_bot_mac_strip": ["release_bot", "mac_strip"], "release_bot_minimal_symbols": ["release_bot", "minimal_symbols"], "release_bot_out_of_process_profiling": ["release_bot", "out_of_process_profiling"], "release_bot_x86": ["release_bot", "x86"], "release_bot_x86_minimal_symbols": ["release_bot", "x86", "minimal_symbols"], "release_libfuzzer_asan": ["release", "libfuzzer", "asan", "chromeos_codecs", "pdf_xfa", "disable_nacl", "optimize_for_fuzzing"], "release_libfuzzer_asan_clang_tot": ["clang_tot", "release", "libfuzzer", "asan", "chromeos_codecs", "pdf_xfa", "disable_nacl"], "release_libfuzzer_mac_asan": ["release", "libfuzzer", "asan", "chrome_with_codecs", "pdf_xfa", "disable_nacl", "optimize_for_fuzzing"], "release_libfuzzer_msan": ["release", "libfuzzer", "msan", "chromeos_codecs", "pdf_xfa", "disable_nacl", "optimize_for_fuzzing"], "release_libfuzzer_ubsan": ["release", "libfuzzer", "ubsan_security", "chromeos_codecs", "pdf_xfa", "disable_nacl", "optimize_for_fuzzing"], "release_trybot": ["release_trybot"], "release_trybot_arm": ["release_trybot", "arm"], "release_trybot_arm64": ["release_trybot", "arm64"], "release_trybot_fuchsia": ["release_trybot", "fuchsia"], "release_trybot_minimal_symbols": ["release_trybot", "minimal_symbols"], "release_trybot_x86": ["release_trybot", "x86"], "shared_release_bot_x86": ["shared_release_bot", "x86"], "syzyasan_no_pch_release_x86": ["syzyasan", "no_pch", "release", "x86"], "thin_lto_clang_tot_full_symbols_release_static_use_lld": ["thin_lto", "clang_tot", "full_symbols", "release", "static", "use_lld"], "tsan_disable_nacl_debug_bot": ["tsan", "disable_nacl", "debug_bot"], "tsan_disable_nacl_release_bot": ["tsan", "disable_nacl", "release_bot"], "tsan_disable_nacl_release_trybot": ["tsan", "disable_nacl", "release_trybot"], "ubsan_release_bot": ["u<PERSON>an", "release_bot"], "ubsan_vptr_edge_release_bot": ["ubsan_vptr", "edge", "release_bot"], "ubsan_vptr_release_bot": ["ubsan_vptr", "ubsan_no_recover_hack", "release_bot"], "ubsan_vptr_release_trybot": ["ubsan_vptr", "ubsan_no_recover_hack", "release_trybot"], "v8_future_debug_bot": ["v8_future", "debug_bot"], "v8_future_release_bot": ["v8_future", "release_bot"], "win_clang_debug_bot": ["no_clang", "debug_bot", "minimal_symbols"], "win_clang_release_bot": ["no_clang", "release_bot", "minimal_symbols"], "windows_analyze": ["no_symbols", "no_pch", "shared", "x86", "win_analyze"], "windows_chromium_analysis": ["analysis"]}, "luci_tryservers": {"chromium.try": ["LUCI linux_chromium_rel_ng"]}, "masters": {"chromeos.chrome": {"Chrome4CROS Packages": "chromeos_with_codecs_release_bot", "Linux ChromeOS Buildspec Tests": "chromeos_with_codecs_release_bot", "chrome-tot-chromeos-amd64-generic": "cros_chrome_sdk"}, "chromium": {"Android": "android_without_codecs_release_bot_minimal_symbols", "Linux x64": "release_bot", "Mac": "release_bot_mac_strip", "Win": "release_bot_x86_minimal_symbols", "Win x64": "release_bot_minimal_symbols"}, "chromium.android": {"Android Cronet ARM64 Builder": "android_cronet_release_bot_minimal_symbols_arm64", "Android Cronet ARM64 Builder (dbg)": "android_cronet_debug_static_bot_arm64", "Android Cronet ARMv6 Builder": "android_cronet_release_bot_minimal_symbols_armv6", "Android Cronet Builder": "android_cronet_release_bot_minimal_symbols_arm_no_neon", "Android Cronet Builder (dbg)": "android_cronet_debug_static_bot_arm_no_neon", "Android Cronet Builder Asan": "android_cronet_release_bot_minimal_symbols_arm_no_neon_clang_asan", "Android Cronet Data Reduction Proxy Builder": "android_cronet_data_reduction_proxy_release_bot_minimal_symbols_arm_no_neon", "Android Cronet KitKat Builder": "android_cronet_release_bot_minimal_symbols_arm_no_neon", "Android Cronet Lollipop Builder": "android_cronet_release_bot_minimal_symbols_arm_no_neon", "Android Cronet MIPS Builder": "android_cronet_release_bot_minimal_symbols_mipsel", "Android Cronet Marshmallow 64bit Builder": "android_cronet_release_bot_minimal_symbols_arm64", "Android Cronet Marshmallow 64bit Perf": "android_cronet_release_bot_minimal_symbols_arm64", "Android Cronet x86 Builder": "android_cronet_release_bot_minimal_symbols_x86", "Android Cronet x86 Builder (dbg)": "android_cronet_debug_static_bot_x86", "Android MIPS Builder (dbg)": "android_debug_static_bot_mipsel", "Android N5X Swarm Builder": "android_release_bot_minimal_symbols_arm64", "Android arm Builder (dbg)": "android_debug_static_bot", "Android arm64 Builder (dbg)": "android_debug_static_bot_arm64", "Android x64 Builder (dbg)": "android_debug_static_bot_x64", "Android x86 Builder (dbg)": "android_debug_static_bot_x86"}, "chromium.android.fyi": {"Memory Infra Tester": "android_release_thumb_bot", "NDK Next MIPS Builder": "android_ndk_next_release_bot_minimal_symbols_mipsel", "NDK Next arm Builder": "android_ndk_next_release_bot_minimal_symbols", "NDK Next arm64 Builder": "android_ndk_next_release_bot_minimal_symbols_arm64", "NDK Next x64 Builder": "android_ndk_next_release_bot_minimal_symbols_x64", "NDK Next x86 Builder": "android_ndk_next_release_bot_minimal_symbols_x86", "x64 Device Tester": "android_shared_release_bot_x64", "x86 Cloud Tester": "android_debug_static_bot_x86", "x86 Emulator Tester": "android_debug_static_bot_x86"}, "chromium.chrome": {"Google Chrome ChromeOS": "official_goma_chromeos", "Google Chrome Linux x64": "official_goma", "Google Chrome Mac": "official_goma", "Google Chrome Win": "official_goma_x86"}, "chromium.chromedriver": {"Linux": "release_bot", "Linux32": "release_bot_x86", "Mac 10.6": "release_bot", "Win7": "release_bot_x86_minimal_symbols"}, "chromium.chromiumos": {"ChromiumOS amd64-generic Compile": "cros_chrome_sdk", "ChromiumOS daisy Compile": "cros_chrome_sdk", "Linux ChromiumOS Builder": "chromeos_with_codecs_release_bot", "Linux ChromiumOS Builder (dbg)": "chromeos_with_codecs_debug_bot", "Linux ChromiumOS Full": "chromeos_with_codecs_release_bot", "Linux ChromiumOS Ozone Builder": "chromeos_with_codecs_release_bot"}, "chromium.fyi": {"Afl Upload Linux ASan": "release_afl_asan", "Android Builder (dbg)": "android_debug_static_bot_vrdata", "Android Builder Goma Canary (dbg)": "android_debug_bot", "Android deterministic": "android_without_codecs_release_bot_minimal_symbols", "Android deterministic (dbg)": "android_debug_bot", "Browser Side Navigation Linux": "release_bot", "CFI Linux CF": "cfi_full_cfi_diag_recover_release_static", "CFI Linux ToT": "clang_tot_cfi_full_cfi_diag_thin_lto_release_static_dcheck_always_on", "ChromeOS amd64 Chromium Goma Canary": "cros_chrome_sdk", "Chromium Linux Goma Canary": "release_bot", "Chromium Linux Goma Canary (clobber)": "release_bot", "Chromium Linux Goma Canary LocalOutputCache": "release_bot", "Chromium Linux Precise Goma LinkTest": "release_bot", "Chromium Linux32 Goma Canary (clobber)": "release_bot_x86", "Chromium Mac 10.10 MacViews": "mac_views_browser_release_bot", "Chromium Mac 10.11": "release_bot", "Chromium Mac 10.11 Force Mac Toolchain": "release_bot_mac_new_sdk", "Chromium Mac 10.13": "release_bot", "Chromium Mac 10.9 Goma Canary": "release_bot", "Chromium Mac 10.9 Goma Canary (clobber)": "release_bot", "Chromium Mac 10.9 Goma Canary (dbg)": "debug_bot", "Chromium Mac 10.9 Goma Canary (dbg)(clobber)": "debug_bot", "Chromium Mac Goma Canary LocalOutputCache": "release_bot", "Chromium Win 10 GCE Tests": "release_bot_minimal_symbols", "Chromium Win PGO Builder": {"1": "official_optimize_chrome_pgo_phase_1_x86", "2": "official_optimize_chrome_pgo_phase_2_x86"}, "Chromium Win x64 PGO Builder": {"1": "official_optimize_chrome_pgo_phase_1", "2": "official_optimize_chrome_pgo_phase_2"}, "Chromium Windows Analyze": "windows_analyze", "ClangToTAndroid": "android_clang_tot_release", "ClangToTAndroid (dbg)": "android_clang_tot_dbg", "ClangToTAndroid x64": "android_clang_tot_x64", "ClangToTAndroid64": "android_clang_tot_release_arm64", "ClangToTAndroidASan": "android_clang_tot_asan", "ClangToTLinux": "clang_tot_linux_full_symbols_shared_release", "ClangToTLinux (dbg)": "clang_tot_shared_debug", "ClangToTLinuxASan": "clang_tot_asan_lsan_static_release", "ClangToTLinuxASanLibfuzzer": "release_libfuzzer_asan_clang_tot", "ClangToTLinuxLLD": "clang_tot_lld_release_shared", "ClangToTLinuxUBSanVptr": "clang_tot_edge_ubsan_no_recover_hack_static_release", "ClangToTMac": "clang_tot_minimal_symbols_shared_release", "ClangToTMac (dbg)": "clang_tot_shared_debug", "ClangToTMacASan": "asan_disable_nacl_clang_tot_full_symbols_static_release", "ClangToTWin": "clang_tot_official_minimal_symbols_static_release_x86", "ClangToTWin(dbg)": "clang_tot_shared_debug_x86", "ClangToTWin(dll)": "clang_tot_minimal_symbols_shared_release_x86_dcheck", "ClangToTWin64": "clang_tot_official_minimal_symbols_static_release", "ClangToTWin64(dbg)": "clang_tot_shared_debug", "ClangToTWin64(dll)": "clang_tot_shared_release_dcheck", "ClangToTiOS": "ios", "Closure Compilation Linux": "closure_compilation", "CrWin7Goma": "release_bot_x86_minimal_symbols", "CrWin7Goma(clbr)": "shared_release_bot_x86", "CrWin7Goma(dbg)": "debug_bot_x86_minimal_symbols", "CrWin7Goma(dll)": "shared_release_bot_x86", "CrWinAsan": "asan_clang_fuzzer_static_v8_heap_x86_full_symbols_release", "CrWinAsan(dll)": "asan_clang_shared_v8_heap_x86_full_symbols_release", "CrWinAsanCov": "asan_clang_edge_fuzzer_static_v8_heap_x86_full_symbols_release", "CrWinClang": "clang_official_release_bot_minimal_symbols_x86", "CrWinClang(dbg)": "clang_debug_bot_minimal_symbols_x86", "CrWinClang(shared)": "clang_minimal_symbols_shared_release_bot_x86_dcheck", "CrWinClang64": "clang_official_release_bot_minimal_symbols", "CrWinClang64(dbg)": "win_clang_debug_bot", "CrWinClang64(dll)": "clang_shared_release_bot_dcheck", "CrWinClangGoma": "clang_official_optimize_release_bot_minimal_symbols_x86", "CrWinClangLLD": "clang_tot_official_static_use_lld_x86", "CrWinClangLLD64": "clang_tot_shared_release_use_lld_dcheck", "CrWinClngLLD64dbg": "clang_tot_full_symbols_shared_debug_use_lld", "CrWinClngLLDdbg": "clang_tot_full_symbols_shared_debug_use_lld_x86", "CrWinGoma": "release_bot_x86_minimal_symbols", "CrWinGoma(dll)": "shared_release_bot_x86", "CrWinGoma(loc)": "shared_release_bot_x86", "EarlGreyiOS": "ios", "Fuchsia": "release_bot_fuchsia", "Fuchsia (dbg)": "debug_bot_fuchsia", "Fuchsia Compile": "release_bot_fuchsia", "GomaCanaryiOS": "ios", "Headless Linux (dbg)": "headless_linux_debug_bot", "Libfuzzer Upload Linux ASan": "release_libfuzzer_asan", "Libfuzzer Upload Linux ASan Debug": "debug_libfuzzer_asan", "Libfuzzer Upload Linux MSan": "release_libfuzzer_msan", "Libfuzzer Upload Linux UBSan": "release_libfuzzer_ubsan", "Libfuzzer Upload Mac ASan": "release_libfuzzer_<PERSON>_asan", "Linux ARM": "release_bot_arm", "Linux ARM (dbg)": "debug_bot_arm", "Linux ARM64": "release_bot_arm64", "Linux ARM64 (dbg)": "debug_bot_arm64", "Linux Clang Analyzer": "linux_chromium_analysis", "Linux deterministic": "release_bot", "Linux deterministic (dbg)": "debug_bot", "Linux remote_run Builder": "release_bot", "Linux remote_run Tester": "release_bot", "MD Top Chrome ChromeOS material-hybrid": "chromeos_with_codecs_debug_bot", "MD Top Chrome ChromeOS non-material": "chromeos_with_codecs_debug_bot", "MD Top Chrome Linux material": "debug_bot", "MD Top Chrome Win material": "debug_bot_minimal_symbols", "Mac deterministic": "release_bot_mac_strip", "Mac deterministic (dbg)": "debug_bot", "Mojo ChromiumOS": "chromeos_with_codecs_release_trybot", "Mojo Linux": "release_trybot", "Mojo Windows": "release_bot_x86_minimal_symbols", "Out of Process Profiling Android": "release_bot_out_of_process_profiling", "Out of Process Profiling Linux": "release_bot_out_of_process_profiling", "Out of Process Profiling Mac": "release_bot_out_of_process_profiling", "Out of Process Profiling Windows": "release_bot_out_of_process_profiling", "Ozone Linux": "ozone_linux_release_bot", "Site Isolation Android": "android_release_bot_minimal_symbols_arm64", "Site Isolation Linux": "release_trybot", "Site Isolation Win": "release_trybot_x86", "ThinLTO Linux ToT": "thin_lto_clang_tot_full_symbols_release_static_use_lld", "UBSanVptr Linux": "ubsan_vptr_release_bot", "WebKit Linux - RandomOrder": "release_trybot", "WebKit Mac - RandomOrder": "release_trybot", "WebKit Win - RandomOrder": "release_bot_x86_minimal_symbols", "Win 10 Fast Ring": "release_trybot", "Windows Clang Analyzer": "windows_chromium_analysis", "Windows Clang deterministic": "clang_release_bot_minimal_symbols_x86", "Windows deterministic": "release_bot_x86_minimal_symbols", "ios-simulator": "ios"}, "chromium.goma": {"Chromium Linux Goma GCE Staging": "release_bot", "Chromium Linux Goma Staging": "release_bot", "Chromium Mac Goma GCE Staging": "release_bot", "Chromium Mac Goma Staging": "release_bot", "CrWinGomaStaging": "release_bot_x86_minimal_symbols"}, "chromium.gpu": {"GPU Linux Builder": "gpu_tests_release_trybot", "GPU Linux Builder (dbg)": "gpu_tests_debug_trybot", "GPU Mac Builder": "gpu_tests_release_trybot", "GPU Mac Builder (dbg)": "gpu_tests_debug_trybot", "GPU Win Builder": "gpu_tests_release_trybot_x86_minimal_symbols", "GPU Win Builder (dbg)": "gpu_tests_debug_trybot_x86_minimal_symbols"}, "chromium.gpu.fyi": {"Android Release (NVIDIA Shield TV)": "android_release_trybot_arm64", "Android Release (Nexus 5)": "android_release_trybot", "Android Release (Nexus 5X)": "gpu_tests_deqp_android_release_trybot_arm64", "Android Release (Nexus 6)": "android_release_trybot", "Android Release (Nexus 6P)": "android_release_trybot_arm64", "Android Release (Nexus 9)": "android_release_trybot_arm64", "GPU Linux Builder": "gpu_fyi_tests_release_trybot", "GPU Linux Builder (dbg)": "gpu_fyi_tests_debug_trybot", "GPU Mac Builder": "gpu_fyi_tests_release_trybot", "GPU Mac Builder (dbg)": "gpu_fyi_tests_debug_trybot", "GPU Win Builder": "gpu_tests_deqp_gles_release_trybot_x86", "GPU Win Builder (dbg)": "gpu_tests_deqp_gles_debug_trybot_x86", "GPU Win Clang Builder (dbg)": "gpu_fyi_tests_win_clang_debug_bot", "GPU Win x64 Builder": "gpu_tests_deqp_gles_release_trybot", "GPU Win x64 Builder (dbg)": "gpu_tests_deqp_gles_debug_trybot", "Linux ChromiumOS Builder": "gpu_fyi_tests_chromeos_cros_release_trybot", "Linux ChromiumOS Ozone Builder": "gpu_fyi_tests_ozone_linux_system_gbm_libdrm_release_trybot", "Linux GPU TSAN Release": "gpu_fyi_tests_release_trybot_tsan", "Mac GPU ASAN Release": "gpu_fyi_tests_release_trybot_asan"}, "chromium.infra.codesearch": {"codesearch-gen-chromium-android": {"android": "codesearch_gen_chromium_android_bot"}, "codesearch-gen-chromium-chromiumos": {"chromeos": "codesearch_gen_chromium_chromiumos_bot", "linux": "codesearch_gen_chromium_linux_bot"}, "codesearch-gen-chromium-linux": {"linux": "codesearch_gen_chromium_linux_bot"}}, "chromium.linux": {"Android Arm64 Builder (dbg)": "android_debug_static_bot_arm64", "Android Builder": "android_release_bot_minimal_symbols", "Android Builder (dbg)": "android_debug_static_bot", "Android Clang Builder (dbg)": "android_clang_asan_errorprone_findbugs_debug_bot_minimal_symbols", "Android Tests": "android_release_bot_minimal_symbols", "Android Tests (dbg)": "android_debug_static_bot", "Cast Android (dbg)": "android_cast_debug_static_bot", "Cast Audio Linux": "cast_audio_release_bot", "Cast Linux": "cast_release_bot", "Deterministic Linux": "release_bot", "Linux Builder": "release_bot", "Linux Builder (dbg)": "debug_bot", "Linux Builder (dbg)(32)": "debug_bot_x86"}, "chromium.lkgr": {"ASAN Debug": "asan_lsan_edge_debug_bot", "ASAN Release": "asan_lsan_edge_fuzzer_v8_heap_release_bot", "ASAN Release Media": "asan_lsan_edge_fuzzer_v8_heap_chromeos_codecs_release_bot", "ASan Debug (32-bit x86 with V8-ARM)": "asan_edge_v8_heap_debug_bot_hybrid", "ASan Release (32-bit x86 with V8-ARM)": "asan_edge_fuzzer_v8_heap_release_bot_hybrid", "ASan Release Media (32-bit x86 with V8-ARM)": "asan_edge_fuzzer_v8_heap_chromeos_codecs_release_bot_hybrid", "ChromiumOS ASAN Release": "chromeos_asan_lsan_edge_fuzzer_v8_heap_release_bot", "MSAN Release (chained origins)": "msan_edge_release_bot", "MSAN Release (no origins)": "msan_no_origins_edge_release_bot", "Mac ASAN Debug": "asan_disable_nacl_edge_fuzzer_v8_heap_debug_symbols_static_bot", "Mac ASAN Release": "asan_disable_nacl_edge_fuzzer_v8_heap_release_bot", "Mac ASAN Release Media": "asan_disable_nacl_edge_fuzzer_v8_heap_chrome_with_codecs_release_bot", "TSAN Debug": "tsan_disable_nacl_debug_bot", "TSAN Release": "tsan_disable_nacl_release_bot", "UBSan Release": "ubsan_release_bot", "UBSan vptr Release": "ubsan_vptr_edge_release_bot", "Win ASan Release": "asan_fuzzer_v8_heap_release_bot_x86", "Win ASan Release Coverage": "asan_edge_fuzzer_v8_heap_release_bot_x86", "Win ASan Release Media": "asan_fuzzer_v8_heap_chrome_with_codecs_release_bot_x86", "Win SyzyASAN LKGR": "syzyasan_no_pch_release_x86"}, "chromium.mac": {"Mac Builder": "gpu_tests_release_bot", "Mac Builder (dbg)": "debug_bot", "ios-device": "ios", "ios-device-xcode-clang": "ios", "ios-simulator": "ios", "ios-simulator-cronet": "ios", "ios-simulator-eg": "ios", "ios-simulator-xcode-clang": "ios"}, "chromium.memory": {"Linux ASan LSan Builder": "asan_lsan_release_trybot", "Linux CFI": "cfi_full_cfi_diag_thin_lto_release_static_dcheck_always_on_goma", "Linux Chromium OS ASan LSan Builder": "asan_lsan_chromeos_release_trybot", "Linux ChromiumOS MSan Builder": "chromeos_msan_release_bot", "Linux MSan Builder": "msan_release_bot", "Linux TSan Builder": "tsan_disable_nacl_release_bot", "Mac ASan 64 Builder": "asan_full_symbols_disable_nacl_release_bot_dcheck_always_on"}, "chromium.perf": {"Android Builder": "official_goma_minimal_symbols_android", "Android Compile": "official_goma_minimal_symbols_android", "Android arm64 Builder": "official_goma_minimal_symbols_android_arm64", "Android arm64 Compile": "official_goma_minimal_symbols_android_arm64", "Linux Builder": "official_goma_perf", "Mac Builder": "official_goma", "Win Builder": "official_goma_x86", "Win x64 Builder": "official_goma"}, "chromium.perf.fyi": {"Android Builder FYI": "official_goma_minimal_symbols_android", "Android arm64 Builder FYI": "official_goma_minimal_symbols_android_arm64", "Battor Agent Linux": "official_goma_minimal_symbols_clang", "Battor Agent Mac": "official_goma_minimal_symbols_clang", "Battor Agent Win": "official_goma_minimal_symbols_clang", "Win Builder FYI": "official_goma", "Win Clang Builder": "official_goma_minimal_symbols_clang"}, "chromium.swarm": {"Android N5 Swarm": "android_release_bot_minimal_symbols", "Android N5X Swarm": "android_release_bot_minimal_symbols_arm64", "Linux Swarm": "release_bot", "Mac Swarm": "release_bot_mac_strip", "Windows Swarm": "release_bot_x86_minimal_symbols"}, "chromium.webkit": {"Android Builder": "android_release_bot_minimal_symbols", "WebKit Linux Trusty": "release_bot", "WebKit Linux Trusty (dbg)": "debug_bot", "WebKit Linux Trusty ASAN": "asan_lsan_release_bot", "WebKit Linux Trusty Leak": "release_bot", "WebKit Linux Trusty MSAN": "msan_release_bot", "WebKit Mac Builder": "release_bot", "WebKit Mac Builder (dbg)": "debug_bot", "WebKit Mac10.11 (retina)": "release_bot", "WebKit Mac10.12": "release_bot", "WebKit Mac10.9": "release_bot", "WebKit Win Builder": "release_bot_x86_minimal_symbols", "WebKit Win Builder (dbg)": "debug_bot_x86_minimal_symbols", "WebKit Win x64 Builder": "release_bot_minimal_symbols", "WebKit Win x64 Builder (dbg)": "debug_bot_minimal_symbols"}, "chromium.webrtc": {"Linux Builder": "release_bot_chrome_with_codecs", "Mac Builder": "release_bot_chrome_with_codecs", "Win Builder": "release_bot_x86_minimal_symbols"}, "chromium.webrtc.fyi": {"Android Builder": "android_release_bot_minimal_symbols", "Android Builder (dbg)": "android_debug_static_bot", "Android Builder ARM64 (dbg)": "android_debug_static_bot_arm64", "Linux Builder": "release_bot_chrome_with_codecs", "Linux Builder (dbg)": "debug_bot", "Mac Builder": "release_bot_chrome_with_codecs", "Mac Builder (dbg)": "debug_bot", "Win Builder": "release_bot_x86_minimal_symbols", "Win Builder (dbg)": "debug_bot_x86_minimal_symbols"}, "chromium.win": {"Win Builder": "release_bot_x86_minimal_symbols", "Win Builder (dbg)": "debug_bot_x86_minimal_symbols", "Win x64 Builder": "release_bot_minimal_symbols", "Win x64 Builder (dbg)": "debug_bot_minimal_symbols", "WinClang64 (dbg)": "win_clang_release_bot"}, "client.nacl.sdk": {"linux-sdk-asan-multi": "release_bot", "linux-sdk-multi": "release_bot", "linux-sdk-multirel": "release_bot", "mac-sdk-multi": "release_bot", "mac-sdk-multirel": "release_bot", "windows-sdk-multi": "release_bot_x86_minimal_symbols", "windows-sdk-multirel": "release_bot_x86_minimal_symbols"}, "client.v8.chromium": {"Linux - Future": "v8_future_release_bot", "Linux - Future (dbg)": "v8_future_debug_bot", "Linux V8 API Stability": "release_bot"}, "client.v8.fyi": {"Android Builder": "official_goma_minimal_symbols_android", "Android Release (Nexus 5X)": "gpu_tests_deqp_android_release_trybot_arm64", "Linux ASAN Builder": "asan_lsan_release_bot", "Linux Debug Builder": "debug_bot", "Linux Release (NVIDIA)": "gpu_tests_release_trybot", "Linux Release - concurrent marking (NVIDIA)": "gpu_tests_release_trybot_cm", "Linux Snapshot Builder": "release_bot", "Mac Release (Intel)": "gpu_tests_release_trybot", "V8 Android GN (dbg)": "android_debug_bot", "V8 Linux GN": "release_bot", "V8-Blink Linux 64": "release_bot", "V8-Blink Linux 64 (dbg)": "debug_bot", "V8-Blink Linux 64 - future": "release_bot", "V8-Blink Mac": "release_bot", "V8-Blink Win": "release_bot_x86_minimal_symbols", "Win Release (NVIDIA)": "gpu_tests_release_trybot_x86_minimal_symbols"}, "internal.client.kitchensync": {"Linux Canary": "debug_bot", "Linux Dev": "debug_bot", "Mac Dev": "debug_bot", "Windows Dev": "debug_bot_minimal_symbols"}, "tryserver.blink": {"linux_trusty_blink_compile_dbg": "debug_trybot", "linux_trusty_blink_compile_rel": "release_bot_minimal_symbols", "linux_trusty_blink_dbg": "debug_trybot", "linux_trusty_blink_rel": "release_bot_minimal_symbols", "mac10.10_blink_rel": "release_bot_minimal_symbols", "mac10.11_blink_rel": "release_bot_minimal_symbols", "mac10.11_retina_blink_rel": "release_bot_minimal_symbols", "mac10.12_blink_rel": "release_bot_minimal_symbols", "mac10.9_blink_compile_dbg": "debug_trybot", "mac10.9_blink_compile_rel": "release_bot_minimal_symbols", "mac10.9_blink_dbg": "debug_trybot", "mac10.9_blink_rel": "release_bot_minimal_symbols", "win10_blink_rel": "release_bot_x86_minimal_symbols", "win7_blink_compile_dbg": "debug_trybot_x86_minimal_symbols", "win7_blink_compile_rel": "release_bot_x86_minimal_symbols", "win7_blink_dbg": "debug_trybot_x86_minimal_symbols", "win7_blink_rel": "release_bot_x86_minimal_symbols"}, "tryserver.chromium.android": {"android_archive_rel_ng": "android_release_trybot", "android_arm64_dbg_recipe": "android_debug_trybot_arm64", "android_blink_rel": "android_release_trybot", "android_clang_dbg_recipe": "android_clang_asan_errorprone_findbugs_debug_trybot", "android_compile_dbg": "android_debug_trybot", "android_compile_mips_dbg": "android_debug_trybot_mipsel", "android_compile_rel": "android_release_trybot", "android_compile_x64_dbg": "android_debug_trybot_x64", "android_compile_x86_dbg": "android_debug_trybot_x86", "android_coverage": "android_debug_trybot_java_coverage", "android_cronet": "android_cronet_release_trybot_arm_no_neon", "android_cronet_tester": "android_cronet_debug_static_bot_arm_no_neon", "android_n5x_swarming_dbg": "android_debug_trybot_arm64", "android_n5x_swarming_rel": "android_release_trybot_arm64", "android_optional_gpu_tests_rel": "gpu_tests_deqp_android_release_trybot_arm64", "android_unswarmed_n5_rel": "android_release_trybot", "android_unswarmed_n5x_rel": "android_release_trybot_arm64", "cast_shell_android": "android_cast_debug_static_bot", "linux_android_dbg_ng": "android_debug_trybot", "linux_android_rel_ng": "android_release_trybot"}, "tryserver.chromium.angle": {"android_angle_rel_ng": "gpu_tests_deqp_android_release_trybot_arm64", "linux_angle_chromeos_rel_ng": "gpu_fyi_tests_chromeos_cros_release_trybot", "linux_angle_dbg_ng": "gpu_fyi_tests_debug_trybot", "linux_angle_rel_ng": "gpu_fyi_tests_release_trybot", "mac_angle_dbg_ng": "gpu_fyi_tests_debug_trybot", "mac_angle_rel_ng": "gpu_fyi_tests_release_trybot", "win_angle_dbg_ng": "gpu_tests_deqp_gles_debug_trybot_x86", "win_angle_rel_ng": "gpu_tests_deqp_gles_release_trybot_x86", "win_angle_x64_dbg_ng": "gpu_tests_deqp_gles_debug_trybot", "win_angle_x64_rel_ng": "gpu_tests_deqp_gles_release_trybot"}, "tryserver.chromium.linux": {"Chromium Linux Codesearch Builder": "codesearch", "ChromiumOS Codesearch Builder": "codesearch", "cast_shell_audio_linux": "cast_audio_release_trybot", "cast_shell_linux": "cast_release_trybot", "chromeos_amd64-generic_chromium_compile_only_ng": "cros_chrome_sdk", "chromeos_daisy_chromium_compile_only_ng": "cros_chrome_sdk", "chromium_presubmit": "presubmit", "closure_compilation": "closure_compilation", "fuchsia": "release_trybot_fuchsia", "fuchsia_compile": "release_trybot_fuchsia", "linux_arm": "release_trybot_arm", "linux_arm64_dbg": "debug_trybot_arm64", "linux_arm64_rel": "release_trybot_arm64", "linux_arm_dbg": "debug_trybot_arm", "linux_arm_rel": "release_trybot_arm", "linux_chromium_archive_rel_ng": "release_bot", "linux_chromium_asan_rel_ng": "asan_lsan_release_trybot", "linux_chromium_browser_side_navigation_rel": "release_trybot", "linux_chromium_cfi_rel_ng": "cfi_full_cfi_diag_thin_lto_release_static_dcheck_always_on_goma", "linux_chromium_chromeos_asan_rel_ng": "asan_lsan_chromeos_release_trybot", "linux_chromium_chromeos_compile_dbg_ng": "chromeos_with_codecs_debug_trybot", "linux_chromium_chromeos_compile_rel_ng": "chromeos_with_codecs_release_trybot", "linux_chromium_chromeos_dbg_ng": "chromeos_with_codecs_debug_trybot", "linux_chromium_chromeos_msan_rel_ng": "chromeos_msan_release_bot", "linux_chromium_chromeos_ozone_rel_ng": "chromeos_with_codecs_release_trybot", "linux_chromium_chromeos_rel_ng": "chromeos_with_codecs_release_trybot", "linux_chromium_clobber_deterministic": "release_trybot", "linux_chromium_clobber_rel_ng": "release_trybot", "linux_chromium_compile_dbg_32_ng": "debug_trybot_x86", "linux_chromium_compile_dbg_ng": "debug_trybot", "linux_chromium_compile_rel_ng": "release_trybot", "linux_chromium_dbg_32_ng": "debug_trybot_x86", "linux_chromium_dbg_ng": "debug_trybot", "linux_chromium_gn_upload": "gn_linux_upload", "linux_chromium_headless_rel": "headless_linux_release_trybot", "linux_chromium_msan_rel_ng": "msan_release_bot", "linux_chromium_ozone_compile_only_ng": "ozone_linux_release_trybot", "linux_chromium_ozone_ng": "ozone_linux_release_trybot", "linux_chromium_rel_ng": "gpu_tests_release_trybot", "linux_chromium_tsan_rel_ng": "tsan_disable_nacl_release_trybot", "linux_chromium_ubsan_rel_ng": "ubsan_vptr_release_trybot", "linux_layout_tests_layout_ng": "release_bot", "linux_layout_tests_slimming_paint_v2": "release_trybot", "linux_mojo": "release_trybot", "linux_mojo_chromeos": "release_trybot", "linux_nacl_sdk": "release_bot", "linux_nacl_sdk_build": "release_bot", "linux_optional_gpu_tests_rel": "gpu_fyi_tests_release_trybot", "linux_site_isolation": "release_trybot", "linux_upload_clang": "release_bot"}, "tryserver.chromium.mac": {"ios-device": "ios", "ios-device-xcode-clang": "ios", "ios-simulator": "ios", "ios-simulator-cronet": "ios", "ios-simulator-eg": "ios", "ios-simulator-xcode-clang": "ios", "mac_chromium_10.10_macviews": "mac_views_browser_release_trybot", "mac_chromium_10.12_rel_ng": "gpu_tests_release_trybot", "mac_chromium_archive_rel_ng": "release_bot_mac_strip", "mac_chromium_asan_rel_ng": "asan_dcheck_disable_nacl_release_bot", "mac_chromium_compile_dbg_ng": "debug_trybot", "mac_chromium_compile_rel_ng": "gpu_tests_release_trybot", "mac_chromium_dbg_ng": "debug_trybot", "mac_chromium_gn_upload": "release_bot", "mac_chromium_rel_ng": "gpu_tests_release_trybot", "mac_nacl_sdk": "release_bot", "mac_nacl_sdk_build": "release_bot", "mac_optional_gpu_tests_rel": "gpu_fyi_tests_release_trybot", "mac_upload_clang": "release_bot"}, "tryserver.chromium.perf": {"Mac Builder": "official_goma", "android_arm64_perf_bisect_builder": "official_goma_minimal_symbols_android_arm64", "android_fyi_perf_bisect": "official_goma_minimal_symbols_android", "android_nexus5X_perf_bisect": "official_goma_minimal_symbols_android", "android_nexus5_perf_bisect": "official_goma_minimal_symbols_android", "android_nexus6_perf_bisect": "official_goma_minimal_symbols_android", "android_nexus7_perf_bisect": "official_goma_minimal_symbols_android", "android_nexus9_perf_bisect": "official_goma_minimal_symbols_android_arm64", "android_one_perf_bisect": "official_goma_minimal_symbols_android", "android_perf_bisect_builder": "official_goma_minimal_symbols_android", "android_s5_perf_bisect": "official_goma_minimal_symbols_android", "android_webview_arm64_aosp_perf_bisect": "official_goma_minimal_symbols_android", "linux_fyi_perf_bisect": "official_goma", "linux_perf_bisect": "official_goma", "linux_perf_bisect_builder": "official_goma", "linux_perf_cq": "official_goma", "mac_10_10_perf_bisect": "official_goma", "mac_10_11_perf_bisect": "official_goma", "mac_fyi_perf_bisect": "official_goma", "mac_hdd_perf_bisect": "official_goma", "mac_perf_bisect_builder": "official_goma", "mac_retina_perf_bisect": "official_goma", "mac_retina_perf_cq": "official_goma", "win_8_perf_bisect": "official_goma_x86", "win_fyi_perf_bisect": "official_goma_x86", "win_perf_bisect": "official_goma_x86", "win_perf_bisect_builder": "official_goma_x86", "win_x64_perf_bisect": "official_goma", "winx64_10_perf_bisect": "official_goma", "winx64_10_perf_cq": "official_goma", "winx64_bisect_builder": "official_goma", "winx64_zen_perf_bisect": "official_goma", "winx64ati_perf_bisect": "official_goma", "winx64intel_perf_bisect": "official_goma", "winx64nvidia_perf_bisect": "official_goma"}, "tryserver.chromium.win": {"win10_chromium_x64_rel_ng": "release_trybot", "win10_chromium_x64_rel_ng_exp": "release_trybot", "win10_gce_x64_rel": "release_bot_minimal_symbols", "win8_chromium_gn_upload": "release_bot_x86_minimal_symbols", "win_archive": "release_trybot_x86", "win_chrome_official": "official_goma_x86", "win_chromium_compile_dbg_ng": "debug_trybot_x86_minimal_symbols", "win_chromium_compile_rel_ng": "gpu_tests_release_trybot_x86_minimal_symbols", "win_chromium_dbg_ng": "debug_trybot_x86_minimal_symbols", "win_chromium_rel_ng": "gpu_tests_release_trybot_x86_minimal_symbols", "win_chromium_syzyasan_rel": "syzyasan_no_pch_release_x86", "win_chromium_x64_rel_ng": "release_trybot_minimal_symbols", "win_clang": "win_clang_release_bot", "win_clang_dbg": "clang_debug_trybot_x86", "win_clang_rel": "clang_official_release_trybot_x86", "win_clang_x64_dbg": "win_clang_debug_bot", "win_clang_x64_rel": "clang_official_release_trybot", "win_mojo": "release_trybot_x86", "win_nacl_sdk": "release_bot_x86_minimal_symbols", "win_nacl_sdk_build": "release_bot_x86_minimal_symbols", "win_optional_gpu_tests_rel": "gpu_tests_deqp_gles_release_trybot_x86", "win_pgo": {"1": "official_optimize_chrome_pgo_phase_1_x86", "2": "official_optimize_chrome_pgo_phase_2_x86"}, "win_pgo_x64": {"1": "official_optimize_chrome_pgo_phase_1", "2": "official_optimize_chrome_pgo_phase_2"}, "win_upload_clang": "release_bot", "win_x64_archive": "release_trybot"}, "tryserver.v8": {"v8_android_chromium_gn_dbg": "android_debug_bot", "v8_linux_blink_rel": "release_trybot", "v8_linux_chromium_gn_rel": "release_trybot"}}, "mixins": {"afl": {"gn_args": "use_afl=true"}, "analysis": {"gn_args": "use_clang_static_analyzer=true"}, "android": {"mixins": ["android_without_codecs", "chrome_with_codecs"]}, "android_without_codecs": {"gn_args": "target_os=\"android\""}, "angle_deqp_tests": {"gn_args": "build_angle_deqp_tests=true"}, "arm": {"gn_args": "target_cpu=\"arm\""}, "arm64": {"gn_args": "target_cpu=\"arm64\""}, "arm_no_neon": {"gn_args": "arm_use_neon=false", "mixins": ["arm"]}, "arm_thumb": {"gn_args": "arm_use_thumb=true"}, "armv6": {"gn_args": "arm_version=6"}, "asan": {"gn_args": "is_asan=true"}, "cast": {"gn_args": "is_chromecast=true"}, "cast_audio": {"gn_args": "is_cast_audio_only=true enable_webrtc=false"}, "cfi_diag": {"gn_args": "use_cfi_diag=true"}, "cfi_full": {"gn_args": "is_cfi=true use_cfi_cast=true"}, "cfi_recover": {"gn_args": "use_cfi_recover=true"}, "chrome_pgo_phase_1": {"gn_args": "chrome_pgo_phase=1 is_clang=false"}, "chrome_pgo_phase_2": {"gn_args": "chrome_pgo_phase=2 is_clang=false"}, "chrome_with_codecs": {"mixins": ["ffmpeg_branding_chrome", "proprietary_codecs"]}, "chromeos": {"gn_args": "target_os=\"chromeos\""}, "chromeos_codecs": {"mixins": ["ffmpeg_branding_chromeos", "proprietary_codecs"]}, "chromeos_with_codecs": {"mixins": ["chromeos", "chromeos_codecs"]}, "clang": {"gn_args": "is_clang=true"}, "clang_tot": {"gn_args": "llvm_force_head_revision=true clang_use_chrome_plugins=false", "mixins": ["clang"]}, "cronet": {"gn_args": "disable_file_support=true disable_ftp_support=true enable_websockets=false use_platform_icu_alternatives=true use_partition_alloc=false enable_reporting=false include_transport_security_state_preload_list=false"}, "cros_chrome_sdk": {"cros_passthrough": true}, "dcheck_always_on": {"gn_args": "dcheck_always_on=true"}, "debug": {"gn_args": "is_debug=true"}, "debug_bot": {"mixins": ["debug", "shared", "goma"]}, "debug_static_bot": {"mixins": ["debug", "static", "minimal_symbols", "goma"]}, "debug_symbols_static_bot": {"mixins": ["debug", "static", "goma"]}, "debug_trybot": {"mixins": ["debug_bot", "minimal_symbols"]}, "disable_nacl": {"gn_args": "enable_nacl=false"}, "edge": {"gn_args": "sanitizer_coverage_flags=\"trace-pc-guard\""}, "error": {"gn_args": "error"}, "errorprone": {"gn_args": "use_errorprone_java_compiler=true"}, "ffmpeg_branding_chrome": {"gn_args": "ffmpeg_branding=\"Chrome\""}, "ffmpeg_branding_chromeos": {"gn_args": "ffmpeg_branding=\"ChromeOS\""}, "findbugs": {"gn_args": "run_findbugs=true"}, "fuchsia": {"gn_args": "target_os=\"fuchsia\""}, "full_symbols": {"gn_args": "symbol_level=2"}, "fuzzer": {"gn_args": "enable_ipc_fuzzer=true"}, "gn_linux_upload": {"gn_args": "use_ozone=true"}, "goma": {"gn_args": "use_goma=true strip_absolute_paths_from_debug_symbols=true"}, "gpu_fyi_tests": {"mixins": ["gpu_tests", "internal_gles2_conform_tests", "angle_deqp_tests"]}, "gpu_tests": {"mixins": ["chrome_with_codecs"]}, "headless": {"args_file": "//build/args/headless.gn"}, "hybrid": {"gn_args": "v8_target_cpu=\"arm\" target_cpu=\"x86\"", "mixins": ["disable_nacl"]}, "include_vr_data": {"gn_args": "include_vr_data=true"}, "internal_gles2_conform_tests": {"gn_args": "internal_gles2_conform_tests=true"}, "java_coverage": {"gn_args": "emma_coverage=true emma_filter=\"org.chromium.*\""}, "libfuzzer": {"gn_args": "use_libfuzzer=true"}, "lsan": {"gn_args": "is_lsan=true"}, "lto": {"gn_args": "allow_posix_link_time_opt=true"}, "mac_new_sdk": {"gn_args": "mac_sdk_min=\"10.12\""}, "mac_strip": {"gn_args": "enable_stripping=true"}, "mac_views_browser": {"gn_args": "mac_views_browser=true"}, "minimal_symbols": {"gn_args": "symbol_level=1"}, "mipsel": {"gn_args": "target_cpu=\"mipsel\""}, "msan": {"gn_args": "is_msan=true msan_track_origins=2 use_prebuilt_instrumented_libraries=true"}, "msan_no_origins": {"gn_args": "is_msan=true msan_track_origins=0 use_prebuilt_instrumented_libraries=true"}, "ndk_next": {"gn_args": "android_ndk_version=\"r13b\" android_ndk_major_version=13", "mixins": ["clang"]}, "no_clang": {"gn_args": "is_clang=false"}, "no_gnome_keyring": {"gn_args": "use_gnome_keyring=false"}, "no_pch": {"gn_args": "enable_precompiled_headers=false"}, "no_symbols": {"gn_args": "symbol_level=0"}, "official": {"gn_args": "is_chrome_branded=true", "mixins": ["official_optimize"]}, "official_optimize": {"gn_args": "is_official_build=true is_debug=false"}, "optimize_for_fuzzing": {"gn_args": "optimize_for_fuzzing=true"}, "out_of_process_profiling": {"gn_args": "enable_oop_heap_profiling=true"}, "ozone_linux": {"gn_args": "ozone_auto_platforms=false ozone_platform_wayland=true ozone_platform=\"x11\" ozone_platform_x11=true ozone_platform_gbm=true enable_package_mash_services=true use_ash=false use_xkbcommon=true use_ozone=true"}, "pdf_xfa": {"gn_args": "pdf_enable_xfa=true"}, "proprietary_codecs": {"gn_args": "proprietary_codecs=true"}, "release": {"gn_args": "is_debug=false"}, "release_bot": {"mixins": ["release", "static", "goma"]}, "release_trybot": {"mixins": ["release_bot", "minimal_symbols", "dcheck_always_on"]}, "shared": {"gn_args": "is_component_build=true"}, "shared_release_bot": {"mixins": ["shared", "release", "goma"]}, "static": {"gn_args": "is_component_build=false"}, "system_gbm_libdrm": {"gn_args": "use_system_libdrm=true use_system_minigbm=true"}, "syzyasan": {"gn_args": "is_syzyasan=true is_clang=false"}, "thin_lto": {"gn_args": "use_thin_lto=true", "mixins": ["lto"]}, "tsan": {"gn_args": "is_tsan=true"}, "ubsan": {"gn_args": "is_ubsan=true"}, "ubsan_no_recover_hack": {"gn_args": "is_ubsan_no_recover=true", "mixins": ["ubsan_vptr"]}, "ubsan_security": {"gn_args": "is_ubsan_security=true"}, "ubsan_vptr": {"gn_args": "is_ubsan_vptr=true"}, "use_lld": {"gn_args": "use_lld=true"}, "v8_concurrent_marking": {"gn_args": "v8_enable_concurrent_marking=true"}, "v8_future": {"gn_args": "v8_enable_future=true"}, "v8_heap": {"gn_args": "v8_enable_verify_heap=true"}, "win_analyze": {"gn_args": "use_vs_code_analysis=true"}, "x64": {"gn_args": "target_cpu=\"x64\""}, "x86": {"gn_args": "target_cpu=\"x86\""}}}