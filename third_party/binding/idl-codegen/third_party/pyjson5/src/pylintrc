# Copyright 2014 Google Inc. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

[MASTER]

# <PERSON><PERSON> collected data for later comparisons.
persistent=yes

[MESSAGES CONTROL]

disable=
    broad-except,
    global-statement,
    locally-disabled,
    missing-docstring,
    no-self-use,
    too-many-arguments,
    too-few-public-methods,
    too-many-branches,
    too-many-instance-attributes,
    too-many-locals,
    too-many-public-methods,
    too-many-return-statements,
    unidiomatic-typecheck,

[REPORTS]

reports=no

[BASIC]

# By default, pylint wants method names to be at most 31 chars long,
# but we want to allow up to 49 to allow for longer test names.
method-rgx=[a-zA-Z_][a-zA-Z0-9_]{0,48}$

# By default, pylint only allows UPPER_CASE constants, but we want to
# allow snake_case as well in some situations.
const-rgx=[a-zA-Z_][a-zA-Z0-9_]{0,30}$

# By default, pylint wants all parameter names to be at least two chars long,
# but we want to allow single-char parameter names as well.
argument-rgx=[a-z_][a-z0-9_]{0,30}$

# By default, pylint wants all variable names to be at least two chars long,
# but we want to allow single-char variable names as well.
variable-rgx=[a-z_][a-z0-9_]{0,30}$
