[egg_info]
tag_build = 
tag_date = 0

[tool:pytest]
addopts = --tb native -v -r fxX -W error
python_files = test/*test_*.py

[upload]
sign = 1
identity = C4DAFEE1

[flake8]
show-source = true
enable-extensions = G
ignore = 
	A003,
	D,
	E203,E305,E711,E712,E721,E722,E741,
	N801,N802,N806,
	RST304,RST303,RST299,RST399,
	W503,W504
exclude = .venv,.git,.tox,dist,docs/*,*egg,build
import-order-style = google
application-import-names = mako,test

[bdist_wheel]
universal = 1

