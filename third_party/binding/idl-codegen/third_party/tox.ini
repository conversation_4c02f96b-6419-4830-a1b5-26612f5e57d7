[tox]
envlist = py

[testenv]
cov_args=--cov=mako --cov-report term --cov-report xml

deps=pytest>=3.1.0
     mock
     beaker
     markupsafe
     pygments
     babel
     dogpile.cache
     lingua<4
     cov: pytest-cov

setenv=
    cov: COVERAGE={[testenv]cov_args}

commands=py.test {env:COVERAGE:} {posargs}


[testenv:pep8]
basepython = python3
deps=
      flake8
      flake8-import-order
      flake8-builtins
      flake8-docstrings
      flake8-rst-docstrings
      pydocstyle<4.0.0
      # used by flake8-rst-docstrings
      pygments
commands = flake8 ./mako/ ./test/ setup.py --exclude test/templates,test/foo  {posargs}
