// Copyright 2017 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

/* Test Namespace productions

Run with --test to generate an AST and verify that all comments accurately
reflect the state of the Nodes.

TREE
Type(Name)
  Type(Name)
  Type(Name)
    Type(Name)
    ...
This comment signals that a tree of nodes matching the BUILD comment
symatics should exist.  This is an exact match.
*/


/** TREE
 *Namespace(MyNamespace)
 */
namespace MyNamespace { };

/** TREE
 *Namespace(MyNamespace2)
 *  Operation(fooLong)
 *    Arguments()
 *    Type()
 *      PrimitiveType(long)
 *  Operation(voidArgLong)
 *    Arguments()
 *      Argument(arg)
 *        Type()
 *          PrimitiveType(long)
 *    Type()
 *      PrimitiveType(void)
 */
namespace MyNamespace2 {
  long fooLong();
  void voidArgLong(long arg);
};

/** TREE
 *Namespace(MyNamespaceMissingArgument)
 *  Operation(foo)
 *    Arguments()
 *      Argument(arg)
 *        Type()
 *          StringType(DOMString)
 *      Error(Missing argument.)
 *    Type()
 *      PrimitiveType(void)
 */
namespace MyNamespaceMissingArgument {
  void foo(DOMString arg, );
};

/** TREE
 *Namespace(VectorUtils)
 *  Attribute(unit)
 *    READONLY: True
 *    Type()
 *      Typeref(Vector)
 *  Operation(dotProduct)
 *    Arguments()
 *      Argument(x)
 *        Type()
 *          Typeref(Vector)
 *      Argument(y)
 *        Type()
 *          Typeref(Vector)
 *    Type()
 *      PrimitiveType(double)
 *  Operation(crossProduct)
 *    Arguments()
 *      Argument(x)
 *        Type()
 *          Typeref(Vector)
 *      Argument(y)
 *        Type()
 *          Typeref(Vector)
 *    Type()
 *      Typeref(Vector)
 */
namespace VectorUtils {
  readonly attribute Vector unit;
  double dotProduct(Vector x, Vector y);
  Vector crossProduct(Vector x, Vector y);
};

/**TREE
 *Namespace(ErrorOnlyExtAttrs)
 *  Error(Unexpected ";" after "]".)
 */
namespace ErrorOnlyExtAttrs {
  [Clamp];
};

/** TREE
 *Error(Unexpected keyword "attribute" after "{".)
 */
namespace ErrorNonReadonly {
  attribute Vector unit2;
};

/** TREE
 *Error(Unexpected ";" after "{".)
 */
namespace NameSpaceError {
  ;

/**TREE
 *Namespace(PartialNamespace)
 *  PARTIAL: True
 *  Operation(fooLong)
 *    Arguments()
 *    Type()
 *      PrimitiveType(long)
 */
partial namespace PartialNamespace {
  long fooLong();
};

/** TREE
 *Namespace(NamespaceWithExtAttrs)
 *  Operation(fooLong)
 *    Arguments()
 *    Type()
 *      PrimitiveType(long)
 *  ExtAttributes()
 *    ExtAttribute(Replaceable)
 */
[Replaceable] namespace NamespaceWithExtAttrs {
  long fooLong();
};

/** TREE
 *Namespace(NamespaceAnnotatedTypeMember)
 *  Operation(fooLong)
 *    Arguments()
 *    Type()
 *      PrimitiveType(long)
 *      ExtAttributes()
 *        ExtAttribute(Clamp)
 */
namespace NamespaceAnnotatedTypeMember {
  [Clamp] long fooLong();
};
