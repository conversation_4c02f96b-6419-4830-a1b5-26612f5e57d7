ANY any
ATTRIBUTE attribute
BOOLEAN boolean
BYTESTRING ByteString
BYTE byte
CALLBACK callback
CONST const
CONSTRUCTOR constructor
<PERSON><PERSON><PERSON>R deleter
DICTIONARY dictionary
DOMSTRING DOMString
DOUBLE double
ENUM enum
FALSE false
FLOAT float
GETTER getter
INFINITY Infinity
INHERIT inherit
INTERFACE interface
ITERABLE iterable
LONG long
MAPLIKE maplike
MIXIN mixin
NAN NaN
NULL null
OBJECT object
OCTET octet
OPTIONAL optional
OR or
PARTIAL partial
PROMISE Promise
READONLY readonly
RECORD record
REQUIRED required
SEQUENCE sequence
SETLIKE setlike
SETTER setter
SHORT short
STATIC static
STRINGIFIER stringifier
TYPEDEF typedef
TRUE true
UNSIGNED unsigned
UNRESTRICTED unrestricted
USVSTRING USVString
VOID void
