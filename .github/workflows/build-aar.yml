name: Build AAR

on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'release tag'
        required: true
        type: string

jobs:
  build:
    runs-on: lynx-ubuntu-22.04-medium

    steps:
    - name: Checkout repository
      uses: actions/checkout@v2
    - name: Set up JDK 11
      uses: actions/setup-java@v1
      with:
        java-version: 11

    - name: Cache Gradle packages
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: <PERSON> execute permission for gradlew
      run: cd Android &&  chmod +x ./gradlew

    - name: Build the AAR
      run: cd Android && ./gradlew :app:assemblerelease

    - name: push to release
      uses: ncipollo/release-action@v1
      with:
        tag: ${{ github.event.inputs.tag }}
        token: ${{ secrets.GITHUB_TOKEN }}
        artifacts: "Android/app/build/outputs/aar/primjs-release.aar"