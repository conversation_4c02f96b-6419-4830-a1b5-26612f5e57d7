name: bazel

on:
  push: {}
  pull_request: {}

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v1

    - name: mount bazel cache
      uses: actions/cache@v2.0.0
      env:
        cache-name: bazel-cache
      with:
        path: "~/.cache/bazel"
        key: ${{ env.cache-name }}-${{ runner.os }}-${{ github.ref }}
        restore-keys: |
          ${{ env.cache-name }}-${{ runner.os }}-main

    - name: build
      run: |
        bazel build //:benchmark //:benchmark_main //test/...

    - name: test
      run: |
        bazel test --test_output=all //test/...
