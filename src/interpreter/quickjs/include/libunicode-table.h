/*
 * Regular Expression Engine
 *
 * Copyright (c) 2017-2018 <PERSON><PERSON><PERSON>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
 * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

/* Compressed unicode tables */
/* Automatically generated file - do not edit */

#include <stdint.h>

static const uint32_t case_conv_table1[359] = {
    0x00209a30, 0x00309a00, 0x005a8173, 0x00601730, 0x006c0730, 0x006f81b3,
    0x00701700, 0x007c0700, 0x007f8100, 0x00803040, 0x009801c3, 0x00988190,
    0x00990640, 0x009c9040, 0x00a481b4, 0x00a52e40, 0x00bc0130, 0x00bc8640,
    0x00bf8170, 0x00c00100, 0x00c08130, 0x00c10440, 0x00c30130, 0x00c38240,
    0x00c48230, 0x00c58240, 0x00c70130, 0x00c78130, 0x00c80130, 0x00c88240,
    0x00c98130, 0x00ca0130, 0x00ca8100, 0x00cb0130, 0x00cb8130, 0x00cc0240,
    0x00cd0100, 0x00ce0130, 0x00ce8130, 0x00cf0100, 0x00cf8130, 0x00d00640,
    0x00d30130, 0x00d38240, 0x00d48130, 0x00d60240, 0x00d70130, 0x00d78240,
    0x00d88230, 0x00d98440, 0x00db8130, 0x00dc0240, 0x00de0240, 0x00df8100,
    0x00e20350, 0x00e38350, 0x00e50350, 0x00e69040, 0x00ee8100, 0x00ef1240,
    0x00f801b4, 0x00f88350, 0x00fa0240, 0x00fb0130, 0x00fb8130, 0x00fc2840,
    0x01100130, 0x01111240, 0x011d0131, 0x011d8240, 0x011e8130, 0x011f0131,
    0x011f8201, 0x01208240, 0x01218130, 0x01220130, 0x01228130, 0x01230a40,
    0x01280101, 0x01288101, 0x01290101, 0x01298100, 0x012a0100, 0x012b0200,
    0x012c8100, 0x012d8100, 0x012e0101, 0x01300100, 0x01308101, 0x01318100,
    0x01328101, 0x01330101, 0x01340100, 0x01348100, 0x01350101, 0x01358101,
    0x01360101, 0x01378100, 0x01388101, 0x01390100, 0x013a8100, 0x013e8101,
    0x01400100, 0x01410101, 0x01418100, 0x01438101, 0x01440100, 0x01448100,
    0x01450200, 0x01460100, 0x01490100, 0x014e8101, 0x014f0101, 0x01a28173,
    0x01b80440, 0x01bb0240, 0x01bd8300, 0x01bf8130, 0x01c30130, 0x01c40330,
    0x01c60130, 0x01c70230, 0x01c801d0, 0x01c89130, 0x01d18930, 0x01d60100,
    0x01d68300, 0x01d801d3, 0x01d89100, 0x01e10173, 0x01e18900, 0x01e60100,
    0x01e68200, 0x01e78130, 0x01e80173, 0x01e88173, 0x01ea8173, 0x01eb0173,
    0x01eb8100, 0x01ec1840, 0x01f80173, 0x01f88173, 0x01f90100, 0x01f98100,
    0x01fa01a0, 0x01fa8173, 0x01fb8240, 0x01fc8130, 0x01fd0240, 0x01fe8330,
    0x02001030, 0x02082030, 0x02182000, 0x02281000, 0x02302240, 0x02453640,
    0x02600130, 0x02608e40, 0x02678100, 0x02686040, 0x0298a630, 0x02b0a600,
    0x02c381b5, 0x08502631, 0x08638131, 0x08668131, 0x08682b00, 0x087e8300,
    0x09d05011, 0x09f80610, 0x09fc0620, 0x0e400174, 0x0e408174, 0x0e410174,
    0x0e418174, 0x0e420174, 0x0e428174, 0x0e430174, 0x0e438180, 0x0e440180,
    0x0e482b30, 0x0e5e8330, 0x0ebc8101, 0x0ebe8101, 0x0ec70101, 0x0f007e40,
    0x0f3f1840, 0x0f4b01b5, 0x0f4b81b6, 0x0f4c01b6, 0x0f4c81b6, 0x0f4d01b7,
    0x0f4d8180, 0x0f4f0130, 0x0f506040, 0x0f800800, 0x0f840830, 0x0f880600,
    0x0f8c0630, 0x0f900800, 0x0f940830, 0x0f980800, 0x0f9c0830, 0x0fa00600,
    0x0fa40630, 0x0fa801b0, 0x0fa88100, 0x0fa901d3, 0x0fa98100, 0x0faa01d3,
    0x0faa8100, 0x0fab01d3, 0x0fab8100, 0x0fac8130, 0x0fad8130, 0x0fae8130,
    0x0faf8130, 0x0fb00800, 0x0fb40830, 0x0fb80200, 0x0fb90400, 0x0fbb0200,
    0x0fbc0201, 0x0fbd0201, 0x0fbe0201, 0x0fc008b7, 0x0fc40867, 0x0fc808b8,
    0x0fcc0868, 0x0fd008b8, 0x0fd40868, 0x0fd80200, 0x0fd901b9, 0x0fd981b1,
    0x0fda01b9, 0x0fdb01b1, 0x0fdb81d7, 0x0fdc0230, 0x0fdd0230, 0x0fde0161,
    0x0fdf0173, 0x0fe101b9, 0x0fe181b2, 0x0fe201ba, 0x0fe301b2, 0x0fe381d8,
    0x0fe40430, 0x0fe60162, 0x0fe80200, 0x0fe901d0, 0x0fe981d0, 0x0feb01b0,
    0x0feb81d0, 0x0fec0230, 0x0fed0230, 0x0ff00201, 0x0ff101d3, 0x0ff181d3,
    0x0ff201ba, 0x0ff28101, 0x0ff301b0, 0x0ff381d3, 0x0ff40230, 0x0ff50230,
    0x0ff60131, 0x0ff901ba, 0x0ff981b2, 0x0ffa01bb, 0x0ffb01b2, 0x0ffb81d9,
    0x0ffc0230, 0x0ffd0230, 0x0ffe0162, 0x109301a0, 0x109501a0, 0x109581a0,
    0x10990131, 0x10a70101, 0x10b01031, 0x10b81001, 0x10c18240, 0x125b1a31,
    0x12681a01, 0x16002f31, 0x16182f01, 0x16300240, 0x16310130, 0x16318130,
    0x16320130, 0x16328100, 0x16330100, 0x16338640, 0x16368130, 0x16370130,
    0x16378130, 0x16380130, 0x16390240, 0x163a8240, 0x163f0230, 0x16406440,
    0x16758440, 0x16790240, 0x16802600, 0x16938100, 0x16968100, 0x53202e40,
    0x53401c40, 0x53910e40, 0x53993e40, 0x53bc8440, 0x53be8130, 0x53bf0a40,
    0x53c58240, 0x53c68130, 0x53c80440, 0x53ca0101, 0x53cb1440, 0x53d50130,
    0x53d58130, 0x53d60130, 0x53d68130, 0x53d70130, 0x53d80130, 0x53d88130,
    0x53d90130, 0x53d98131, 0x53da0c40, 0x53e10240, 0x53e20131, 0x53e28130,
    0x53e30130, 0x55a98101, 0x55b85020, 0x7d8001b2, 0x7d8081b2, 0x7d8101b2,
    0x7d8181da, 0x7d8201da, 0x7d8281b3, 0x7d8301b3, 0x7d8981bb, 0x7d8a01bb,
    0x7d8a81bb, 0x7d8b01bc, 0x7d8b81bb, 0x7f909a31, 0x7fa09a01, 0x82002831,
    0x82142801, 0x82582431, 0x826c2401, 0x86403331, 0x86603301, 0x8c502031,
    0x8c602001, 0xb7202031, 0xb7302001, 0xf4802231, 0xf4912201,
};

static const uint8_t case_conv_table2[359] = {
    0x01, 0x00, 0x9c, 0x06, 0x07, 0x4d, 0x03, 0x04, 0x10, 0x00, 0x8f, 0x0b,
    0x00, 0x00, 0x11, 0x00, 0x08, 0x00, 0x53, 0x4a, 0x51, 0x00, 0x52, 0x00,
    0x53, 0x00, 0x3a, 0x54, 0x55, 0x00, 0x57, 0x59, 0x3f, 0x5d, 0x5c, 0x00,
    0x46, 0x61, 0x63, 0x42, 0x64, 0x00, 0x66, 0x00, 0x68, 0x00, 0x6a, 0x00,
    0x6c, 0x00, 0x6e, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x1a, 0x00,
    0x93, 0x00, 0x00, 0x20, 0x35, 0x00, 0x27, 0x00, 0x21, 0x00, 0x24, 0x22,
    0x2a, 0x00, 0x13, 0x6b, 0x6d, 0x00, 0x26, 0x24, 0x27, 0x14, 0x16, 0x18,
    0x1b, 0x1c, 0x3e, 0x1e, 0x3f, 0x1f, 0x39, 0x3d, 0x22, 0x21, 0x41, 0x1e,
    0x40, 0x25, 0x25, 0x26, 0x28, 0x20, 0x2a, 0x49, 0x2c, 0x43, 0x2e, 0x4b,
    0x30, 0x4c, 0x32, 0x44, 0x42, 0x99, 0x00, 0x00, 0x95, 0x8f, 0x7d, 0x7e,
    0x83, 0x84, 0x12, 0x80, 0x82, 0x76, 0x77, 0x12, 0x7b, 0xa3, 0x7c, 0x78,
    0x79, 0x8a, 0x92, 0x98, 0xa6, 0xa0, 0x85, 0x00, 0x9a, 0xa1, 0x93, 0x75,
    0x33, 0x95, 0x00, 0x8e, 0x00, 0x74, 0x99, 0x98, 0x97, 0x96, 0x00, 0x00,
    0x9e, 0x00, 0x9c, 0x00, 0xa1, 0xa0, 0x15, 0x2e, 0x2f, 0x30, 0xb4, 0xb5,
    0x4c, 0xaa, 0xa9, 0x12, 0x14, 0x1e, 0x21, 0x22, 0x22, 0x2a, 0x34, 0x35,
    0xa6, 0xa7, 0x36, 0x1f, 0x4a, 0x00, 0x00, 0x97, 0x01, 0x5a, 0xda, 0x1d,
    0x36, 0x05, 0x00, 0xc4, 0xc3, 0xc6, 0xc5, 0xc8, 0xc7, 0xca, 0xc9, 0xcc,
    0xcb, 0xc4, 0xd5, 0x45, 0xd6, 0x42, 0xd7, 0x46, 0xd8, 0xce, 0xd0, 0xd2,
    0xd4, 0xda, 0xd9, 0xee, 0xf6, 0xfe, 0x0e, 0x07, 0x0f, 0x80, 0x9f, 0x00,
    0x21, 0x80, 0xa3, 0xed, 0x00, 0xc0, 0x40, 0xc6, 0x60, 0xe7, 0xdb, 0xe6,
    0x99, 0xc0, 0x00, 0x00, 0x06, 0x60, 0xdc, 0x29, 0xfd, 0x15, 0x12, 0x06,
    0x16, 0xf8, 0xdd, 0x06, 0x15, 0x12, 0x84, 0x08, 0xc6, 0x16, 0xff, 0xdf,
    0x03, 0xc0, 0x40, 0x00, 0x46, 0x60, 0xde, 0xe0, 0x6d, 0x37, 0x38, 0x39,
    0x15, 0x14, 0x17, 0x16, 0x00, 0x1a, 0x19, 0x1c, 0x1b, 0x00, 0x5f, 0xb7,
    0x65, 0x44, 0x47, 0x00, 0x4f, 0x62, 0x4e, 0x50, 0x00, 0x00, 0x48, 0x00,
    0x00, 0x00, 0xa3, 0xa4, 0xa5, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb6, 0x00,
    0x00, 0x5a, 0x00, 0x48, 0x00, 0x5b, 0x56, 0x58, 0x60, 0x5e, 0x70, 0x69,
    0x6f, 0x4b, 0x00, 0x00, 0x3b, 0x67, 0xb8, 0x45, 0xa8, 0x8a, 0x8b, 0x8c,
    0xab, 0xac, 0x58, 0x58, 0xaf, 0x94, 0xb0, 0x6f, 0xb2, 0x5a, 0x59, 0x5c,
    0x5b, 0x5e, 0x5d, 0x60, 0x5f, 0x62, 0x61, 0x64, 0x63, 0x66, 0x65,
};

static const uint16_t case_conv_ext[58] = {
    0x0399, 0x0308, 0x0301, 0x03a5, 0x0313, 0x0300, 0x0342, 0x0391, 0x0397,
    0x03a9, 0x0046, 0x0049, 0x004c, 0x0053, 0x0069, 0x0307, 0x02bc, 0x004e,
    0x004a, 0x030c, 0x0535, 0x0552, 0x0048, 0x0331, 0x0054, 0x0057, 0x030a,
    0x0059, 0x0041, 0x02be, 0x1f08, 0x1f80, 0x1f28, 0x1f90, 0x1f68, 0x1fa0,
    0x1fba, 0x0386, 0x1fb3, 0x1fca, 0x0389, 0x1fc3, 0x03a1, 0x1ffa, 0x038f,
    0x1ff3, 0x0544, 0x0546, 0x053b, 0x054e, 0x053d, 0x03b8, 0x0462, 0xa64a,
    0x1e60, 0x03c9, 0x006b, 0x00e5,
};

static const uint8_t unicode_prop_Cased1_table[172] = {
    0x40, 0xa9, 0x80, 0x8e, 0x80, 0xfc, 0x80, 0xd3, 0x80, 0x8c, 0x80, 0x8d,
    0x81, 0x8d, 0x02, 0x80, 0xe1, 0x80, 0x91, 0x85, 0x9a, 0x01, 0x00, 0x01,
    0x11, 0x00, 0x01, 0x04, 0x08, 0x01, 0x08, 0x30, 0x08, 0x01, 0x15, 0x20,
    0x00, 0x39, 0x99, 0x31, 0x9d, 0x84, 0x40, 0x94, 0x80, 0xd6, 0x82, 0xa6,
    0x80, 0x41, 0x62, 0x80, 0xa6, 0x80, 0x57, 0x76, 0xf8, 0x02, 0x80, 0x8f,
    0x80, 0xb0, 0x40, 0xdb, 0x08, 0x80, 0x41, 0xd0, 0x80, 0x8c, 0x80, 0x8f,
    0x8c, 0xe4, 0x03, 0x01, 0x89, 0x00, 0x14, 0x28, 0x10, 0x11, 0x02, 0x01,
    0x18, 0x0b, 0x24, 0x4b, 0x26, 0x01, 0x01, 0x86, 0xe5, 0x80, 0x60, 0x79,
    0xb6, 0x81, 0x40, 0x91, 0x81, 0xbd, 0x88, 0x94, 0x05, 0x80, 0x98, 0x80,
    0xc7, 0x82, 0x43, 0x34, 0xa2, 0x06, 0x80, 0x8b, 0x61, 0x28, 0x97, 0xd4,
    0x80, 0xc6, 0x01, 0x08, 0x09, 0x0b, 0x80, 0x8b, 0x00, 0x06, 0x80, 0xc0,
    0x03, 0x0f, 0x06, 0x80, 0x9b, 0x03, 0x04, 0x00, 0x16, 0x80, 0x41, 0x53,
    0x81, 0x98, 0x80, 0x98, 0x80, 0x9e, 0x80, 0x98, 0x80, 0x9e, 0x80, 0x98,
    0x80, 0x9e, 0x80, 0x98, 0x80, 0x9e, 0x80, 0x98, 0x07, 0x59, 0x63, 0x99,
    0x85, 0x99, 0x85, 0x99,
};

static const uint8_t unicode_prop_Cased1_index[18] = {
    0xb9, 0x02, 0xe0, 0xa0, 0x1e, 0x40, 0x9e, 0xa6, 0x40,
    0xba, 0xd4, 0x01, 0x89, 0xd7, 0x01, 0x8a, 0xf1, 0x01,
};

static const uint8_t unicode_prop_Case_Ignorable_table[678] = {
    0xa6, 0x05, 0x80, 0x8a, 0x80, 0xa2, 0x00, 0x80, 0xc6, 0x03, 0x00, 0x03,
    0x01, 0x81, 0x41, 0xf6, 0x40, 0xbf, 0x19, 0x18, 0x88, 0x08, 0x80, 0x40,
    0xfa, 0x86, 0x40, 0xce, 0x80, 0xb6, 0xac, 0x00, 0x01, 0x01, 0x00, 0xab,
    0x80, 0x8a, 0x85, 0x89, 0x8a, 0x00, 0xa2, 0x80, 0x89, 0x94, 0x8f, 0x80,
    0xe4, 0x38, 0x89, 0x03, 0xa0, 0x00, 0x80, 0x9d, 0x9a, 0xda, 0x8a, 0xb9,
    0x8a, 0x18, 0x08, 0x97, 0x97, 0xaa, 0x82, 0xf6, 0xaf, 0xb6, 0x00, 0x03,
    0x3b, 0x02, 0x86, 0x89, 0x81, 0x8c, 0x80, 0x8e, 0x80, 0xb9, 0x03, 0x1f,
    0x80, 0x93, 0x81, 0x99, 0x01, 0x81, 0xb8, 0x03, 0x0b, 0x09, 0x12, 0x80,
    0x9d, 0x0a, 0x80, 0x8a, 0x81, 0xb8, 0x03, 0x20, 0x0b, 0x80, 0x93, 0x81,
    0x95, 0x28, 0x80, 0xb9, 0x01, 0x00, 0x1f, 0x07, 0x80, 0x8a, 0x81, 0x9d,
    0x80, 0xbc, 0x80, 0x8b, 0x80, 0xb1, 0x02, 0x80, 0xb8, 0x14, 0x10, 0x1e,
    0x81, 0x8a, 0x81, 0x9c, 0x80, 0xb9, 0x01, 0x05, 0x04, 0x81, 0x93, 0x81,
    0x9b, 0x81, 0xb8, 0x0b, 0x1f, 0x80, 0x93, 0x81, 0xe5, 0x06, 0x10, 0x80,
    0xd9, 0x01, 0x86, 0x8a, 0x88, 0xe1, 0x01, 0x88, 0x88, 0x00, 0x85, 0xc9,
    0x81, 0x9a, 0x00, 0x00, 0x80, 0xb6, 0x8d, 0x04, 0x01, 0x84, 0x8a, 0x80,
    0xa3, 0x88, 0x80, 0xe5, 0x18, 0x28, 0x09, 0x81, 0x98, 0x0b, 0x82, 0x8f,
    0x83, 0x8c, 0x01, 0x0d, 0x80, 0x8e, 0x80, 0xdd, 0x80, 0x42, 0x5f, 0x82,
    0x43, 0xb1, 0x82, 0x9c, 0x82, 0x9c, 0x81, 0x9d, 0x81, 0xbf, 0x08, 0x37,
    0x01, 0x8a, 0x10, 0x20, 0xac, 0x83, 0xb3, 0x80, 0xc0, 0x81, 0xa1, 0x80,
    0xf5, 0x13, 0x81, 0x88, 0x05, 0x82, 0x40, 0xda, 0x09, 0x80, 0xb9, 0x00,
    0x30, 0x00, 0x01, 0x3d, 0x89, 0x08, 0xa6, 0x07, 0x8e, 0xc0, 0x83, 0xaf,
    0x00, 0x20, 0x04, 0x80, 0xa7, 0x88, 0x8b, 0x81, 0x9f, 0x19, 0x08, 0x82,
    0xb7, 0x00, 0x0a, 0x00, 0x82, 0xb9, 0x39, 0x81, 0xbf, 0x85, 0xd1, 0x10,
    0x8c, 0x06, 0x18, 0x28, 0x11, 0xb1, 0xbe, 0x8c, 0x80, 0xa1, 0xde, 0x04,
    0x41, 0xbc, 0x00, 0x82, 0x8a, 0x82, 0x8c, 0x82, 0x8c, 0x82, 0x8c, 0x81,
    0x8b, 0x27, 0x81, 0x89, 0x01, 0x01, 0x84, 0xb0, 0x20, 0x89, 0x00, 0x8c,
    0x80, 0x8f, 0x8c, 0xb2, 0xa0, 0x4b, 0x8a, 0x81, 0xf0, 0x82, 0xfc, 0x80,
    0x8e, 0x80, 0xdf, 0x9f, 0xae, 0x80, 0x41, 0xd4, 0x80, 0xa3, 0x1a, 0x24,
    0x80, 0xdc, 0x85, 0xdc, 0x82, 0x60, 0x6f, 0x15, 0x80, 0x44, 0xe1, 0x85,
    0x41, 0x0d, 0x80, 0xe1, 0x18, 0x89, 0x00, 0x9b, 0x83, 0xcf, 0x81, 0x8d,
    0xa1, 0xcd, 0x80, 0x96, 0x82, 0xec, 0x0f, 0x02, 0x03, 0x80, 0x98, 0x81,
    0x40, 0x9c, 0x81, 0x99, 0x91, 0x8c, 0x80, 0xa5, 0x87, 0x98, 0x8a, 0xad,
    0x82, 0xaf, 0x01, 0x19, 0x81, 0x90, 0x80, 0x94, 0x81, 0xc1, 0x29, 0x09,
    0x81, 0x8b, 0x07, 0x80, 0xa2, 0x80, 0x8a, 0x80, 0xb2, 0x00, 0x11, 0x0c,
    0x08, 0x80, 0x9a, 0x80, 0x8d, 0x0c, 0x08, 0x80, 0xe3, 0x84, 0x40, 0x84,
    0x01, 0x03, 0x80, 0x60, 0x4f, 0x2f, 0x80, 0x40, 0x92, 0x8f, 0x42, 0x3d,
    0x8f, 0x10, 0x8b, 0x8f, 0xa1, 0x01, 0x80, 0x40, 0xa8, 0x06, 0x05, 0x80,
    0x8a, 0x80, 0xa2, 0x00, 0x80, 0xae, 0x80, 0xac, 0x81, 0xc2, 0x80, 0x94,
    0x82, 0x42, 0x00, 0x80, 0x40, 0xe1, 0x80, 0x40, 0x94, 0x84, 0x46, 0x85,
    0x10, 0x0c, 0x83, 0xa7, 0x13, 0x80, 0x40, 0xa4, 0x81, 0x42, 0x3c, 0x83,
    0x42, 0x1d, 0x8a, 0x40, 0xaf, 0x80, 0xb5, 0x8e, 0xb7, 0x82, 0xb0, 0x19,
    0x09, 0x80, 0x8e, 0x80, 0xb1, 0x82, 0xa3, 0x20, 0x87, 0xbd, 0x80, 0x8b,
    0x81, 0xb3, 0x88, 0x89, 0x83, 0xe1, 0x11, 0x00, 0x0d, 0x80, 0x40, 0x9f,
    0x02, 0x87, 0x94, 0x81, 0xb8, 0x0a, 0x80, 0xa4, 0x32, 0x84, 0x40, 0xc2,
    0x39, 0x10, 0x80, 0x96, 0x80, 0xd3, 0x28, 0x03, 0x08, 0x81, 0x40, 0xed,
    0x1d, 0x08, 0x81, 0x9a, 0x81, 0xd4, 0x39, 0x00, 0x81, 0xe9, 0x00, 0x01,
    0x28, 0x80, 0xe4, 0x11, 0x18, 0x84, 0x41, 0x02, 0x88, 0x01, 0x41, 0x98,
    0x19, 0x0b, 0x80, 0x9f, 0x89, 0xa7, 0x29, 0x1f, 0x80, 0x88, 0x29, 0x82,
    0xad, 0x8c, 0x01, 0x41, 0x95, 0x30, 0x28, 0x80, 0xd1, 0x95, 0x0e, 0x01,
    0x01, 0xf9, 0x2a, 0x00, 0x08, 0x30, 0x80, 0xc7, 0x0a, 0x00, 0x80, 0x41,
    0x5a, 0x81, 0x55, 0x3a, 0x88, 0x60, 0x36, 0xb6, 0x84, 0xba, 0x86, 0x88,
    0x83, 0x44, 0x0a, 0x80, 0xbe, 0x90, 0xbf, 0x08, 0x80, 0x60, 0x4c, 0xb8,
    0x08, 0x83, 0x54, 0xc2, 0x82, 0x88, 0x8f, 0x0e, 0x9d, 0x83, 0x40, 0x93,
    0x82, 0x47, 0xba, 0xb6, 0x83, 0xb1, 0x38, 0x8d, 0x80, 0x95, 0x20, 0x8e,
    0x45, 0x4f, 0x30, 0x90, 0x0e, 0x01, 0x04, 0x41, 0x04, 0x8d, 0x41, 0xad,
    0x83, 0x45, 0xdf, 0x86, 0xec, 0x87, 0x4a, 0xae, 0x84, 0x6c, 0x0c, 0x00,
    0x80, 0x9d, 0xdf, 0xff, 0x40, 0xef,
};

static const uint8_t unicode_prop_Case_Ignorable_index[66] = {
    0xc0, 0x05, 0x00, 0x2e, 0x08, 0x20, 0x52, 0x0a, 0x00, 0x05, 0x0c,
    0x00, 0x4f, 0x0e, 0x20, 0x75, 0x10, 0x20, 0x44, 0x18, 0x00, 0x43,
    0x1b, 0x00, 0x00, 0x1e, 0x00, 0x7e, 0x2c, 0x00, 0x7e, 0xa6, 0x40,
    0x83, 0xa9, 0x20, 0xf7, 0xaa, 0x00, 0x41, 0xff, 0x20, 0x28, 0x0d,
    0x01, 0x3f, 0x12, 0x41, 0xde, 0x15, 0x21, 0x5c, 0x1a, 0x01, 0xf5,
    0x6a, 0x21, 0x37, 0xda, 0x01, 0x02, 0x00, 0x2e, 0xf0, 0x01, 0x0e,
};

static const uint8_t unicode_prop_ID_Start_table[1024] = {
    0xc0, 0x99, 0x85, 0x99, 0xae, 0x80, 0x89, 0x03, 0x04, 0x96, 0x80, 0x9e,
    0x80, 0x41, 0xc9, 0x83, 0x8b, 0x8d, 0x26, 0x00, 0x80, 0x40, 0x80, 0x20,
    0x09, 0x18, 0x05, 0x00, 0x10, 0x00, 0x93, 0x80, 0xd2, 0x80, 0x40, 0x8a,
    0x87, 0x40, 0xa5, 0x80, 0xa5, 0x08, 0x85, 0xa8, 0xc6, 0x9a, 0x1b, 0xac,
    0xaa, 0xa2, 0x08, 0xe2, 0x00, 0x8e, 0x0e, 0x81, 0x89, 0x11, 0x80, 0x8f,
    0x00, 0x9d, 0x9c, 0xd8, 0x8a, 0x80, 0x97, 0xa0, 0x88, 0x0b, 0x04, 0x95,
    0x18, 0x88, 0x02, 0x80, 0x96, 0x98, 0x86, 0x8a, 0xb4, 0x94, 0x07, 0xc5,
    0xb5, 0x10, 0x91, 0x06, 0x89, 0x8e, 0x8f, 0x1f, 0x09, 0x81, 0x95, 0x06,
    0x00, 0x13, 0x10, 0x8f, 0x80, 0x8c, 0x08, 0x82, 0x8d, 0x81, 0x89, 0x07,
    0x2b, 0x09, 0x95, 0x06, 0x01, 0x01, 0x01, 0x9e, 0x18, 0x80, 0x92, 0x82,
    0x8f, 0x88, 0x02, 0x80, 0x95, 0x06, 0x01, 0x04, 0x10, 0x91, 0x80, 0x8e,
    0x81, 0x96, 0x80, 0x8a, 0x39, 0x09, 0x95, 0x06, 0x01, 0x04, 0x10, 0x9d,
    0x08, 0x82, 0x8e, 0x80, 0x90, 0x00, 0x2a, 0x10, 0x1a, 0x08, 0x00, 0x0a,
    0x0a, 0x12, 0x8b, 0x95, 0x80, 0xb3, 0x38, 0x10, 0x96, 0x80, 0x8f, 0x10,
    0x99, 0x14, 0x81, 0x9d, 0x03, 0x38, 0x10, 0x96, 0x80, 0x89, 0x04, 0x10,
    0x9f, 0x00, 0x81, 0x8e, 0x81, 0x91, 0x38, 0x10, 0xa8, 0x08, 0x8f, 0x04,
    0x17, 0x82, 0x97, 0x2c, 0x91, 0x82, 0x97, 0x80, 0x88, 0x00, 0x0e, 0xb9,
    0xaf, 0x01, 0x8b, 0x86, 0xb9, 0x08, 0x00, 0x20, 0x97, 0x00, 0x80, 0x89,
    0x01, 0x88, 0x01, 0x20, 0x80, 0x94, 0x83, 0x9f, 0x80, 0xbe, 0x38, 0xa3,
    0x9a, 0x84, 0xf2, 0xaa, 0x93, 0x80, 0x8f, 0x2b, 0x1a, 0x02, 0x0e, 0x13,
    0x8c, 0x8b, 0x80, 0x90, 0xa5, 0x00, 0x20, 0x81, 0xaa, 0x80, 0x41, 0x4c,
    0x03, 0x0e, 0x00, 0x03, 0x81, 0xa8, 0x03, 0x81, 0xa0, 0x03, 0x0e, 0x00,
    0x03, 0x81, 0x8e, 0x80, 0xb8, 0x03, 0x81, 0xc2, 0xa4, 0x8f, 0x8f, 0xd5,
    0x0d, 0x82, 0x42, 0x6b, 0x81, 0x90, 0x80, 0x99, 0x84, 0xca, 0x82, 0x8a,
    0x86, 0x8c, 0x03, 0x8d, 0x91, 0x8d, 0x91, 0x8d, 0x8c, 0x02, 0x8e, 0xb3,
    0xa2, 0x03, 0x80, 0xc2, 0xd8, 0x86, 0xa8, 0x00, 0x84, 0xc5, 0x89, 0x9e,
    0xb0, 0x9d, 0x0c, 0x8a, 0xab, 0x83, 0x99, 0xb5, 0x96, 0x88, 0xb4, 0xd1,
    0x80, 0xdc, 0xae, 0x90, 0x86, 0xb6, 0x9d, 0x8c, 0x81, 0x89, 0xab, 0x99,
    0xa3, 0xa8, 0x82, 0x89, 0xa3, 0x81, 0x88, 0x86, 0xaa, 0x0a, 0xa8, 0x18,
    0x28, 0x0a, 0x04, 0x40, 0xbf, 0xbf, 0x41, 0x15, 0x0d, 0x81, 0xa5, 0x0d,
    0x0f, 0x00, 0x00, 0x00, 0x80, 0x9e, 0x81, 0xb4, 0x06, 0x00, 0x12, 0x06,
    0x13, 0x0d, 0x83, 0x8c, 0x22, 0x06, 0xf3, 0x80, 0x8c, 0x80, 0x8f, 0x8c,
    0xe4, 0x03, 0x01, 0x89, 0x00, 0x0d, 0x28, 0x00, 0x00, 0x80, 0x8f, 0x0b,
    0x24, 0x18, 0x90, 0xa8, 0x4a, 0x76, 0xae, 0x80, 0xae, 0x80, 0x40, 0x84,
    0x2b, 0x11, 0x8b, 0xa5, 0x00, 0x20, 0x81, 0xb7, 0x30, 0x8f, 0x96, 0x88,
    0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x86, 0x42, 0x25, 0x82, 0x98,
    0x88, 0x34, 0x0c, 0x83, 0xd5, 0x1c, 0x80, 0xd9, 0x03, 0x84, 0xaa, 0x80,
    0xdd, 0x90, 0x9a, 0xb4, 0x8f, 0x41, 0xff, 0x59, 0xb5, 0xc9, 0x60, 0x51,
    0xef, 0x8f, 0x44, 0x8c, 0xc2, 0xad, 0x81, 0x41, 0x0c, 0x82, 0x8f, 0x89,
    0x81, 0x93, 0xae, 0x8f, 0x9e, 0x81, 0xcf, 0xa6, 0x88, 0x81, 0xe6, 0x81,
    0xb4, 0x0c, 0xaf, 0x8a, 0x02, 0x03, 0x80, 0x96, 0x9c, 0xb3, 0x8d, 0xb1,
    0xbd, 0x2a, 0x00, 0x81, 0x8a, 0x9b, 0x89, 0x96, 0x98, 0x9c, 0x86, 0xae,
    0x9b, 0x80, 0x8f, 0x20, 0x89, 0x89, 0x20, 0xa8, 0x96, 0x10, 0x87, 0x93,
    0x96, 0x10, 0x82, 0xb1, 0x00, 0x11, 0x0c, 0x08, 0x00, 0x97, 0x11, 0x8a,
    0x32, 0x8b, 0x29, 0x29, 0x85, 0x88, 0x30, 0x30, 0xaa, 0x80, 0x8b, 0x87,
    0xf2, 0x9c, 0x60, 0x2b, 0xa3, 0x8b, 0x96, 0x83, 0xb0, 0x60, 0x21, 0x03,
    0x41, 0x6d, 0x81, 0xe9, 0xa5, 0x86, 0x8b, 0x24, 0x00, 0x89, 0x80, 0x8c,
    0x04, 0x00, 0x01, 0x01, 0x80, 0xeb, 0xa0, 0x41, 0x6a, 0x91, 0xbf, 0x81,
    0xb5, 0xa7, 0x8b, 0xf3, 0x20, 0x40, 0x86, 0xa3, 0x99, 0x85, 0x99, 0x8a,
    0xd8, 0x15, 0x0d, 0x0d, 0x0a, 0xa2, 0x8b, 0x80, 0x99, 0x80, 0x92, 0x01,
    0x80, 0x8e, 0x81, 0x8d, 0xa1, 0xfa, 0xc4, 0xb4, 0x41, 0x0a, 0x9c, 0x82,
    0xb0, 0xae, 0x9f, 0x8c, 0x9d, 0x84, 0xa5, 0x89, 0x9d, 0x81, 0xa3, 0x1f,
    0x04, 0xa9, 0x40, 0x9d, 0x91, 0xa3, 0x83, 0xa3, 0x83, 0xa7, 0x87, 0xb3,
    0x40, 0x9b, 0x41, 0x36, 0x88, 0x95, 0x89, 0x87, 0x40, 0x97, 0x29, 0x00,
    0xab, 0x01, 0x10, 0x81, 0x96, 0x89, 0x96, 0x88, 0x9e, 0xc0, 0x92, 0x01,
    0x89, 0x95, 0x89, 0x99, 0xc5, 0xb7, 0x29, 0xbf, 0x80, 0x8e, 0x18, 0x10,
    0x9c, 0xa9, 0x9c, 0x82, 0x9c, 0xa2, 0x38, 0x9b, 0x9a, 0xb5, 0x89, 0x95,
    0x89, 0x92, 0x8c, 0x91, 0xed, 0xc8, 0xb6, 0xb2, 0x8c, 0xb2, 0x8c, 0xa3,
    0x41, 0xdb, 0x9c, 0x89, 0x07, 0x95, 0x40, 0x99, 0x96, 0x8b, 0xb4, 0xca,
    0xac, 0x9f, 0x98, 0x99, 0xa3, 0x9c, 0x80, 0x8a, 0xa2, 0x10, 0x8b, 0xaf,
    0x8d, 0x83, 0x94, 0x00, 0x80, 0xa2, 0x91, 0x80, 0x98, 0xd3, 0x30, 0x00,
    0x18, 0x8e, 0x80, 0x89, 0x86, 0xae, 0xa5, 0x39, 0x09, 0x95, 0x06, 0x01,
    0x04, 0x10, 0x91, 0x80, 0x8b, 0x84, 0x40, 0x9d, 0xb4, 0x91, 0x83, 0x93,
    0x80, 0x9f, 0xaf, 0x93, 0x08, 0x80, 0x40, 0xb7, 0xae, 0xa8, 0x83, 0xa3,
    0xaf, 0x93, 0x80, 0xba, 0xaa, 0x8c, 0x80, 0xc6, 0x9a, 0x40, 0xe4, 0xab,
    0xf3, 0xbf, 0x9e, 0x80, 0x40, 0x9f, 0x39, 0xa6, 0x8f, 0x00, 0x80, 0x9b,
    0x80, 0x89, 0xa7, 0x30, 0x94, 0x80, 0x8a, 0xad, 0x92, 0x80, 0xa1, 0xb8,
    0x41, 0x06, 0x88, 0x80, 0xa4, 0x90, 0x80, 0xb0, 0x9d, 0xef, 0x30, 0x08,
    0xa5, 0x94, 0x80, 0x98, 0x28, 0x08, 0x9f, 0x8d, 0x80, 0x41, 0x46, 0x92,
    0x41, 0x0c, 0x43, 0x99, 0xe5, 0xee, 0x90, 0x40, 0xc3, 0x4a, 0xbb, 0x44,
    0x2e, 0x4f, 0xd0, 0x42, 0x46, 0x60, 0x21, 0xb8, 0x42, 0x38, 0x86, 0x9e,
    0xf0, 0x9d, 0x91, 0xaf, 0x8f, 0x83, 0x9e, 0x94, 0x84, 0x92, 0x42, 0xaf,
    0xbf, 0xff, 0xca, 0x20, 0xc1, 0x8c, 0xbf, 0x08, 0x80, 0x9b, 0x57, 0xf7,
    0x87, 0x42, 0xf2, 0x60, 0x25, 0x0c, 0x41, 0x1e, 0xb0, 0x82, 0x90, 0x1f,
    0x41, 0x8b, 0x49, 0x03, 0xea, 0x84, 0x8c, 0x82, 0x88, 0x86, 0x89, 0x57,
    0x65, 0xd4, 0x80, 0xc6, 0x01, 0x08, 0x09, 0x0b, 0x80, 0x8b, 0x00, 0x06,
    0x80, 0xc0, 0x03, 0x0f, 0x06, 0x80, 0x9b, 0x03, 0x04, 0x00, 0x16, 0x80,
    0x41, 0x53, 0x81, 0x98, 0x80, 0x98, 0x80, 0x9e, 0x80, 0x98, 0x80, 0x9e,
    0x80, 0x98, 0x80, 0x9e, 0x80, 0x98, 0x80, 0x9e, 0x80, 0x98, 0x07, 0x49,
    0x33, 0xac, 0x89, 0x86, 0x8f, 0x80, 0x41, 0x70, 0xab, 0x45, 0x13, 0x40,
    0xc4, 0xba, 0xc3, 0x30, 0x44, 0xb3, 0x18, 0x9a, 0x01, 0x00, 0x08, 0x80,
    0x89, 0x03, 0x00, 0x00, 0x28, 0x18, 0x00, 0x00, 0x02, 0x01, 0x00, 0x08,
    0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x0b, 0x06, 0x03, 0x03, 0x00, 0x80,
    0x89, 0x80, 0x90, 0x22, 0x04, 0x80, 0x90, 0x51, 0x43, 0x60, 0xa6, 0xd6,
    0xa8, 0x50, 0x34, 0x8a, 0x40, 0xdd, 0x81, 0x56, 0x81, 0x8d, 0x5d, 0x30,
    0x4c, 0x1e, 0x42, 0x1d,
};

static const uint8_t unicode_prop_ID_Start_index[96] = {
    0xf6, 0x03, 0x20, 0xa6, 0x07, 0x00, 0xb1, 0x09, 0x00, 0xba, 0x0a, 0x00,
    0xd1, 0x0b, 0x20, 0x62, 0x0d, 0x40, 0x01, 0x0f, 0x20, 0x5e, 0x12, 0x00,
    0xf9, 0x16, 0x00, 0x17, 0x1a, 0x20, 0xc0, 0x1d, 0x20, 0x9d, 0x20, 0x00,
    0x68, 0x2d, 0x00, 0x00, 0x32, 0x20, 0xc0, 0xa7, 0x20, 0x29, 0xaa, 0x00,
    0xa4, 0xd7, 0x20, 0xc8, 0xfd, 0x20, 0x75, 0x01, 0x01, 0x37, 0x07, 0x01,
    0x36, 0x0a, 0x21, 0xf7, 0x0f, 0x21, 0xa9, 0x12, 0x01, 0x30, 0x16, 0x21,
    0x8a, 0x1a, 0x01, 0x9a, 0x23, 0x01, 0x80, 0x6e, 0x21, 0x89, 0xbc, 0x21,
    0xc1, 0xd6, 0x01, 0xc5, 0xe8, 0x21, 0x73, 0xee, 0x01, 0x1e, 0xfa, 0x02,
};

static const uint8_t unicode_prop_ID_Continue1_table[607] = {
    0xaf, 0x89, 0xa4, 0x80, 0xd6, 0x80, 0x42, 0x47, 0xef, 0x96, 0x80, 0x40,
    0xfa, 0x84, 0x41, 0x08, 0xac, 0x00, 0x01, 0x01, 0x00, 0xc7, 0x8a, 0xaf,
    0x9e, 0x28, 0xe4, 0x31, 0x29, 0x08, 0x19, 0x89, 0x96, 0x80, 0x9d, 0x9a,
    0xda, 0x8a, 0x8e, 0x89, 0xa0, 0x88, 0x88, 0x80, 0x97, 0x18, 0x88, 0x02,
    0x04, 0xaa, 0x82, 0xf6, 0x8e, 0x80, 0xa0, 0xb5, 0x10, 0x91, 0x06, 0x89,
    0x09, 0x89, 0x90, 0x82, 0xb7, 0x00, 0x31, 0x09, 0x82, 0x88, 0x80, 0x89,
    0x09, 0x89, 0x8d, 0x01, 0x82, 0xb7, 0x00, 0x23, 0x09, 0x12, 0x80, 0x93,
    0x8b, 0x10, 0x8a, 0x82, 0xb7, 0x00, 0x38, 0x10, 0x82, 0x93, 0x09, 0x89,
    0x89, 0x28, 0x82, 0xb7, 0x00, 0x31, 0x09, 0x17, 0x81, 0x89, 0x09, 0x89,
    0x91, 0x80, 0xba, 0x22, 0x10, 0x83, 0x88, 0x80, 0x8d, 0x89, 0x8f, 0x84,
    0xb8, 0x30, 0x10, 0x1e, 0x81, 0x8a, 0x09, 0x89, 0x90, 0x82, 0xb7, 0x00,
    0x30, 0x10, 0x1e, 0x81, 0x8a, 0x09, 0x89, 0x8f, 0x83, 0xb6, 0x08, 0x30,
    0x10, 0x83, 0x88, 0x80, 0x89, 0x09, 0x89, 0x91, 0x81, 0xc5, 0x03, 0x28,
    0x00, 0x3d, 0x89, 0x09, 0xbc, 0x01, 0x86, 0x8b, 0x38, 0x89, 0xd6, 0x01,
    0x88, 0x8a, 0x29, 0x89, 0xbd, 0x0d, 0x89, 0x8a, 0x00, 0x00, 0x03, 0x81,
    0xb0, 0x93, 0x01, 0x84, 0x8a, 0x80, 0xa3, 0x88, 0x80, 0xe3, 0x93, 0x80,
    0x89, 0x8b, 0x1b, 0x10, 0x11, 0x32, 0x83, 0x8c, 0x8b, 0x80, 0x8e, 0x42,
    0xbe, 0x82, 0x88, 0x88, 0x43, 0x9f, 0x82, 0x9c, 0x82, 0x9c, 0x81, 0x9d,
    0x81, 0xbf, 0x9f, 0x88, 0x01, 0x89, 0xa0, 0x11, 0x89, 0x40, 0x8e, 0x80,
    0xf5, 0x8b, 0x83, 0x8b, 0x89, 0x89, 0xff, 0x8a, 0xbb, 0x84, 0xb8, 0x89,
    0x80, 0x9c, 0x81, 0x8a, 0x85, 0x89, 0x95, 0x8d, 0xc1, 0x84, 0xae, 0x90,
    0x8a, 0x89, 0x90, 0x88, 0x8b, 0x82, 0x9d, 0x8c, 0x81, 0x89, 0xab, 0x8d,
    0xaf, 0x93, 0x87, 0x89, 0x85, 0x89, 0xf5, 0x10, 0x94, 0x18, 0x28, 0x0a,
    0x40, 0xc5, 0xb9, 0x04, 0x42, 0x3e, 0x81, 0x92, 0x80, 0xfa, 0x8c, 0x18,
    0x82, 0x8b, 0x4b, 0xfd, 0x82, 0x40, 0x8c, 0x80, 0xdf, 0x9f, 0x42, 0x29,
    0x85, 0xe8, 0x81, 0x60, 0x75, 0x84, 0x89, 0xc4, 0x03, 0x89, 0x9f, 0x81,
    0xcf, 0x81, 0x41, 0x0f, 0x02, 0x03, 0x80, 0x96, 0x84, 0xd7, 0x81, 0xb1,
    0x91, 0x89, 0x89, 0x85, 0x91, 0x8c, 0x8a, 0x9b, 0x87, 0x98, 0x8c, 0xab,
    0x83, 0xae, 0x8d, 0x8e, 0x89, 0x8a, 0x80, 0x89, 0x89, 0xae, 0x8d, 0x8b,
    0x07, 0x09, 0x89, 0xa0, 0x82, 0xb1, 0x00, 0x11, 0x0c, 0x08, 0x80, 0xa8,
    0x24, 0x81, 0x40, 0xeb, 0x38, 0x09, 0x89, 0x60, 0x4f, 0x23, 0x80, 0x42,
    0xe0, 0x8f, 0x8f, 0x8f, 0x11, 0x97, 0x82, 0x40, 0xbf, 0x89, 0xa4, 0x80,
    0x42, 0xbc, 0x80, 0x40, 0xe1, 0x80, 0x40, 0x94, 0x84, 0x41, 0x24, 0x89,
    0x45, 0x56, 0x10, 0x0c, 0x83, 0xa7, 0x13, 0x80, 0x40, 0xa4, 0x81, 0x42,
    0x3c, 0x1f, 0x89, 0x42, 0x0b, 0x8a, 0x40, 0xae, 0x82, 0xb4, 0x8e, 0x9e,
    0x89, 0x8e, 0x83, 0xac, 0x8a, 0xb4, 0x89, 0x2a, 0xa3, 0x8d, 0x80, 0x89,
    0x21, 0xab, 0x80, 0x8b, 0x82, 0xaf, 0x8d, 0x3b, 0x82, 0x89, 0xd1, 0x8b,
    0x28, 0x40, 0x9f, 0x8b, 0x84, 0x89, 0x2b, 0xb6, 0x08, 0x31, 0x09, 0x82,
    0x88, 0x80, 0x89, 0x09, 0x32, 0x84, 0x40, 0xbf, 0x91, 0x88, 0x89, 0x18,
    0xd0, 0x93, 0x8b, 0x89, 0x40, 0xd4, 0x31, 0x88, 0x9a, 0x81, 0xd1, 0x90,
    0x8e, 0x89, 0xd0, 0x8c, 0x87, 0x89, 0xd2, 0x8e, 0x83, 0x89, 0x40, 0xf1,
    0x8e, 0x40, 0xa4, 0x89, 0x40, 0xe6, 0x31, 0x32, 0x80, 0x9b, 0x89, 0xa7,
    0x30, 0x1f, 0x80, 0x88, 0x8a, 0xad, 0x8f, 0x41, 0x94, 0x38, 0x87, 0x8f,
    0x89, 0xb7, 0x95, 0x80, 0x8d, 0xf9, 0x2a, 0x00, 0x08, 0x30, 0x07, 0x89,
    0xaf, 0x20, 0x08, 0x27, 0x89, 0x41, 0x48, 0x83, 0x60, 0x4b, 0x68, 0x89,
    0x40, 0x85, 0x84, 0xba, 0x86, 0x98, 0x89, 0x43, 0xf4, 0x00, 0xb6, 0x33,
    0x60, 0x4d, 0x09, 0x81, 0x54, 0xc5, 0x22, 0x2f, 0x39, 0x86, 0x9d, 0x83,
    0x40, 0x93, 0x82, 0x45, 0x88, 0xb1, 0x41, 0xff, 0xb6, 0x83, 0xb1, 0x38,
    0x8d, 0x80, 0x95, 0x20, 0x8e, 0x45, 0x4f, 0x30, 0x90, 0x0e, 0x01, 0x04,
    0x41, 0x04, 0x86, 0x88, 0x89, 0x41, 0xa1, 0x8d, 0x45, 0xd5, 0x86, 0xec,
    0x34, 0x89, 0x6c, 0x17, 0xa5, 0x40, 0xef,
};

static const uint8_t unicode_prop_ID_Continue1_index[57] = {
    0xfa, 0x06, 0x00, 0x84, 0x09, 0x00, 0xf0, 0x0a, 0x00, 0x70, 0x0c, 0x00,
    0xf4, 0x0d, 0x00, 0x4a, 0x10, 0x20, 0x1a, 0x18, 0x20, 0x74, 0x1b, 0x00,
    0xe2, 0x20, 0x00, 0x28, 0xa8, 0x20, 0x7e, 0xaa, 0x20, 0x40, 0xff, 0x00,
    0x03, 0x10, 0x21, 0xeb, 0x12, 0x01, 0x41, 0x16, 0x01, 0x40, 0x1c, 0x61,
    0x37, 0x6b, 0x21, 0x76, 0xda, 0x01, 0xf0, 0x01, 0x0e,
};

#ifdef CONFIG_ALL_UNICODE

static const uint8_t unicode_cc_table[831] = {
    0xb2, 0xcf, 0xd4, 0x00, 0xe8, 0x03, 0xdc, 0x00, 0xe8, 0x00, 0xd8, 0x04,
    0xdc, 0x01, 0xca, 0x03, 0xdc, 0x01, 0xca, 0x0a, 0xdc, 0x04, 0x01, 0x03,
    0xdc, 0xc7, 0x00, 0xf0, 0xc0, 0x02, 0xdc, 0xc2, 0x01, 0xdc, 0x80, 0xc2,
    0x03, 0xdc, 0xc0, 0x00, 0xe8, 0x01, 0xdc, 0xc0, 0x41, 0xe9, 0x00, 0xea,
    0x41, 0xe9, 0x00, 0xea, 0x00, 0xe9, 0xcc, 0xb0, 0xe2, 0xc4, 0xb0, 0xd8,
    0x00, 0xdc, 0xc3, 0x00, 0xdc, 0xc2, 0x00, 0xde, 0x00, 0xdc, 0xc5, 0x05,
    0xdc, 0xc1, 0x00, 0xdc, 0xc1, 0x00, 0xde, 0x00, 0xe4, 0xc0, 0x49, 0x0a,
    0x43, 0x13, 0x80, 0x00, 0x17, 0x80, 0x41, 0x18, 0x80, 0xc0, 0x00, 0xdc,
    0x80, 0x00, 0x12, 0xb0, 0x17, 0xc7, 0x42, 0x1e, 0xaf, 0x47, 0x1b, 0xc1,
    0x01, 0xdc, 0xc4, 0x00, 0xdc, 0xc1, 0x00, 0xdc, 0x8f, 0x00, 0x23, 0xb0,
    0x34, 0xc6, 0x81, 0xc3, 0x00, 0xdc, 0xc0, 0x81, 0xc1, 0x80, 0x00, 0xdc,
    0xc1, 0x00, 0xdc, 0xa2, 0x00, 0x24, 0x9d, 0xc0, 0x00, 0xdc, 0xc1, 0x00,
    0xdc, 0xc1, 0x02, 0xdc, 0xc0, 0x01, 0xdc, 0xc0, 0x00, 0xdc, 0xc2, 0x00,
    0xdc, 0xc0, 0x00, 0xdc, 0xc0, 0x00, 0xdc, 0xc0, 0x00, 0xdc, 0xc1, 0xb0,
    0x6f, 0xc6, 0x00, 0xdc, 0xc0, 0x88, 0x00, 0xdc, 0x97, 0xc3, 0x80, 0xc8,
    0x80, 0xc2, 0x80, 0xc4, 0xaa, 0x02, 0xdc, 0xb0, 0x46, 0x00, 0xdc, 0xcd,
    0x80, 0x00, 0xdc, 0xc1, 0x00, 0xdc, 0xc1, 0x00, 0xdc, 0xc2, 0x02, 0xdc,
    0x42, 0x1b, 0xc2, 0x00, 0xdc, 0xc1, 0x01, 0xdc, 0xc4, 0xb0, 0x0b, 0x00,
    0x07, 0x8f, 0x00, 0x09, 0x82, 0xc0, 0x00, 0xdc, 0xc1, 0xb0, 0x36, 0x00,
    0x07, 0x8f, 0x00, 0x09, 0xaf, 0xc0, 0xb0, 0x0c, 0x00, 0x07, 0x8f, 0x00,
    0x09, 0xb0, 0x3d, 0x00, 0x07, 0x8f, 0x00, 0x09, 0xb0, 0x3d, 0x00, 0x07,
    0x8f, 0x00, 0x09, 0xb0, 0x4e, 0x00, 0x09, 0xb0, 0x4e, 0x00, 0x09, 0x86,
    0x00, 0x54, 0x00, 0x5b, 0xb0, 0x34, 0x00, 0x07, 0x8f, 0x00, 0x09, 0xb0,
    0x3c, 0x01, 0x09, 0x8f, 0x00, 0x09, 0xb0, 0x4b, 0x00, 0x09, 0xb0, 0x3c,
    0x01, 0x67, 0x00, 0x09, 0x8c, 0x03, 0x6b, 0xb0, 0x3b, 0x01, 0x76, 0x00,
    0x09, 0x8c, 0x03, 0x7a, 0xb0, 0x1b, 0x01, 0xdc, 0x9a, 0x00, 0xdc, 0x80,
    0x00, 0xdc, 0x80, 0x00, 0xd8, 0xb0, 0x06, 0x41, 0x81, 0x80, 0x00, 0x84,
    0x84, 0x03, 0x82, 0x81, 0x00, 0x82, 0x80, 0xc1, 0x00, 0x09, 0x80, 0xc1,
    0xb0, 0x0d, 0x00, 0xdc, 0xb0, 0x3f, 0x00, 0x07, 0x80, 0x01, 0x09, 0xb0,
    0x21, 0x00, 0xdc, 0xb2, 0x9e, 0xc2, 0xb3, 0x83, 0x00, 0x09, 0x9e, 0x00,
    0x09, 0xb0, 0x6c, 0x00, 0x09, 0x89, 0xc0, 0xb0, 0x9a, 0x00, 0xe4, 0xb0,
    0x5e, 0x00, 0xde, 0xc0, 0x00, 0xdc, 0xb0, 0xaa, 0xc0, 0x00, 0xdc, 0xb0,
    0x16, 0x00, 0x09, 0x93, 0xc7, 0x81, 0x00, 0xdc, 0xaf, 0xc4, 0x05, 0xdc,
    0xc1, 0x00, 0xdc, 0xb0, 0x45, 0x00, 0x07, 0x8e, 0x00, 0x09, 0xa5, 0xc0,
    0x00, 0xdc, 0xc6, 0xb0, 0x05, 0x01, 0x09, 0xb0, 0x09, 0x00, 0x07, 0x8a,
    0x01, 0x09, 0xb0, 0x12, 0x00, 0x07, 0xb0, 0x67, 0xc2, 0x41, 0x00, 0x04,
    0xdc, 0xc1, 0x03, 0xdc, 0xc0, 0x41, 0x00, 0x05, 0x01, 0x83, 0x00, 0xdc,
    0x85, 0xc0, 0x82, 0xc1, 0xb0, 0x95, 0xc1, 0x00, 0xdc, 0xc6, 0x00, 0xdc,
    0xc1, 0x00, 0xea, 0x00, 0xd6, 0x00, 0xdc, 0x00, 0xca, 0xe4, 0x00, 0xe8,
    0x01, 0xe4, 0x00, 0xdc, 0x80, 0xc0, 0x00, 0xe9, 0x00, 0xdc, 0xc0, 0x00,
    0xdc, 0xb2, 0x9f, 0xc1, 0x01, 0x01, 0xc3, 0x02, 0x01, 0xc1, 0x83, 0xc0,
    0x82, 0x01, 0x01, 0xc0, 0x00, 0xdc, 0xc0, 0x01, 0x01, 0x03, 0xdc, 0xc0,
    0xb8, 0x03, 0xcd, 0xc2, 0xb0, 0x5c, 0x00, 0x09, 0xb0, 0x2f, 0xdf, 0xb1,
    0xf9, 0x00, 0xda, 0x00, 0xe4, 0x00, 0xe8, 0x00, 0xde, 0x01, 0xe0, 0xb0,
    0x38, 0x01, 0x08, 0xb8, 0x6d, 0xa3, 0xc0, 0x83, 0xc9, 0x9f, 0xc1, 0xb0,
    0x1f, 0xc1, 0xb0, 0xe3, 0x00, 0x09, 0xb0, 0x8c, 0x00, 0x09, 0x9a, 0xd1,
    0xb0, 0x08, 0x02, 0xdc, 0xa4, 0x00, 0x09, 0xb0, 0x2e, 0x00, 0x07, 0x8b,
    0x00, 0x09, 0xb0, 0xbe, 0xc0, 0x80, 0xc1, 0x00, 0xdc, 0x81, 0xc1, 0x84,
    0xc1, 0x80, 0xc0, 0xb0, 0x03, 0x00, 0x09, 0xb0, 0xc5, 0x00, 0x09, 0xb8,
    0x46, 0xff, 0x00, 0x1a, 0xb2, 0xd0, 0xc6, 0x06, 0xdc, 0xc1, 0xb3, 0x9c,
    0x00, 0xdc, 0xb0, 0xb1, 0x00, 0xdc, 0xb0, 0x64, 0xc4, 0xb6, 0x61, 0x00,
    0xdc, 0x80, 0xc0, 0xa7, 0xc0, 0x00, 0x01, 0x00, 0xdc, 0x83, 0x00, 0x09,
    0xb0, 0x74, 0xc0, 0x00, 0xdc, 0xb2, 0x0c, 0xc3, 0xb1, 0xed, 0x01, 0xdc,
    0xc2, 0x00, 0xdc, 0xc0, 0x03, 0xdc, 0xb0, 0xc4, 0x00, 0x09, 0xb0, 0x07,
    0x00, 0x09, 0xb0, 0x08, 0x00, 0x09, 0x00, 0x07, 0xb0, 0x14, 0xc2, 0xaf,
    0x01, 0x09, 0xb0, 0x0d, 0x00, 0x07, 0xb0, 0x1b, 0x00, 0x09, 0x88, 0x00,
    0x07, 0xb0, 0x39, 0x00, 0x09, 0x00, 0x07, 0xb0, 0x81, 0x00, 0x07, 0x00,
    0x09, 0xb0, 0x1f, 0x01, 0x07, 0x8f, 0x00, 0x09, 0x97, 0xc6, 0x82, 0xc4,
    0xb0, 0x9c, 0x00, 0x09, 0x82, 0x00, 0x07, 0x96, 0xc0, 0xb0, 0x32, 0x00,
    0x09, 0x00, 0x07, 0xb0, 0xca, 0x00, 0x09, 0x00, 0x07, 0xb0, 0x4d, 0x00,
    0x09, 0xb0, 0x45, 0x00, 0x09, 0x00, 0x07, 0xb0, 0x42, 0x00, 0x09, 0xb0,
    0xdc, 0x00, 0x09, 0x00, 0x07, 0xb1, 0x74, 0x00, 0x09, 0xb0, 0x22, 0x00,
    0x09, 0x91, 0x00, 0x09, 0xb0, 0x20, 0x00, 0x09, 0xb1, 0x74, 0x00, 0x09,
    0xb0, 0xd1, 0x00, 0x07, 0x80, 0x01, 0x09, 0xb0, 0x20, 0x00, 0x09, 0xb8,
    0x45, 0x27, 0x04, 0x01, 0xb0, 0x0a, 0xc6, 0xb8, 0x49, 0x36, 0x00, 0x01,
    0xb8, 0x0c, 0x95, 0x01, 0xd8, 0x02, 0x01, 0x82, 0x00, 0xe2, 0x04, 0xd8,
    0x87, 0x07, 0xdc, 0x81, 0xc4, 0x01, 0xdc, 0x9d, 0xc3, 0xb0, 0x63, 0xc2,
    0xb8, 0x05, 0x8a, 0xc6, 0x80, 0xd0, 0x81, 0xc6, 0x80, 0xc1, 0x80, 0xc4,
    0xb0, 0xd4, 0xc6, 0xb1, 0x84, 0xc3, 0xb5, 0xaf, 0x06, 0xdc, 0xb0, 0x3c,
    0xc5, 0x00, 0x07,
};

static const uint8_t unicode_cc_index[78] = {
    0x4d, 0x03, 0x00, 0x97, 0x05, 0x20, 0xc6, 0x05, 0x00, 0xe7, 0x06, 0x00,
    0x45, 0x07, 0x00, 0xe2, 0x08, 0x00, 0x53, 0x09, 0x00, 0xcd, 0x0b, 0x20,
    0x38, 0x0e, 0x00, 0x73, 0x0f, 0x20, 0x5d, 0x13, 0x20, 0x60, 0x1a, 0x20,
    0xe6, 0x1b, 0x20, 0xfa, 0x1c, 0x00, 0x00, 0x1e, 0x20, 0x80, 0x2d, 0x00,
    0x06, 0xa8, 0x00, 0xbe, 0xaa, 0x00, 0x76, 0x03, 0x01, 0x4d, 0x0f, 0x01,
    0xcb, 0x11, 0x21, 0x5e, 0x14, 0x01, 0x3b, 0x18, 0x21, 0xf0, 0x6a, 0x41,
    0xaa, 0xd1, 0x01, 0x4b, 0xe9, 0x01,
};

static const uint32_t unicode_decomp_table1[687] = {
    0x00280081, 0x002a0097, 0x002a8081, 0x002bc097, 0x002c8115, 0x002d0097,
    0x002d4081, 0x002e0097, 0x002e4115, 0x002f0199, 0x00302016, 0x00400842,
    0x00448a42, 0x004a0442, 0x004c0096, 0x004c8117, 0x004d0242, 0x004e4342,
    0x004fc12f, 0x0050c342, 0x005240bf, 0x00530342, 0x00550942, 0x005a0842,
    0x005e0096, 0x005e4342, 0x005fc081, 0x00680142, 0x006bc142, 0x00710185,
    0x0071c317, 0x00734844, 0x00778344, 0x00798342, 0x007b02be, 0x007c4197,
    0x007d0142, 0x007e0444, 0x00800e42, 0x00878142, 0x00898744, 0x00ac0483,
    0x00b60317, 0x00b80283, 0x00d00214, 0x00d10096, 0x00dd0080, 0x00de8097,
    0x00df8080, 0x00e10097, 0x00e1413e, 0x00e1c080, 0x00e204be, 0x00ea83ae,
    0x00f282ae, 0x00f401ad, 0x00f4c12e, 0x00f54103, 0x00fc0303, 0x00fe4081,
    0x0100023e, 0x0101c0be, 0x010301be, 0x010640be, 0x010e40be, 0x0114023e,
    0x0115c0be, 0x011701be, 0x011d8144, 0x01304144, 0x01340244, 0x01358144,
    0x01368344, 0x01388344, 0x013a8644, 0x013e0144, 0x0161c085, 0x018882ae,
    0x019d422f, 0x01b00184, 0x01b4c084, 0x024a4084, 0x024c4084, 0x024d0084,
    0x0256042e, 0x0272c12e, 0x02770120, 0x0277c084, 0x028cc084, 0x028d8084,
    0x029641ae, 0x02978084, 0x02d20084, 0x02d2c12e, 0x02d70120, 0x02e50084,
    0x02f281ae, 0x03120084, 0x03300084, 0x0331c122, 0x0332812e, 0x035281ae,
    0x03768084, 0x037701ae, 0x038cc085, 0x03acc085, 0x03b7012f, 0x03c30081,
    0x03d0c084, 0x03d34084, 0x03d48084, 0x03d5c084, 0x03d70084, 0x03da4084,
    0x03dcc084, 0x03dd412e, 0x03ddc085, 0x03de0084, 0x03de4085, 0x03e04084,
    0x03e4c084, 0x03e74084, 0x03e88084, 0x03e9c084, 0x03eb0084, 0x03ee4084,
    0x04098084, 0x043f0081, 0x06c18484, 0x06c48084, 0x06cec184, 0x06d00120,
    0x06d0c084, 0x074b0383, 0x074cc41f, 0x074f1783, 0x075e0081, 0x0766d283,
    0x07801d44, 0x078e8942, 0x07931844, 0x079f0d42, 0x07a58216, 0x07a68085,
    0x07a6c0be, 0x07a80d44, 0x07aea044, 0x07c00122, 0x07c08344, 0x07c20122,
    0x07c28344, 0x07c40122, 0x07c48244, 0x07c60122, 0x07c68244, 0x07c8113e,
    0x07d08244, 0x07d20122, 0x07d28244, 0x07d40122, 0x07d48344, 0x07d64c3e,
    0x07dc4080, 0x07dc80be, 0x07dcc080, 0x07dd00be, 0x07dd4080, 0x07dd80be,
    0x07ddc080, 0x07de00be, 0x07de4080, 0x07de80be, 0x07dec080, 0x07df00be,
    0x07df4080, 0x07e00820, 0x07e40820, 0x07e80820, 0x07ec05be, 0x07eec080,
    0x07ef00be, 0x07ef4097, 0x07ef8080, 0x07efc117, 0x07f0443e, 0x07f24080,
    0x07f280be, 0x07f2c080, 0x07f303be, 0x07f4c080, 0x07f582ae, 0x07f6c080,
    0x07f7433e, 0x07f8c080, 0x07f903ae, 0x07fac080, 0x07fb013e, 0x07fb8102,
    0x07fc83be, 0x07fe4080, 0x07fe80be, 0x07fec080, 0x07ff00be, 0x07ff4080,
    0x07ff8097, 0x0800011e, 0x08008495, 0x08044081, 0x0805c097, 0x08090081,
    0x08094097, 0x08098099, 0x080bc081, 0x080cc085, 0x080d00b1, 0x080d8085,
    0x080dc0b1, 0x080f0197, 0x0811c197, 0x0815c0b3, 0x0817c081, 0x081c0595,
    0x081ec081, 0x081f0215, 0x0820051f, 0x08228583, 0x08254415, 0x082a0097,
    0x08400119, 0x08408081, 0x0840c0bf, 0x08414119, 0x0841c081, 0x084240bf,
    0x0842852d, 0x08454081, 0x08458097, 0x08464295, 0x08480097, 0x08484099,
    0x08488097, 0x08490081, 0x08498080, 0x084a0081, 0x084a8102, 0x084b0495,
    0x084d421f, 0x084e4081, 0x084ec099, 0x084f0283, 0x08514295, 0x08540119,
    0x0854809b, 0x0854c619, 0x0857c097, 0x08580081, 0x08584097, 0x08588099,
    0x0858c097, 0x08590081, 0x08594097, 0x08598099, 0x0859c09b, 0x085a0097,
    0x085a4081, 0x085a8097, 0x085ac099, 0x085b0295, 0x085c4097, 0x085c8099,
    0x085cc097, 0x085d0081, 0x085d4097, 0x085d8099, 0x085dc09b, 0x085e0097,
    0x085e4081, 0x085e8097, 0x085ec099, 0x085f0215, 0x08624099, 0x0866813e,
    0x086b80be, 0x087341be, 0x088100be, 0x088240be, 0x088300be, 0x088901be,
    0x088b0085, 0x088b40b1, 0x088bc085, 0x088c00b1, 0x089040be, 0x089100be,
    0x0891c1be, 0x089801be, 0x089b42be, 0x089d0144, 0x089e0144, 0x08a00144,
    0x08a10144, 0x08a20144, 0x08ab023e, 0x08b80244, 0x08ba8220, 0x08ca411e,
    0x0918049f, 0x091a4523, 0x091cc097, 0x091d04a5, 0x091f452b, 0x0921c09b,
    0x092204a1, 0x09244525, 0x0926c099, 0x09270d25, 0x092d8d1f, 0x09340d1f,
    0x093a8081, 0x0a8300b3, 0x0a9d0099, 0x0a9d4097, 0x0a9d8099, 0x0ab700be,
    0x0b1f0115, 0x0b5bc081, 0x0ba7c081, 0x0bbcc081, 0x0bc004ad, 0x0bc244ad,
    0x0bc484ad, 0x0bc6f383, 0x0be0852d, 0x0be31d03, 0x0bf1882d, 0x0c000081,
    0x0c0d8283, 0x0c130b84, 0x0c194284, 0x0c1c0122, 0x0c1cc122, 0x0c1d8122,
    0x0c1e4122, 0x0c1f0122, 0x0c250084, 0x0c26c123, 0x0c278084, 0x0c27c085,
    0x0c2b0b84, 0x0c314284, 0x0c340122, 0x0c34c122, 0x0c358122, 0x0c364122,
    0x0c370122, 0x0c3d0084, 0x0c3dc220, 0x0c3f8084, 0x0c3fc085, 0x0c4c4a2d,
    0x0c51451f, 0x0c53ca9f, 0x0c5915ad, 0x0c648703, 0x0c800741, 0x0c838089,
    0x0c83c129, 0x0c8441a9, 0x0c850089, 0x0c854129, 0x0c85c2a9, 0x0c870089,
    0x0c87408f, 0x0c87808d, 0x0c881241, 0x0c910203, 0x0c940099, 0x0c9444a3,
    0x0c968323, 0x0c98072d, 0x0c9b84af, 0x0c9dc2a1, 0x0c9f00b5, 0x0c9f40b3,
    0x0c9f8085, 0x0ca01883, 0x0cac4223, 0x0cad4523, 0x0cafc097, 0x0cb004a1,
    0x0cb241a5, 0x0cb30097, 0x0cb34099, 0x0cb38097, 0x0cb3c099, 0x0cb417ad,
    0x0cbfc085, 0x0cc001b3, 0x0cc0c0b1, 0x0cc100b3, 0x0cc14131, 0x0cc1c0b5,
    0x0cc200b3, 0x0cc241b1, 0x0cc30133, 0x0cc38131, 0x0cc40085, 0x0cc440b1,
    0x0cc48133, 0x0cc50085, 0x0cc540b5, 0x0cc580b7, 0x0cc5c0b5, 0x0cc600b1,
    0x0cc64135, 0x0cc6c0b3, 0x0cc701b1, 0x0cc7c0b3, 0x0cc800b5, 0x0cc840b3,
    0x0cc881b1, 0x0cc9422f, 0x0cca4131, 0x0ccac0b5, 0x0ccb00b1, 0x0ccb40b3,
    0x0ccb80b5, 0x0ccbc0b1, 0x0ccc012f, 0x0ccc80b5, 0x0cccc0b3, 0x0ccd00b5,
    0x0ccd40b1, 0x0ccd80b5, 0x0ccdc085, 0x0cce02b1, 0x0ccf40b3, 0x0ccf80b1,
    0x0ccfc085, 0x0cd001b1, 0x0cd0c0b3, 0x0cd101b1, 0x0cd1c0b5, 0x0cd200b3,
    0x0cd24085, 0x0cd280b5, 0x0cd2c085, 0x0cd30133, 0x0cd381b1, 0x0cd440b3,
    0x0cd48085, 0x0cd4c0b1, 0x0cd500b3, 0x0cd54085, 0x0cd580b5, 0x0cd5c0b1,
    0x0cd60521, 0x0cd88525, 0x0cdb02a5, 0x0cdc4099, 0x0cdc8117, 0x0cdd0099,
    0x0cdd4197, 0x0cde0127, 0x0cde8285, 0x0cdfc089, 0x0ce0043f, 0x0ce20099,
    0x0ce2409b, 0x0ce283bf, 0x0ce44219, 0x0ce54205, 0x0ce6433f, 0x0ce7c131,
    0x0ce84085, 0x0ce881b1, 0x0ce94085, 0x0ce98107, 0x0cea0089, 0x0cea4097,
    0x0cea8219, 0x0ceb809d, 0x0cebc08d, 0x0cec083f, 0x0cf00105, 0x0cf0809b,
    0x0cf0c197, 0x0cf1809b, 0x0cf1c099, 0x0cf20517, 0x0cf48099, 0x0cf4c117,
    0x0cf54119, 0x0cf5c097, 0x0cf6009b, 0x0cf64099, 0x0cf68217, 0x0cf78119,
    0x0cf804a1, 0x0cfa4525, 0x0cfcc525, 0x0cff4125, 0x0cffc099, 0x29a70103,
    0x29dc0081, 0x29fe0103, 0x2ad70203, 0x3e401482, 0x3e4a7f82, 0x3e6a3f82,
    0x3e8aa102, 0x3e9b0110, 0x3e9c2f82, 0x3eb3c590, 0x3ec00197, 0x3ec0c119,
    0x3ec1413f, 0x3ec4c2af, 0x3ec74184, 0x3ec804ad, 0x3eca4081, 0x3eca8304,
    0x3ecc03a0, 0x3ece02a0, 0x3ecf8084, 0x3ed00120, 0x3ed0c120, 0x3ed184ae,
    0x3ed3c085, 0x3ed4312d, 0x3ef4cbad, 0x3efa892f, 0x3eff022d, 0x3f002f2f,
    0x3f1782a5, 0x3f18c0b1, 0x3f1907af, 0x3f1cffaf, 0x3f3c81a5, 0x3f3d64af,
    0x3f542031, 0x3f649b31, 0x3f7c0131, 0x3f7c83b3, 0x3f7e40b1, 0x3f7e80bd,
    0x3f7ec0bb, 0x3f7f00b3, 0x3f840503, 0x3f8c01ad, 0x3f8cc315, 0x3f8e462d,
    0x3f91cc03, 0x3f97c695, 0x3f9c01af, 0x3f9d0085, 0x3f9d852f, 0x3fa03aad,
    0x3fbd442f, 0x3fc06f1f, 0x3fd7c11f, 0x3fd85fad, 0x3fe80081, 0x3fe84f1f,
    0x3ff0831f, 0x3ff2831f, 0x3ff4831f, 0x3ff6819f, 0x3ff80783, 0x44268192,
    0x442ac092, 0x444b8112, 0x44d2c112, 0x452ec212, 0x456e8112, 0x74578392,
    0x746ec312, 0x75000d1f, 0x75068d1f, 0x750d0d1f, 0x7513839f, 0x7515891f,
    0x751a0d1f, 0x75208d1f, 0x75271015, 0x752f439f, 0x7531459f, 0x75340d1f,
    0x753a8d1f, 0x75410395, 0x7543441f, 0x7545839f, 0x75478d1f, 0x754e0795,
    0x7552839f, 0x75548d1f, 0x755b0d1f, 0x75618d1f, 0x75680d1f, 0x756e8d1f,
    0x75750d1f, 0x757b8d1f, 0x75820d1f, 0x75888d1f, 0x758f0d1f, 0x75958d1f,
    0x759c0d1f, 0x75a28d1f, 0x75a90103, 0x75aa089f, 0x75ae4081, 0x75ae839f,
    0x75b04081, 0x75b08c9f, 0x75b6c081, 0x75b7032d, 0x75b8889f, 0x75bcc081,
    0x75bd039f, 0x75bec081, 0x75bf0c9f, 0x75c54081, 0x75c5832d, 0x75c7089f,
    0x75cb4081, 0x75cb839f, 0x75cd4081, 0x75cd8c9f, 0x75d3c081, 0x75d4032d,
    0x75d5889f, 0x75d9c081, 0x75da039f, 0x75dbc081, 0x75dc0c9f, 0x75e24081,
    0x75e2832d, 0x75e4089f, 0x75e84081, 0x75e8839f, 0x75ea4081, 0x75ea8c9f,
    0x75f0c081, 0x75f1042d, 0x75f3851f, 0x75f6051f, 0x75f8851f, 0x75fb051f,
    0x75fd851f, 0x7b80022d, 0x7b814dad, 0x7b884203, 0x7b89c081, 0x7b8a452d,
    0x7b8d0403, 0x7b908081, 0x7b91dc03, 0x7ba0052d, 0x7ba2c8ad, 0x7ba84483,
    0x7baac8ad, 0x7c400097, 0x7c404521, 0x7c440d25, 0x7c4a8087, 0x7c4ac115,
    0x7c4b4117, 0x7c4c0d1f, 0x7c528217, 0x7c538099, 0x7c53c097, 0x7c5a8197,
    0x7c640097, 0x7c80012f, 0x7c808081, 0x7c841603, 0x7c9004c1, 0x7c940103,
    0xbe0001ac, 0xbe00d110, 0xbe0947ac, 0xbe0d3910, 0xbe29872c, 0xbe2d022c,
    0xbe2e3790, 0xbe49ff90, 0xbe69bc10,
};

static const uint16_t unicode_decomp_table2[687] = {
    0x0020, 0x0000, 0x0061, 0x0002, 0x0004, 0x0006, 0x03bc, 0x0008, 0x000a,
    0x000c, 0x0015, 0x0095, 0x00a5, 0x00b9, 0x00c1, 0x00c3, 0x00c7, 0x00cb,
    0x00d1, 0x00d7, 0x00dd, 0x00e0, 0x00e6, 0x00f8, 0x0108, 0x010a, 0x0073,
    0x0110, 0x0112, 0x0114, 0x0120, 0x012c, 0x0144, 0x014d, 0x0153, 0x0162,
    0x0168, 0x016a, 0x0176, 0x0192, 0x0194, 0x01a9, 0x01bb, 0x01c7, 0x01d1,
    0x01d5, 0x02b9, 0x01d7, 0x003b, 0x01d9, 0x01db, 0x00b7, 0x01e1, 0x01fc,
    0x020c, 0x0218, 0x021d, 0x0223, 0x0227, 0x03a3, 0x0233, 0x023f, 0x0242,
    0x024b, 0x024e, 0x0251, 0x025d, 0x0260, 0x0269, 0x026c, 0x026f, 0x0275,
    0x0278, 0x0281, 0x028a, 0x029c, 0x029f, 0x02a3, 0x02af, 0x02b9, 0x02c5,
    0x02c9, 0x02cd, 0x02d1, 0x02d5, 0x02e7, 0x02ed, 0x02f1, 0x02f5, 0x02f9,
    0x02fd, 0x0305, 0x0309, 0x030d, 0x0313, 0x0317, 0x031b, 0x0323, 0x0327,
    0x032b, 0x032f, 0x0335, 0x033d, 0x0341, 0x0349, 0x034d, 0x0351, 0x0f0b,
    0x0357, 0x035b, 0x035f, 0x0363, 0x0367, 0x036b, 0x036f, 0x0373, 0x0379,
    0x037d, 0x0381, 0x0385, 0x0389, 0x038d, 0x0391, 0x0395, 0x0399, 0x039d,
    0x03a1, 0x10dc, 0x03a5, 0x03c9, 0x03cd, 0x03d9, 0x03dd, 0x03e1, 0x03ef,
    0x03f1, 0x043d, 0x044f, 0x0499, 0x04f0, 0x0502, 0x054a, 0x0564, 0x056c,
    0x0570, 0x0573, 0x059a, 0x05fa, 0x05fe, 0x0607, 0x060b, 0x0614, 0x0618,
    0x061e, 0x0622, 0x0628, 0x068e, 0x0694, 0x0698, 0x069e, 0x06a2, 0x06ab,
    0x03ac, 0x06f3, 0x03ad, 0x06f6, 0x03ae, 0x06f9, 0x03af, 0x06fc, 0x03cc,
    0x06ff, 0x03cd, 0x0702, 0x03ce, 0x0705, 0x0709, 0x070d, 0x0711, 0x0386,
    0x0732, 0x0735, 0x03b9, 0x0737, 0x073b, 0x0388, 0x0753, 0x0389, 0x0756,
    0x0390, 0x076b, 0x038a, 0x0777, 0x03b0, 0x0789, 0x038e, 0x0799, 0x079f,
    0x07a3, 0x038c, 0x07b8, 0x038f, 0x07bb, 0x00b4, 0x07be, 0x07c0, 0x07c2,
    0x2010, 0x07cb, 0x002e, 0x07cd, 0x07cf, 0x0020, 0x07d2, 0x07d6, 0x07db,
    0x07df, 0x07e4, 0x07ea, 0x07f0, 0x0020, 0x07f6, 0x2212, 0x0801, 0x0805,
    0x0807, 0x081d, 0x0825, 0x0827, 0x0043, 0x082d, 0x0830, 0x0190, 0x0836,
    0x0839, 0x004e, 0x0845, 0x0847, 0x084c, 0x084e, 0x0851, 0x005a, 0x03a9,
    0x005a, 0x0853, 0x0857, 0x0860, 0x0069, 0x0862, 0x0865, 0x086f, 0x0874,
    0x087a, 0x087e, 0x08a2, 0x0049, 0x08a4, 0x08a6, 0x08a9, 0x0056, 0x08ab,
    0x08ad, 0x08b0, 0x08b4, 0x0058, 0x08b6, 0x08b8, 0x08bb, 0x08c0, 0x08c2,
    0x08c5, 0x0076, 0x08c7, 0x08c9, 0x08cc, 0x08d0, 0x0078, 0x08d2, 0x08d4,
    0x08d7, 0x08db, 0x08de, 0x08e4, 0x08e7, 0x08f0, 0x08f3, 0x08f6, 0x08f9,
    0x0902, 0x0906, 0x090b, 0x090f, 0x0914, 0x0917, 0x091a, 0x0923, 0x092c,
    0x093b, 0x093e, 0x0941, 0x0944, 0x0947, 0x094a, 0x0956, 0x095c, 0x0960,
    0x0962, 0x0964, 0x0968, 0x096a, 0x0970, 0x0978, 0x097c, 0x0980, 0x0986,
    0x0989, 0x098f, 0x0991, 0x0030, 0x0993, 0x0999, 0x099c, 0x099e, 0x09a1,
    0x09a4, 0x2d61, 0x6bcd, 0x9f9f, 0x09a6, 0x09b1, 0x09bc, 0x09c7, 0x0a95,
    0x0aa1, 0x0b15, 0x0020, 0x0b27, 0x0b31, 0x0b8d, 0x0ba1, 0x0ba5, 0x0ba9,
    0x0bad, 0x0bb1, 0x0bb5, 0x0bb9, 0x0bbd, 0x0bc1, 0x0bc5, 0x0c21, 0x0c35,
    0x0c39, 0x0c3d, 0x0c41, 0x0c45, 0x0c49, 0x0c4d, 0x0c51, 0x0c55, 0x0c59,
    0x0c6f, 0x0c71, 0x0c73, 0x0ca0, 0x0cbc, 0x0cdc, 0x0ce4, 0x0cec, 0x0cf4,
    0x0cfc, 0x0d04, 0x0d0c, 0x0d14, 0x0d22, 0x0d2e, 0x0d7a, 0x0d82, 0x0d85,
    0x0d89, 0x0d8d, 0x0d9d, 0x0db1, 0x0db5, 0x0dbc, 0x0dc2, 0x0dc6, 0x0e28,
    0x0e2c, 0x0e30, 0x0e32, 0x0e36, 0x0e3c, 0x0e3e, 0x0e41, 0x0e43, 0x0e46,
    0x0e77, 0x0e7b, 0x0e89, 0x0e8e, 0x0e94, 0x0e9c, 0x0ea3, 0x0ea9, 0x0eb4,
    0x0ebe, 0x0ec6, 0x0eca, 0x0ecf, 0x0ed9, 0x0edd, 0x0ee4, 0x0eec, 0x0ef3,
    0x0ef8, 0x0f04, 0x0f0a, 0x0f15, 0x0f1b, 0x0f22, 0x0f28, 0x0f33, 0x0f3d,
    0x0f45, 0x0f4c, 0x0f51, 0x0f57, 0x0f5e, 0x0f63, 0x0f69, 0x0f70, 0x0f76,
    0x0f7d, 0x0f82, 0x0f89, 0x0f8d, 0x0f9e, 0x0fa4, 0x0fa9, 0x0fad, 0x0fb8,
    0x0fbe, 0x0fc9, 0x0fd0, 0x0fd6, 0x0fda, 0x0fe1, 0x0fe5, 0x0fef, 0x0ffa,
    0x1000, 0x1004, 0x1009, 0x100f, 0x1013, 0x101a, 0x101f, 0x1023, 0x1029,
    0x102f, 0x1032, 0x1036, 0x1039, 0x103f, 0x1045, 0x1059, 0x1061, 0x1079,
    0x107c, 0x1080, 0x1095, 0x10a1, 0x10b1, 0x10c3, 0x10cb, 0x10cf, 0x10da,
    0x10de, 0x10ea, 0x10f2, 0x10f4, 0x1100, 0x1105, 0x1111, 0x1141, 0x1149,
    0x114d, 0x1153, 0x1157, 0x115a, 0x116e, 0x1171, 0x1175, 0x117b, 0x117d,
    0x1181, 0x1184, 0x118c, 0x1192, 0x1196, 0x119c, 0x11a2, 0x11a8, 0x11ab,
    0xa76f, 0x11af, 0x11b3, 0x11bb, 0x120d, 0x130b, 0x1409, 0x148d, 0x1492,
    0x1550, 0x1569, 0x156f, 0x1575, 0x157b, 0x1587, 0x1593, 0x002b, 0x159e,
    0x15b6, 0x15ba, 0x15be, 0x15c2, 0x15c6, 0x15ca, 0x15de, 0x15e2, 0x1646,
    0x165f, 0x1685, 0x168b, 0x1749, 0x174f, 0x1754, 0x1774, 0x1874, 0x187a,
    0x190e, 0x19d0, 0x1a74, 0x1a7c, 0x1a9a, 0x1a9f, 0x1ab3, 0x1abd, 0x1ac3,
    0x1ad7, 0x1adc, 0x1ae2, 0x1af0, 0x1b20, 0x1b2d, 0x1b35, 0x1b39, 0x1b4f,
    0x1bc6, 0x1bd8, 0x1bda, 0x1bdc, 0x3164, 0x1c1d, 0x1c1f, 0x1c21, 0x1c23,
    0x1c25, 0x1c27, 0x1c45, 0x1c53, 0x1c58, 0x1c61, 0x1c6a, 0x1c7c, 0x1c85,
    0x1ca5, 0x1cc0, 0x1cc2, 0x1cc4, 0x1cc6, 0x1cc8, 0x1cca, 0x1ccc, 0x1cce,
    0x1cee, 0x1cf0, 0x1cf2, 0x1cf4, 0x1cf6, 0x1cfd, 0x1cff, 0x1d01, 0x1d03,
    0x1d12, 0x1d14, 0x1d16, 0x1d18, 0x1d1a, 0x1d1c, 0x1d1e, 0x1d20, 0x1d22,
    0x1d24, 0x1d26, 0x1d28, 0x1d2a, 0x1d2c, 0x1d2e, 0x1d32, 0x03f4, 0x1d34,
    0x2207, 0x1d36, 0x2202, 0x1d38, 0x1d40, 0x03f4, 0x1d42, 0x2207, 0x1d44,
    0x2202, 0x1d46, 0x1d4e, 0x03f4, 0x1d50, 0x2207, 0x1d52, 0x2202, 0x1d54,
    0x1d5c, 0x03f4, 0x1d5e, 0x2207, 0x1d60, 0x2202, 0x1d62, 0x1d6a, 0x03f4,
    0x1d6c, 0x2207, 0x1d6e, 0x2202, 0x1d70, 0x1d7a, 0x1d7c, 0x1d7e, 0x1d80,
    0x1d82, 0x1d84, 0x1d8a, 0x1da7, 0x062d, 0x1daf, 0x1dbb, 0x062c, 0x1dcb,
    0x1e3b, 0x1e47, 0x1e5a, 0x1e6c, 0x1e7f, 0x1e81, 0x1e85, 0x1e8b, 0x1e91,
    0x1e93, 0x1e97, 0x1e99, 0x1ea1, 0x1ea4, 0x1ea6, 0x1eac, 0x1eae, 0x30b5,
    0x1eb4, 0x1f0c, 0x1f22, 0x1f26, 0x1f2b, 0x1f78, 0x1f89, 0x208a, 0x209a,
    0x20a0, 0x219a, 0x22b8,
};

static const uint8_t unicode_decomp_data[9158] = {
    0x20, 0x88, 0x20, 0x84, 0x32, 0x33, 0x20, 0x81, 0x20, 0xa7, 0x31, 0x6f,
    0x31, 0xd0, 0x34, 0x31, 0xd0, 0x32, 0x33, 0xd0, 0x34, 0x41, 0x80, 0x41,
    0x81, 0x41, 0x82, 0x41, 0x83, 0x41, 0x88, 0x41, 0x8a, 0x00, 0x00, 0x43,
    0xa7, 0x45, 0x80, 0x45, 0x81, 0x45, 0x82, 0x45, 0x88, 0x49, 0x80, 0x49,
    0x81, 0x49, 0x82, 0x49, 0x88, 0x00, 0x00, 0x4e, 0x83, 0x4f, 0x80, 0x4f,
    0x81, 0x4f, 0x82, 0x4f, 0x83, 0x4f, 0x88, 0x00, 0x00, 0x00, 0x00, 0x55,
    0x80, 0x55, 0x81, 0x55, 0x82, 0x55, 0x88, 0x59, 0x81, 0x00, 0x00, 0x00,
    0x00, 0x61, 0x80, 0x61, 0x81, 0x61, 0x82, 0x61, 0x83, 0x61, 0x88, 0x61,
    0x8a, 0x00, 0x00, 0x63, 0xa7, 0x65, 0x80, 0x65, 0x81, 0x65, 0x82, 0x65,
    0x88, 0x69, 0x80, 0x69, 0x81, 0x69, 0x82, 0x69, 0x88, 0x00, 0x00, 0x6e,
    0x83, 0x6f, 0x80, 0x6f, 0x81, 0x6f, 0x82, 0x6f, 0x83, 0x6f, 0x88, 0x00,
    0x00, 0x00, 0x00, 0x75, 0x80, 0x75, 0x81, 0x75, 0x82, 0x75, 0x88, 0x79,
    0x81, 0x00, 0x00, 0x79, 0x88, 0x41, 0x84, 0x41, 0x86, 0x41, 0xa8, 0x43,
    0x81, 0x43, 0x82, 0x43, 0x87, 0x43, 0x8c, 0x44, 0x8c, 0x45, 0x84, 0x45,
    0x86, 0x45, 0x87, 0x45, 0xa8, 0x45, 0x8c, 0x47, 0x82, 0x47, 0x86, 0x47,
    0x87, 0x47, 0xa7, 0x48, 0x82, 0x49, 0x83, 0x49, 0x84, 0x49, 0x86, 0x49,
    0xa8, 0x49, 0x87, 0x49, 0x4a, 0x69, 0x6a, 0x4a, 0x82, 0x4b, 0xa7, 0x4c,
    0x81, 0x4c, 0xa7, 0x4c, 0x8c, 0x4c, 0x00, 0x00, 0x6b, 0x20, 0x6b, 0x4e,
    0x81, 0x4e, 0xa7, 0x4e, 0x8c, 0xbc, 0x02, 0x6e, 0x4f, 0x84, 0x4f, 0x86,
    0x4f, 0x8b, 0x52, 0x81, 0x52, 0xa7, 0x52, 0x8c, 0x53, 0x81, 0x53, 0x82,
    0x53, 0xa7, 0x53, 0x8c, 0x54, 0xa7, 0x54, 0x8c, 0x55, 0x83, 0x55, 0x84,
    0x55, 0x86, 0x55, 0x8a, 0x55, 0x8b, 0x55, 0xa8, 0x57, 0x82, 0x59, 0x82,
    0x59, 0x88, 0x5a, 0x81, 0x5a, 0x87, 0x5a, 0x8c, 0x4f, 0x9b, 0x55, 0x9b,
    0x44, 0x00, 0x7d, 0x01, 0x44, 0x00, 0x7e, 0x01, 0x64, 0x00, 0x7e, 0x01,
    0x4c, 0x4a, 0x4c, 0x6a, 0x6c, 0x6a, 0x4e, 0x4a, 0x4e, 0x6a, 0x6e, 0x6a,
    0x41, 0x00, 0x8c, 0x49, 0x00, 0x8c, 0x4f, 0x00, 0x8c, 0x55, 0x00, 0x8c,
    0xdc, 0x00, 0x84, 0xdc, 0x00, 0x81, 0xdc, 0x00, 0x8c, 0xdc, 0x00, 0x80,
    0xc4, 0x00, 0x84, 0x26, 0x02, 0x84, 0xc6, 0x00, 0x84, 0x47, 0x8c, 0x4b,
    0x8c, 0x4f, 0xa8, 0xea, 0x01, 0x84, 0xeb, 0x01, 0x84, 0xb7, 0x01, 0x8c,
    0x92, 0x02, 0x8c, 0x6a, 0x00, 0x8c, 0x44, 0x5a, 0x44, 0x7a, 0x64, 0x7a,
    0x47, 0x81, 0x4e, 0x00, 0x80, 0xc5, 0x00, 0x81, 0xc6, 0x00, 0x81, 0xd8,
    0x00, 0x81, 0x41, 0x8f, 0x41, 0x91, 0x45, 0x8f, 0x45, 0x91, 0x49, 0x8f,
    0x49, 0x91, 0x4f, 0x8f, 0x4f, 0x91, 0x52, 0x8f, 0x52, 0x91, 0x55, 0x8f,
    0x55, 0x91, 0x53, 0xa6, 0x54, 0xa6, 0x48, 0x8c, 0x41, 0x00, 0x87, 0x45,
    0x00, 0xa7, 0xd6, 0x00, 0x84, 0xd5, 0x00, 0x84, 0x4f, 0x00, 0x87, 0x2e,
    0x02, 0x84, 0x59, 0x00, 0x84, 0x68, 0x00, 0x66, 0x02, 0x6a, 0x00, 0x72,
    0x00, 0x79, 0x02, 0x7b, 0x02, 0x81, 0x02, 0x77, 0x00, 0x79, 0x00, 0x20,
    0x86, 0x20, 0x87, 0x20, 0x8a, 0x20, 0xa8, 0x20, 0x83, 0x20, 0x8b, 0x63,
    0x02, 0x6c, 0x00, 0x73, 0x00, 0x78, 0x00, 0x95, 0x02, 0x80, 0x81, 0x00,
    0x93, 0x88, 0x81, 0x20, 0xc5, 0x20, 0x81, 0xa8, 0x00, 0x81, 0x91, 0x03,
    0x81, 0x95, 0x03, 0x81, 0x97, 0x03, 0x81, 0x99, 0x03, 0x81, 0x00, 0x00,
    0x00, 0x9f, 0x03, 0x81, 0x00, 0x00, 0x00, 0xa5, 0x03, 0x81, 0xa9, 0x03,
    0x81, 0xca, 0x03, 0x81, 0x01, 0x03, 0x98, 0x07, 0xa4, 0x07, 0xb0, 0x00,
    0xb4, 0x00, 0xb6, 0x00, 0xb8, 0x00, 0xca, 0x00, 0x01, 0x03, 0xb8, 0x07,
    0xc4, 0x07, 0xbe, 0x00, 0xc4, 0x00, 0xc8, 0x00, 0xa5, 0x03, 0x0d, 0x13,
    0x00, 0x01, 0x03, 0xd1, 0x00, 0xd1, 0x07, 0xc6, 0x03, 0xc0, 0x03, 0xba,
    0x03, 0xc1, 0x03, 0xc2, 0x03, 0x00, 0x00, 0x98, 0x03, 0xb5, 0x03, 0x15,
    0x04, 0x80, 0x15, 0x04, 0x88, 0x00, 0x00, 0x00, 0x13, 0x04, 0x81, 0x06,
    0x04, 0x88, 0x1a, 0x04, 0x81, 0x18, 0x04, 0x80, 0x23, 0x04, 0x86, 0x18,
    0x04, 0x86, 0x38, 0x04, 0x86, 0x35, 0x04, 0x80, 0x35, 0x04, 0x88, 0x00,
    0x00, 0x00, 0x33, 0x04, 0x81, 0x56, 0x04, 0x88, 0x3a, 0x04, 0x81, 0x38,
    0x04, 0x80, 0x43, 0x04, 0x86, 0x74, 0x04, 0x8f, 0x16, 0x04, 0x86, 0x10,
    0x04, 0x86, 0x10, 0x04, 0x88, 0x15, 0x04, 0x86, 0xd8, 0x04, 0x88, 0x16,
    0x04, 0x88, 0x17, 0x04, 0x88, 0x18, 0x04, 0x84, 0x18, 0x04, 0x88, 0x1e,
    0x04, 0x88, 0xe8, 0x04, 0x88, 0x2d, 0x04, 0x88, 0x23, 0x04, 0x84, 0x23,
    0x04, 0x88, 0x23, 0x04, 0x8b, 0x27, 0x04, 0x88, 0x2b, 0x04, 0x88, 0x65,
    0x05, 0x82, 0x05, 0x27, 0x06, 0x00, 0x2c, 0x00, 0x2d, 0x21, 0x2d, 0x00,
    0x2e, 0x23, 0x2d, 0x27, 0x06, 0x00, 0x4d, 0x21, 0x4d, 0xa0, 0x4d, 0x23,
    0x4d, 0xd5, 0x06, 0x54, 0x06, 0x00, 0x00, 0x00, 0x00, 0xc1, 0x06, 0x54,
    0x06, 0xd2, 0x06, 0x54, 0x06, 0x28, 0x09, 0x3c, 0x09, 0x30, 0x09, 0x3c,
    0x09, 0x33, 0x09, 0x3c, 0x09, 0x15, 0x09, 0x00, 0x27, 0x01, 0x27, 0x02,
    0x27, 0x07, 0x27, 0x0c, 0x27, 0x0d, 0x27, 0x16, 0x27, 0x1a, 0x27, 0xbe,
    0x09, 0x09, 0x00, 0x09, 0x19, 0xa1, 0x09, 0xbc, 0x09, 0xaf, 0x09, 0xbc,
    0x09, 0x32, 0x0a, 0x3c, 0x0a, 0x38, 0x0a, 0x3c, 0x0a, 0x16, 0x0a, 0x00,
    0x26, 0x01, 0x26, 0x06, 0x26, 0x2b, 0x0a, 0x3c, 0x0a, 0x47, 0x0b, 0x56,
    0x0b, 0x3e, 0x0b, 0x09, 0x00, 0x09, 0x19, 0x21, 0x0b, 0x3c, 0x0b, 0x92,
    0x0b, 0xd7, 0x0b, 0xbe, 0x0b, 0x08, 0x00, 0x09, 0x00, 0x08, 0x19, 0x46,
    0x0c, 0x56, 0x0c, 0xbf, 0x0c, 0xd5, 0x0c, 0xc6, 0x0c, 0xd5, 0x0c, 0xc2,
    0x0c, 0x04, 0x00, 0x08, 0x13, 0x3e, 0x0d, 0x08, 0x00, 0x09, 0x00, 0x08,
    0x19, 0xd9, 0x0d, 0xca, 0x0d, 0xca, 0x0d, 0x0f, 0x05, 0x12, 0x00, 0x0f,
    0x15, 0x4d, 0x0e, 0x32, 0x0e, 0xcd, 0x0e, 0xb2, 0x0e, 0x99, 0x0e, 0x12,
    0x00, 0x12, 0x08, 0x42, 0x0f, 0xb7, 0x0f, 0x4c, 0x0f, 0xb7, 0x0f, 0x51,
    0x0f, 0xb7, 0x0f, 0x56, 0x0f, 0xb7, 0x0f, 0x5b, 0x0f, 0xb7, 0x0f, 0x40,
    0x0f, 0xb5, 0x0f, 0x71, 0x0f, 0x72, 0x0f, 0x71, 0x0f, 0x00, 0x03, 0x41,
    0x0f, 0xb2, 0x0f, 0x81, 0x0f, 0xb3, 0x0f, 0x80, 0x0f, 0xb3, 0x0f, 0x81,
    0x0f, 0x71, 0x0f, 0x80, 0x0f, 0x92, 0x0f, 0xb7, 0x0f, 0x9c, 0x0f, 0xb7,
    0x0f, 0xa1, 0x0f, 0xb7, 0x0f, 0xa6, 0x0f, 0xb7, 0x0f, 0xab, 0x0f, 0xb7,
    0x0f, 0x90, 0x0f, 0xb5, 0x0f, 0x25, 0x10, 0x2e, 0x10, 0x05, 0x1b, 0x35,
    0x1b, 0x00, 0x00, 0x00, 0x00, 0x07, 0x1b, 0x35, 0x1b, 0x00, 0x00, 0x00,
    0x00, 0x09, 0x1b, 0x35, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x0b, 0x1b, 0x35,
    0x1b, 0x00, 0x00, 0x00, 0x00, 0x0d, 0x1b, 0x35, 0x1b, 0x11, 0x1b, 0x35,
    0x1b, 0x3a, 0x1b, 0x35, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x1b, 0x35,
    0x1b, 0x3e, 0x1b, 0x35, 0x1b, 0x42, 0x1b, 0x35, 0x1b, 0x41, 0x00, 0xc6,
    0x00, 0x42, 0x00, 0x00, 0x00, 0x44, 0x00, 0x45, 0x00, 0x8e, 0x01, 0x47,
    0x00, 0x4f, 0x00, 0x22, 0x02, 0x50, 0x00, 0x52, 0x00, 0x54, 0x00, 0x55,
    0x00, 0x57, 0x00, 0x61, 0x00, 0x50, 0x02, 0x51, 0x02, 0x02, 0x1d, 0x62,
    0x00, 0x64, 0x00, 0x65, 0x00, 0x59, 0x02, 0x5b, 0x02, 0x5c, 0x02, 0x67,
    0x00, 0x00, 0x00, 0x6b, 0x00, 0x6d, 0x00, 0x4b, 0x01, 0x6f, 0x00, 0x54,
    0x02, 0x16, 0x1d, 0x17, 0x1d, 0x70, 0x00, 0x74, 0x00, 0x75, 0x00, 0x1d,
    0x1d, 0x6f, 0x02, 0x76, 0x00, 0x25, 0x1d, 0xb2, 0x03, 0xb3, 0x03, 0xb4,
    0x03, 0xc6, 0x03, 0xc7, 0x03, 0x69, 0x00, 0x72, 0x00, 0x75, 0x00, 0x76,
    0x00, 0xb2, 0x03, 0xb3, 0x03, 0xc1, 0x03, 0xc6, 0x03, 0xc7, 0x03, 0x52,
    0x02, 0x63, 0x00, 0x55, 0x02, 0xf0, 0x00, 0x5c, 0x02, 0x66, 0x00, 0x5f,
    0x02, 0x61, 0x02, 0x65, 0x02, 0x68, 0x02, 0x69, 0x02, 0x6a, 0x02, 0x7b,
    0x1d, 0x9d, 0x02, 0x6d, 0x02, 0x85, 0x1d, 0x9f, 0x02, 0x71, 0x02, 0x70,
    0x02, 0x72, 0x02, 0x73, 0x02, 0x74, 0x02, 0x75, 0x02, 0x78, 0x02, 0x82,
    0x02, 0x83, 0x02, 0xab, 0x01, 0x89, 0x02, 0x8a, 0x02, 0x1c, 0x1d, 0x8b,
    0x02, 0x8c, 0x02, 0x7a, 0x00, 0x90, 0x02, 0x91, 0x02, 0x92, 0x02, 0xb8,
    0x03, 0x41, 0x00, 0xa5, 0x42, 0x00, 0x87, 0x42, 0x00, 0xa3, 0x42, 0x00,
    0xb1, 0xc7, 0x00, 0x81, 0x44, 0x00, 0x87, 0x44, 0x00, 0xa3, 0x44, 0x00,
    0xb1, 0x44, 0x00, 0xa7, 0x44, 0x00, 0xad, 0x12, 0x01, 0x80, 0x12, 0x01,
    0x81, 0x45, 0x00, 0xad, 0x45, 0x00, 0xb0, 0x28, 0x02, 0x86, 0x46, 0x00,
    0x87, 0x47, 0x00, 0x84, 0x48, 0x00, 0x87, 0x48, 0x00, 0xa3, 0x48, 0x00,
    0x88, 0x48, 0x00, 0xa7, 0x48, 0x00, 0xae, 0x49, 0x00, 0xb0, 0xcf, 0x00,
    0x81, 0x4b, 0x00, 0x81, 0x4b, 0x00, 0xa3, 0x4b, 0x00, 0xb1, 0x4c, 0x00,
    0xa3, 0x36, 0x1e, 0x84, 0x4c, 0xb1, 0x4c, 0xad, 0x4d, 0x81, 0x4d, 0x87,
    0x4d, 0xa3, 0x4e, 0x87, 0x4e, 0xa3, 0x4e, 0xb1, 0x4e, 0xad, 0xd5, 0x00,
    0x81, 0xd5, 0x00, 0x88, 0x4c, 0x01, 0x80, 0x4c, 0x01, 0x81, 0x50, 0x00,
    0x81, 0x50, 0x00, 0x87, 0x52, 0x00, 0x87, 0x52, 0x00, 0xa3, 0x5a, 0x1e,
    0x84, 0x52, 0x00, 0xb1, 0x53, 0x00, 0x87, 0x53, 0x00, 0xa3, 0x5a, 0x01,
    0x87, 0x60, 0x01, 0x87, 0x62, 0x1e, 0x87, 0x54, 0x00, 0x87, 0x54, 0x00,
    0xa3, 0x54, 0x00, 0xb1, 0x54, 0x00, 0xad, 0x55, 0x00, 0xa4, 0x55, 0x00,
    0xb0, 0x55, 0x00, 0xad, 0x68, 0x01, 0x81, 0x6a, 0x01, 0x88, 0x56, 0x83,
    0x56, 0xa3, 0x57, 0x80, 0x57, 0x81, 0x57, 0x88, 0x57, 0x87, 0x57, 0xa3,
    0x58, 0x87, 0x58, 0x88, 0x59, 0x87, 0x5a, 0x82, 0x5a, 0xa3, 0x5a, 0xb1,
    0x68, 0xb1, 0x74, 0x88, 0x77, 0x8a, 0x79, 0x8a, 0x61, 0x00, 0xbe, 0x02,
    0x7f, 0x01, 0x87, 0x41, 0x00, 0xa3, 0x41, 0x00, 0x89, 0xc2, 0x00, 0x81,
    0xc2, 0x00, 0x80, 0xc2, 0x00, 0x89, 0xc2, 0x00, 0x83, 0xa0, 0x1e, 0x82,
    0x02, 0x01, 0x81, 0x02, 0x01, 0x80, 0x02, 0x01, 0x89, 0x02, 0x01, 0x83,
    0xa0, 0x1e, 0x86, 0x45, 0x00, 0xa3, 0x45, 0x00, 0x89, 0x45, 0x00, 0x83,
    0xca, 0x00, 0x81, 0xca, 0x00, 0x80, 0xca, 0x00, 0x89, 0xca, 0x00, 0x83,
    0xb8, 0x1e, 0x82, 0x49, 0x00, 0x89, 0x49, 0x00, 0xa3, 0x4f, 0x00, 0xa3,
    0x4f, 0x00, 0x89, 0xd4, 0x00, 0x81, 0xd4, 0x00, 0x80, 0xd4, 0x00, 0x89,
    0xd4, 0x00, 0x83, 0xcc, 0x1e, 0x82, 0xa0, 0x01, 0x81, 0xa0, 0x01, 0x80,
    0xa0, 0x01, 0x89, 0xa0, 0x01, 0x83, 0xa0, 0x01, 0xa3, 0x55, 0x00, 0xa3,
    0x55, 0x00, 0x89, 0xaf, 0x01, 0x81, 0xaf, 0x01, 0x80, 0xaf, 0x01, 0x89,
    0xaf, 0x01, 0x83, 0xaf, 0x01, 0xa3, 0x59, 0x00, 0x80, 0x59, 0x00, 0xa3,
    0x59, 0x00, 0x89, 0x59, 0x00, 0x83, 0xb1, 0x03, 0x13, 0x03, 0x00, 0x1f,
    0x80, 0x00, 0x1f, 0x81, 0x00, 0x1f, 0xc2, 0x91, 0x03, 0x13, 0x03, 0x08,
    0x1f, 0x80, 0x08, 0x1f, 0x81, 0x08, 0x1f, 0xc2, 0xb5, 0x03, 0x13, 0x03,
    0x10, 0x1f, 0x80, 0x10, 0x1f, 0x81, 0x95, 0x03, 0x13, 0x03, 0x18, 0x1f,
    0x80, 0x18, 0x1f, 0x81, 0xb7, 0x03, 0x93, 0xb7, 0x03, 0x94, 0x20, 0x1f,
    0x80, 0x21, 0x1f, 0x80, 0x20, 0x1f, 0x81, 0x21, 0x1f, 0x81, 0x20, 0x1f,
    0xc2, 0x21, 0x1f, 0xc2, 0x97, 0x03, 0x93, 0x97, 0x03, 0x94, 0x28, 0x1f,
    0x80, 0x29, 0x1f, 0x80, 0x28, 0x1f, 0x81, 0x29, 0x1f, 0x81, 0x28, 0x1f,
    0xc2, 0x29, 0x1f, 0xc2, 0xb9, 0x03, 0x93, 0xb9, 0x03, 0x94, 0x30, 0x1f,
    0x80, 0x31, 0x1f, 0x80, 0x30, 0x1f, 0x81, 0x31, 0x1f, 0x81, 0x30, 0x1f,
    0xc2, 0x31, 0x1f, 0xc2, 0x99, 0x03, 0x93, 0x99, 0x03, 0x94, 0x38, 0x1f,
    0x80, 0x39, 0x1f, 0x80, 0x38, 0x1f, 0x81, 0x39, 0x1f, 0x81, 0x38, 0x1f,
    0xc2, 0x39, 0x1f, 0xc2, 0xbf, 0x03, 0x93, 0xbf, 0x03, 0x94, 0x40, 0x1f,
    0x80, 0x40, 0x1f, 0x81, 0x9f, 0x03, 0x13, 0x03, 0x48, 0x1f, 0x80, 0x48,
    0x1f, 0x81, 0xc5, 0x03, 0x13, 0x03, 0x50, 0x1f, 0x80, 0x50, 0x1f, 0x81,
    0x50, 0x1f, 0xc2, 0xa5, 0x03, 0x94, 0x00, 0x00, 0x00, 0x59, 0x1f, 0x80,
    0x00, 0x00, 0x00, 0x59, 0x1f, 0x81, 0x00, 0x00, 0x00, 0x59, 0x1f, 0xc2,
    0xc9, 0x03, 0x93, 0xc9, 0x03, 0x94, 0x60, 0x1f, 0x80, 0x61, 0x1f, 0x80,
    0x60, 0x1f, 0x81, 0x61, 0x1f, 0x81, 0x60, 0x1f, 0xc2, 0x61, 0x1f, 0xc2,
    0xa9, 0x03, 0x93, 0xa9, 0x03, 0x94, 0x68, 0x1f, 0x80, 0x69, 0x1f, 0x80,
    0x68, 0x1f, 0x81, 0x69, 0x1f, 0x81, 0x68, 0x1f, 0xc2, 0x69, 0x1f, 0xc2,
    0xb1, 0x03, 0x80, 0xb5, 0x03, 0x80, 0xb7, 0x03, 0x80, 0xb9, 0x03, 0x80,
    0xbf, 0x03, 0x80, 0xc5, 0x03, 0x80, 0xc9, 0x03, 0x80, 0x00, 0x1f, 0x45,
    0x03, 0x20, 0x1f, 0x45, 0x03, 0x60, 0x1f, 0x45, 0x03, 0xb1, 0x03, 0x86,
    0xb1, 0x03, 0x84, 0x70, 0x1f, 0xc5, 0xb1, 0x03, 0xc5, 0xac, 0x03, 0xc5,
    0x00, 0x00, 0x00, 0xb1, 0x03, 0xc2, 0xb6, 0x1f, 0xc5, 0x91, 0x03, 0x86,
    0x91, 0x03, 0x84, 0x91, 0x03, 0x80, 0x91, 0x03, 0xc5, 0x20, 0x93, 0x20,
    0x93, 0x20, 0xc2, 0xa8, 0x00, 0xc2, 0x74, 0x1f, 0xc5, 0xb7, 0x03, 0xc5,
    0xae, 0x03, 0xc5, 0x00, 0x00, 0x00, 0xb7, 0x03, 0xc2, 0xc6, 0x1f, 0xc5,
    0x95, 0x03, 0x80, 0x97, 0x03, 0x80, 0x97, 0x03, 0xc5, 0xbf, 0x1f, 0x80,
    0xbf, 0x1f, 0x81, 0xbf, 0x1f, 0xc2, 0xb9, 0x03, 0x86, 0xb9, 0x03, 0x84,
    0xca, 0x03, 0x80, 0x00, 0x03, 0xb9, 0x42, 0xca, 0x42, 0x99, 0x06, 0x99,
    0x04, 0x99, 0x00, 0xfe, 0x1f, 0x80, 0xfe, 0x1f, 0x81, 0xfe, 0x1f, 0xc2,
    0xc5, 0x03, 0x86, 0xc5, 0x03, 0x84, 0xcb, 0x03, 0x80, 0x00, 0x03, 0xc1,
    0x13, 0xc1, 0x14, 0xc5, 0x42, 0xcb, 0x42, 0xa5, 0x06, 0xa5, 0x04, 0xa5,
    0x00, 0xa1, 0x03, 0x94, 0xa8, 0x00, 0x80, 0x85, 0x03, 0x60, 0x00, 0x7c,
    0x1f, 0xc5, 0xc9, 0x03, 0xc5, 0xce, 0x03, 0xc5, 0x00, 0x00, 0x00, 0xc9,
    0x03, 0xc2, 0xf6, 0x1f, 0xc5, 0x9f, 0x03, 0x80, 0xa9, 0x03, 0x80, 0xa9,
    0x03, 0xc5, 0x20, 0x94, 0x02, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20, 0x20,
    0x20, 0x20, 0x20, 0x20, 0xb3, 0x2e, 0x2e, 0x2e, 0x2e, 0x2e, 0x32, 0x20,
    0x32, 0x20, 0x32, 0x20, 0x00, 0x00, 0x00, 0x35, 0x20, 0x35, 0x20, 0x35,
    0x20, 0x00, 0x00, 0x00, 0x21, 0x21, 0x00, 0x00, 0x20, 0x85, 0x3f, 0x3f,
    0x3f, 0x21, 0x21, 0x3f, 0x32, 0x20, 0x00, 0x00, 0x00, 0x00, 0x30, 0x69,
    0x00, 0x00, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x2b, 0x3d, 0x28, 0x29,
    0x6e, 0x30, 0x00, 0x2b, 0x00, 0x12, 0x22, 0x3d, 0x00, 0x28, 0x00, 0x29,
    0x00, 0x00, 0x00, 0x61, 0x00, 0x65, 0x00, 0x6f, 0x00, 0x78, 0x00, 0x59,
    0x02, 0x68, 0x6b, 0x6c, 0x6d, 0x6e, 0x70, 0x73, 0x74, 0x52, 0x73, 0x61,
    0x2f, 0x63, 0x61, 0x2f, 0x73, 0xb0, 0x00, 0x43, 0x63, 0x2f, 0x6f, 0x63,
    0x2f, 0x75, 0xb0, 0x00, 0x46, 0x48, 0x00, 0x1f, 0x00, 0x00, 0x00, 0x20,
    0xdf, 0x01, 0x01, 0x04, 0x24, 0x4e, 0x6f, 0x50, 0x51, 0x52, 0x52, 0x52,
    0x53, 0x4d, 0x54, 0x45, 0x4c, 0x54, 0x4d, 0x4b, 0x00, 0xc5, 0x00, 0x42,
    0x43, 0x00, 0x65, 0x45, 0x46, 0x00, 0x4d, 0x6f, 0xd0, 0x05, 0x46, 0x41,
    0x58, 0xc0, 0x03, 0xb3, 0x03, 0x93, 0x03, 0xa0, 0x03, 0x11, 0x22, 0x44,
    0x64, 0x65, 0x69, 0x6a, 0x31, 0xd0, 0x37, 0x31, 0xd0, 0x39, 0x31, 0xd0,
    0x31, 0x30, 0x31, 0xd0, 0x33, 0x32, 0xd0, 0x33, 0x31, 0xd0, 0x35, 0x32,
    0xd0, 0x35, 0x33, 0xd0, 0x35, 0x34, 0xd0, 0x35, 0x31, 0xd0, 0x36, 0x35,
    0xd0, 0x36, 0x31, 0xd0, 0x38, 0x33, 0xd0, 0x38, 0x35, 0xd0, 0x38, 0x37,
    0xd0, 0x38, 0x31, 0xd0, 0x49, 0x49, 0x49, 0x49, 0x49, 0x49, 0x56, 0x56,
    0x49, 0x56, 0x49, 0x49, 0x56, 0x49, 0x49, 0x49, 0x49, 0x58, 0x58, 0x49,
    0x58, 0x49, 0x49, 0x4c, 0x43, 0x44, 0x4d, 0x69, 0x69, 0x69, 0x69, 0x69,
    0x69, 0x69, 0x76, 0x76, 0x69, 0x76, 0x69, 0x69, 0x76, 0x69, 0x69, 0x69,
    0x69, 0x78, 0x78, 0x69, 0x78, 0x69, 0x69, 0x6c, 0x63, 0x64, 0x6d, 0x30,
    0xd0, 0x33, 0x90, 0x21, 0xb8, 0x92, 0x21, 0xb8, 0x94, 0x21, 0xb8, 0xd0,
    0x21, 0xb8, 0xd4, 0x21, 0xb8, 0xd2, 0x21, 0xb8, 0x03, 0x22, 0xb8, 0x08,
    0x22, 0xb8, 0x0b, 0x22, 0xb8, 0x23, 0x22, 0xb8, 0x00, 0x00, 0x00, 0x25,
    0x22, 0xb8, 0x2b, 0x22, 0x2b, 0x22, 0x2b, 0x22, 0x00, 0x00, 0x00, 0x2e,
    0x22, 0x2e, 0x22, 0x2e, 0x22, 0x00, 0x00, 0x00, 0x3c, 0x22, 0xb8, 0x43,
    0x22, 0xb8, 0x45, 0x22, 0xb8, 0x00, 0x00, 0x00, 0x48, 0x22, 0xb8, 0x3d,
    0x00, 0xb8, 0x00, 0x00, 0x00, 0x61, 0x22, 0xb8, 0x4d, 0x22, 0xb8, 0x3c,
    0x00, 0xb8, 0x3e, 0x00, 0xb8, 0x64, 0x22, 0xb8, 0x65, 0x22, 0xb8, 0x72,
    0x22, 0xb8, 0x76, 0x22, 0xb8, 0x7a, 0x22, 0xb8, 0x82, 0x22, 0xb8, 0x86,
    0x22, 0xb8, 0xa2, 0x22, 0xb8, 0xa8, 0x22, 0xb8, 0xa9, 0x22, 0xb8, 0xab,
    0x22, 0xb8, 0x7c, 0x22, 0xb8, 0x91, 0x22, 0xb8, 0xb2, 0x22, 0x38, 0x03,
    0x08, 0x30, 0x31, 0x00, 0x31, 0x00, 0x30, 0x00, 0x32, 0x30, 0x28, 0x00,
    0x31, 0x00, 0x29, 0x00, 0x28, 0x00, 0x31, 0x00, 0x30, 0x00, 0x29, 0x00,
    0x28, 0x32, 0x30, 0x29, 0x31, 0x00, 0x2e, 0x00, 0x31, 0x00, 0x30, 0x00,
    0x2e, 0x00, 0x32, 0x30, 0x2e, 0x28, 0x00, 0x61, 0x00, 0x29, 0x00, 0x41,
    0x00, 0x61, 0x00, 0x2b, 0x22, 0x00, 0x00, 0x00, 0x00, 0x3a, 0x3a, 0x3d,
    0x3d, 0x3d, 0x3d, 0x3d, 0x3d, 0xdd, 0x2a, 0xb8, 0x6a, 0x56, 0x00, 0x4e,
    0x00, 0x28, 0x36, 0x3f, 0x59, 0x85, 0x8c, 0xa0, 0xba, 0x3f, 0x51, 0x00,
    0x26, 0x2c, 0x43, 0x57, 0x6c, 0xa1, 0xb6, 0xc1, 0x9b, 0x52, 0x00, 0x5e,
    0x7a, 0x7f, 0x9d, 0xa6, 0xc1, 0xce, 0xe7, 0xb6, 0x53, 0xc8, 0x53, 0xe3,
    0x53, 0xd7, 0x56, 0x1f, 0x57, 0xeb, 0x58, 0x02, 0x59, 0x0a, 0x59, 0x15,
    0x59, 0x27, 0x59, 0x73, 0x59, 0x50, 0x5b, 0x80, 0x5b, 0xf8, 0x5b, 0x0f,
    0x5c, 0x22, 0x5c, 0x38, 0x5c, 0x6e, 0x5c, 0x71, 0x5c, 0xdb, 0x5d, 0xe5,
    0x5d, 0xf1, 0x5d, 0xfe, 0x5d, 0x72, 0x5e, 0x7a, 0x5e, 0x7f, 0x5e, 0xf4,
    0x5e, 0xfe, 0x5e, 0x0b, 0x5f, 0x13, 0x5f, 0x50, 0x5f, 0x61, 0x5f, 0x73,
    0x5f, 0xc3, 0x5f, 0x08, 0x62, 0x36, 0x62, 0x4b, 0x62, 0x2f, 0x65, 0x34,
    0x65, 0x87, 0x65, 0x97, 0x65, 0xa4, 0x65, 0xb9, 0x65, 0xe0, 0x65, 0xe5,
    0x65, 0xf0, 0x66, 0x08, 0x67, 0x28, 0x67, 0x20, 0x6b, 0x62, 0x6b, 0x79,
    0x6b, 0xb3, 0x6b, 0xcb, 0x6b, 0xd4, 0x6b, 0xdb, 0x6b, 0x0f, 0x6c, 0x14,
    0x6c, 0x34, 0x6c, 0x6b, 0x70, 0x2a, 0x72, 0x36, 0x72, 0x3b, 0x72, 0x3f,
    0x72, 0x47, 0x72, 0x59, 0x72, 0x5b, 0x72, 0xac, 0x72, 0x84, 0x73, 0x89,
    0x73, 0xdc, 0x74, 0xe6, 0x74, 0x18, 0x75, 0x1f, 0x75, 0x28, 0x75, 0x30,
    0x75, 0x8b, 0x75, 0x92, 0x75, 0x76, 0x76, 0x7d, 0x76, 0xae, 0x76, 0xbf,
    0x76, 0xee, 0x76, 0xdb, 0x77, 0xe2, 0x77, 0xf3, 0x77, 0x3a, 0x79, 0xb8,
    0x79, 0xbe, 0x79, 0x74, 0x7a, 0xcb, 0x7a, 0xf9, 0x7a, 0x73, 0x7c, 0xf8,
    0x7c, 0x36, 0x7f, 0x51, 0x7f, 0x8a, 0x7f, 0xbd, 0x7f, 0x01, 0x80, 0x0c,
    0x80, 0x12, 0x80, 0x33, 0x80, 0x7f, 0x80, 0x89, 0x80, 0xe3, 0x81, 0x00,
    0x07, 0x10, 0x19, 0x29, 0x38, 0x3c, 0x8b, 0x8f, 0x95, 0x4d, 0x86, 0x6b,
    0x86, 0x40, 0x88, 0x4c, 0x88, 0x63, 0x88, 0x7e, 0x89, 0x8b, 0x89, 0xd2,
    0x89, 0x00, 0x8a, 0x37, 0x8c, 0x46, 0x8c, 0x55, 0x8c, 0x78, 0x8c, 0x9d,
    0x8c, 0x64, 0x8d, 0x70, 0x8d, 0xb3, 0x8d, 0xab, 0x8e, 0xca, 0x8e, 0x9b,
    0x8f, 0xb0, 0x8f, 0xb5, 0x8f, 0x91, 0x90, 0x49, 0x91, 0xc6, 0x91, 0xcc,
    0x91, 0xd1, 0x91, 0x77, 0x95, 0x80, 0x95, 0x1c, 0x96, 0xb6, 0x96, 0xb9,
    0x96, 0xe8, 0x96, 0x51, 0x97, 0x5e, 0x97, 0x62, 0x97, 0x69, 0x97, 0xcb,
    0x97, 0xed, 0x97, 0xf3, 0x97, 0x01, 0x98, 0xa8, 0x98, 0xdb, 0x98, 0xdf,
    0x98, 0x96, 0x99, 0x99, 0x99, 0xac, 0x99, 0xa8, 0x9a, 0xd8, 0x9a, 0xdf,
    0x9a, 0x25, 0x9b, 0x2f, 0x9b, 0x32, 0x9b, 0x3c, 0x9b, 0x5a, 0x9b, 0xe5,
    0x9c, 0x75, 0x9e, 0x7f, 0x9e, 0xa5, 0x9e, 0x00, 0x16, 0x1e, 0x28, 0x2c,
    0x54, 0x58, 0x69, 0x6e, 0x7b, 0x96, 0xa5, 0xad, 0xe8, 0xf7, 0xfb, 0x12,
    0x30, 0x00, 0x00, 0x41, 0x53, 0x44, 0x53, 0x45, 0x53, 0x4b, 0x30, 0x99,
    0x30, 0x00, 0x00, 0x00, 0x00, 0x4d, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00,
    0x00, 0x4f, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00, 0x00, 0x51, 0x30, 0x99,
    0x30, 0x00, 0x00, 0x00, 0x00, 0x53, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00,
    0x00, 0x55, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00, 0x00, 0x57, 0x30, 0x99,
    0x30, 0x00, 0x00, 0x00, 0x00, 0x59, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00,
    0x00, 0x5b, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00, 0x00, 0x5d, 0x30, 0x99,
    0x30, 0x00, 0x00, 0x00, 0x00, 0x5f, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00,
    0x00, 0x61, 0x30, 0x99, 0x30, 0x64, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00,
    0x00, 0x66, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00, 0x00, 0x68, 0x30, 0x99,
    0x30, 0x6f, 0x30, 0x99, 0x30, 0x72, 0x30, 0x99, 0x30, 0x75, 0x30, 0x99,
    0x30, 0x78, 0x30, 0x99, 0x30, 0x7b, 0x30, 0x99, 0x30, 0x46, 0x30, 0x99,
    0x30, 0x20, 0x00, 0x99, 0x30, 0x9d, 0x30, 0x99, 0x30, 0x88, 0x30, 0x8a,
    0x30, 0xab, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00, 0x00, 0xad, 0x30, 0x99,
    0x30, 0x00, 0x00, 0x00, 0x00, 0xaf, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00,
    0x00, 0xb1, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00, 0x00, 0xb3, 0x30, 0x99,
    0x30, 0x00, 0x00, 0x00, 0x00, 0xb5, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00,
    0x00, 0xb7, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00, 0x00, 0xb9, 0x30, 0x99,
    0x30, 0x00, 0x00, 0x00, 0x00, 0xbb, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00,
    0x00, 0xbd, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00, 0x00, 0xbf, 0x30, 0x99,
    0x30, 0x00, 0x00, 0x00, 0x00, 0xc1, 0x30, 0x99, 0x30, 0xc4, 0x30, 0x99,
    0x30, 0x00, 0x00, 0x00, 0x00, 0xc6, 0x30, 0x99, 0x30, 0x00, 0x00, 0x00,
    0x00, 0xc8, 0x30, 0x99, 0x30, 0xcf, 0x30, 0x99, 0x30, 0xd2, 0x30, 0x99,
    0x30, 0xd5, 0x30, 0x99, 0x30, 0xd8, 0x30, 0x99, 0x30, 0xdb, 0x30, 0x99,
    0x30, 0xa6, 0x30, 0x99, 0x30, 0xef, 0x30, 0x99, 0x30, 0xfd, 0x30, 0x99,
    0x30, 0xb3, 0x30, 0xc8, 0x30, 0x00, 0x11, 0x00, 0x01, 0xaa, 0x02, 0xac,
    0xad, 0x03, 0x04, 0x05, 0xb0, 0xb1, 0xb2, 0xb3, 0xb4, 0xb5, 0x1a, 0x06,
    0x07, 0x08, 0x21, 0x09, 0x11, 0x61, 0x11, 0x14, 0x11, 0x4c, 0x00, 0x01,
    0xb3, 0xb4, 0xb8, 0xba, 0xbf, 0xc3, 0xc5, 0x08, 0xc9, 0xcb, 0x09, 0x0a,
    0x0c, 0x0e, 0x0f, 0x13, 0x15, 0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1e, 0x22,
    0x2c, 0x33, 0x38, 0xdd, 0xde, 0x43, 0x44, 0x45, 0x70, 0x71, 0x74, 0x7d,
    0x7e, 0x80, 0x8a, 0x8d, 0x00, 0x4e, 0x8c, 0x4e, 0x09, 0x4e, 0xdb, 0x56,
    0x0a, 0x4e, 0x2d, 0x4e, 0x0b, 0x4e, 0x32, 0x75, 0x59, 0x4e, 0x19, 0x4e,
    0x01, 0x4e, 0x29, 0x59, 0x30, 0x57, 0xba, 0x4e, 0x28, 0x00, 0x29, 0x00,
    0x00, 0x11, 0x02, 0x11, 0x03, 0x11, 0x05, 0x11, 0x06, 0x11, 0x07, 0x11,
    0x09, 0x11, 0x0b, 0x11, 0x0c, 0x11, 0x0e, 0x11, 0x0f, 0x11, 0x10, 0x11,
    0x11, 0x11, 0x12, 0x11, 0x28, 0x00, 0x00, 0x11, 0x61, 0x11, 0x29, 0x00,
    0x28, 0x00, 0x02, 0x11, 0x61, 0x11, 0x29, 0x00, 0x28, 0x00, 0x05, 0x11,
    0x61, 0x11, 0x29, 0x00, 0x28, 0x00, 0x09, 0x11, 0x61, 0x11, 0x29, 0x00,
    0x28, 0x00, 0x0b, 0x11, 0x61, 0x11, 0x29, 0x00, 0x28, 0x00, 0x0e, 0x11,
    0x61, 0x11, 0x29, 0x00, 0x28, 0x00, 0x0c, 0x11, 0x6e, 0x11, 0x29, 0x00,
    0x28, 0x00, 0x0b, 0x11, 0x69, 0x11, 0x0c, 0x11, 0x65, 0x11, 0xab, 0x11,
    0x29, 0x00, 0x28, 0x00, 0x0b, 0x11, 0x69, 0x11, 0x12, 0x11, 0x6e, 0x11,
    0x29, 0x00, 0x28, 0x00, 0x29, 0x00, 0x00, 0x4e, 0x8c, 0x4e, 0x09, 0x4e,
    0xdb, 0x56, 0x94, 0x4e, 0x6d, 0x51, 0x03, 0x4e, 0x6b, 0x51, 0x5d, 0x4e,
    0x41, 0x53, 0x08, 0x67, 0x6b, 0x70, 0x34, 0x6c, 0x28, 0x67, 0xd1, 0x91,
    0x1f, 0x57, 0xe5, 0x65, 0x2a, 0x68, 0x09, 0x67, 0x3e, 0x79, 0x0d, 0x54,
    0x79, 0x72, 0xa1, 0x8c, 0x5d, 0x79, 0xb4, 0x52, 0xe3, 0x4e, 0x7c, 0x54,
    0x66, 0x5b, 0xe3, 0x76, 0x01, 0x4f, 0xc7, 0x8c, 0x54, 0x53, 0x6d, 0x79,
    0x11, 0x4f, 0xea, 0x81, 0xf3, 0x81, 0x4f, 0x55, 0x7c, 0x5e, 0x87, 0x65,
    0x8f, 0x7b, 0x50, 0x54, 0x45, 0x32, 0x00, 0x31, 0x00, 0x33, 0x00, 0x30,
    0x00, 0x00, 0x11, 0x00, 0x02, 0x03, 0x05, 0x06, 0x07, 0x09, 0x0b, 0x0c,
    0x0e, 0x0f, 0x10, 0x11, 0x12, 0x00, 0x11, 0x00, 0x61, 0x02, 0x61, 0x03,
    0x61, 0x05, 0x61, 0x06, 0x61, 0x07, 0x61, 0x09, 0x61, 0x0b, 0x61, 0x0c,
    0x61, 0x0e, 0x11, 0x61, 0x11, 0x00, 0x11, 0x0e, 0x61, 0xb7, 0x00, 0x69,
    0x0b, 0x11, 0x01, 0x63, 0x00, 0x69, 0x0b, 0x11, 0x6e, 0x11, 0x00, 0x4e,
    0x8c, 0x4e, 0x09, 0x4e, 0xdb, 0x56, 0x94, 0x4e, 0x6d, 0x51, 0x03, 0x4e,
    0x6b, 0x51, 0x5d, 0x4e, 0x41, 0x53, 0x08, 0x67, 0x6b, 0x70, 0x34, 0x6c,
    0x28, 0x67, 0xd1, 0x91, 0x1f, 0x57, 0xe5, 0x65, 0x2a, 0x68, 0x09, 0x67,
    0x3e, 0x79, 0x0d, 0x54, 0x79, 0x72, 0xa1, 0x8c, 0x5d, 0x79, 0xb4, 0x52,
    0xd8, 0x79, 0x37, 0x75, 0x73, 0x59, 0x69, 0x90, 0x2a, 0x51, 0x70, 0x53,
    0xe8, 0x6c, 0x05, 0x98, 0x11, 0x4f, 0x99, 0x51, 0x63, 0x6b, 0x0a, 0x4e,
    0x2d, 0x4e, 0x0b, 0x4e, 0xe6, 0x5d, 0xf3, 0x53, 0x3b, 0x53, 0x97, 0x5b,
    0x66, 0x5b, 0xe3, 0x76, 0x01, 0x4f, 0xc7, 0x8c, 0x54, 0x53, 0x1c, 0x59,
    0x33, 0x00, 0x36, 0x00, 0x34, 0x00, 0x30, 0x00, 0x35, 0x30, 0x31, 0x00,
    0x08, 0x67, 0x31, 0x00, 0x30, 0x00, 0x08, 0x67, 0x48, 0x67, 0x65, 0x72,
    0x67, 0x65, 0x56, 0x4c, 0x54, 0x44, 0xa2, 0x30, 0x00, 0x02, 0x04, 0x06,
    0x08, 0x09, 0x0b, 0x0d, 0x0f, 0x11, 0x13, 0x15, 0x17, 0x19, 0x1b, 0x1d,
    0x1f, 0x22, 0x24, 0x26, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x30, 0x33,
    0x36, 0x39, 0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x42, 0x44, 0x46, 0x47, 0x48,
    0x49, 0x4a, 0x4b, 0x4d, 0x4e, 0x4f, 0x50, 0xe4, 0x4e, 0x8c, 0x54, 0xa1,
    0x30, 0x01, 0x30, 0x5b, 0x27, 0x01, 0x4a, 0x34, 0x00, 0x01, 0x52, 0x39,
    0x01, 0xa2, 0x30, 0x00, 0x5a, 0x49, 0xa4, 0x30, 0x00, 0x27, 0x4f, 0x0c,
    0xa4, 0x30, 0x00, 0x4f, 0x1d, 0x02, 0x05, 0x4f, 0xa8, 0x30, 0x00, 0x11,
    0x07, 0x54, 0x21, 0xa8, 0x30, 0x00, 0x54, 0x03, 0x54, 0xa4, 0x30, 0x06,
    0x4f, 0x15, 0x06, 0x58, 0x3c, 0x07, 0x00, 0x46, 0xab, 0x30, 0x00, 0x3e,
    0x18, 0x1d, 0x00, 0x42, 0x3f, 0x51, 0xac, 0x30, 0x00, 0x41, 0x47, 0x00,
    0x47, 0x32, 0xae, 0x30, 0xac, 0x30, 0xae, 0x30, 0x00, 0x1d, 0x4e, 0xad,
    0x30, 0x00, 0x38, 0x3d, 0x4f, 0x01, 0x3e, 0x13, 0x4f, 0xad, 0x30, 0xed,
    0x30, 0xad, 0x30, 0x00, 0x40, 0x03, 0x3c, 0x33, 0xad, 0x30, 0x00, 0x40,
    0x34, 0x4f, 0x1b, 0x3e, 0xad, 0x30, 0x00, 0x40, 0x42, 0x16, 0x1b, 0xb0,
    0x30, 0x00, 0x39, 0x30, 0xa4, 0x30, 0x0c, 0x45, 0x3c, 0x24, 0x4f, 0x0b,
    0x47, 0x18, 0x00, 0x49, 0xaf, 0x30, 0x00, 0x3e, 0x4d, 0x1e, 0xb1, 0x30,
    0x00, 0x4b, 0x08, 0x02, 0x3a, 0x19, 0x02, 0x4b, 0x2c, 0xa4, 0x30, 0x11,
    0x00, 0x0b, 0x47, 0xb5, 0x30, 0x00, 0x3e, 0x0c, 0x47, 0x2b, 0xb0, 0x30,
    0x07, 0x3a, 0x43, 0x00, 0xb9, 0x30, 0x02, 0x3a, 0x08, 0x02, 0x3a, 0x0f,
    0x07, 0x43, 0x00, 0xb7, 0x30, 0x10, 0x00, 0x12, 0x34, 0x11, 0x3c, 0x13,
    0x17, 0xa4, 0x30, 0x2a, 0x1f, 0x24, 0x2b, 0x00, 0x20, 0xbb, 0x30, 0x16,
    0x41, 0x00, 0x38, 0x0d, 0xc4, 0x30, 0x0d, 0x38, 0x00, 0xd0, 0x30, 0x00,
    0x2c, 0x1c, 0x1b, 0xa2, 0x30, 0x32, 0x00, 0x17, 0x26, 0x49, 0xaf, 0x30,
    0x25, 0x00, 0x3c, 0xb3, 0x30, 0x21, 0x00, 0x20, 0x38, 0xa1, 0x30, 0x34,
    0x00, 0x48, 0x22, 0x28, 0xa3, 0x30, 0x32, 0x00, 0x59, 0x25, 0xa7, 0x30,
    0x2f, 0x1c, 0x10, 0x00, 0x44, 0xd5, 0x30, 0x00, 0x14, 0x1e, 0xaf, 0x30,
    0x29, 0x00, 0x10, 0x4d, 0x3c, 0xda, 0x30, 0xbd, 0x30, 0xb8, 0x30, 0x22,
    0x13, 0x1a, 0x20, 0x33, 0x0c, 0x22, 0x3b, 0x01, 0x22, 0x44, 0x00, 0x21,
    0x44, 0x07, 0xa4, 0x30, 0x39, 0x00, 0x4f, 0x24, 0xc8, 0x30, 0x14, 0x23,
    0x00, 0xdb, 0x30, 0xf3, 0x30, 0xc9, 0x30, 0x14, 0x2a, 0x00, 0x12, 0x33,
    0x22, 0x12, 0x33, 0x2a, 0xa4, 0x30, 0x3a, 0x00, 0x0b, 0x49, 0xa4, 0x30,
    0x3a, 0x00, 0x47, 0x3a, 0x1f, 0x2b, 0x3a, 0x47, 0x0b, 0xb7, 0x30, 0x27,
    0x3c, 0x00, 0x30, 0x3c, 0xaf, 0x30, 0x30, 0x00, 0x3e, 0x44, 0xdf, 0x30,
    0xea, 0x30, 0xd0, 0x30, 0x0f, 0x1a, 0x00, 0x2c, 0x1b, 0xe1, 0x30, 0xac,
    0x30, 0xac, 0x30, 0x35, 0x00, 0x1c, 0x47, 0x35, 0x50, 0x1c, 0x3f, 0xa2,
    0x30, 0x42, 0x5a, 0x27, 0x42, 0x5a, 0x49, 0x44, 0x00, 0x51, 0xc3, 0x30,
    0x27, 0x00, 0x05, 0x28, 0xea, 0x30, 0xe9, 0x30, 0xd4, 0x30, 0x17, 0x00,
    0x28, 0xd6, 0x30, 0x15, 0x26, 0x00, 0x15, 0xec, 0x30, 0xe0, 0x30, 0xb2,
    0x30, 0x3a, 0x41, 0x16, 0x00, 0x41, 0xc3, 0x30, 0x2c, 0x00, 0x05, 0x30,
    0x00, 0xb9, 0x70, 0x31, 0x00, 0x30, 0x00, 0xb9, 0x70, 0x32, 0x00, 0x30,
    0x00, 0xb9, 0x70, 0x68, 0x50, 0x61, 0x64, 0x61, 0x41, 0x55, 0x62, 0x61,
    0x72, 0x6f, 0x56, 0x70, 0x63, 0x64, 0x6d, 0x64, 0x00, 0x6d, 0x00, 0xb2,
    0x00, 0x49, 0x00, 0x55, 0x00, 0x73, 0x5e, 0x10, 0x62, 0x2d, 0x66, 0x8c,
    0x54, 0x27, 0x59, 0x63, 0x6b, 0x0e, 0x66, 0xbb, 0x6c, 0x2a, 0x68, 0x0f,
    0x5f, 0x1a, 0x4f, 0x3e, 0x79, 0x70, 0x00, 0x41, 0x6e, 0x00, 0x41, 0xbc,
    0x03, 0x41, 0x6d, 0x00, 0x41, 0x6b, 0x00, 0x41, 0x4b, 0x00, 0x42, 0x4d,
    0x00, 0x42, 0x47, 0x00, 0x42, 0x63, 0x61, 0x6c, 0x6b, 0x63, 0x61, 0x6c,
    0x70, 0x00, 0x46, 0x6e, 0x00, 0x46, 0xbc, 0x03, 0x46, 0xbc, 0x03, 0x67,
    0x6d, 0x00, 0x67, 0x6b, 0x00, 0x67, 0x48, 0x00, 0x7a, 0x6b, 0x48, 0x7a,
    0x4d, 0x48, 0x7a, 0x47, 0x48, 0x7a, 0x54, 0x48, 0x7a, 0xbc, 0x03, 0x13,
    0x21, 0x6d, 0x00, 0x13, 0x21, 0x64, 0x00, 0x13, 0x21, 0x6b, 0x00, 0x13,
    0x21, 0x66, 0x00, 0x6d, 0x6e, 0x00, 0x6d, 0xbc, 0x03, 0x6d, 0x6d, 0x00,
    0x6d, 0x63, 0x00, 0x6d, 0x6b, 0x00, 0x6d, 0x63, 0x00, 0x0a, 0x0a, 0x4f,
    0x00, 0x0a, 0x4f, 0x6d, 0x00, 0xb2, 0x00, 0x63, 0x00, 0x08, 0x0a, 0x4f,
    0x0a, 0x0a, 0x50, 0x00, 0x0a, 0x50, 0x6d, 0x00, 0xb3, 0x00, 0x6b, 0x00,
    0x6d, 0x00, 0xb3, 0x00, 0x6d, 0x00, 0x15, 0x22, 0x73, 0x00, 0x6d, 0x00,
    0x15, 0x22, 0x73, 0x00, 0xb2, 0x00, 0x50, 0x61, 0x6b, 0x50, 0x61, 0x4d,
    0x50, 0x61, 0x47, 0x50, 0x61, 0x72, 0x61, 0x64, 0x72, 0x61, 0x64, 0xd1,
    0x73, 0x72, 0x00, 0x61, 0x00, 0x64, 0x00, 0x15, 0x22, 0x73, 0x00, 0xb2,
    0x00, 0x70, 0x00, 0x73, 0x6e, 0x00, 0x73, 0xbc, 0x03, 0x73, 0x6d, 0x00,
    0x73, 0x70, 0x00, 0x56, 0x6e, 0x00, 0x56, 0xbc, 0x03, 0x56, 0x6d, 0x00,
    0x56, 0x6b, 0x00, 0x56, 0x4d, 0x00, 0x56, 0x70, 0x00, 0x57, 0x6e, 0x00,
    0x57, 0xbc, 0x03, 0x57, 0x6d, 0x00, 0x57, 0x6b, 0x00, 0x57, 0x4d, 0x00,
    0x57, 0x6b, 0x00, 0xa9, 0x03, 0x4d, 0x00, 0xa9, 0x03, 0x61, 0x2e, 0x6d,
    0x2e, 0x42, 0x71, 0x63, 0x63, 0x63, 0x64, 0x43, 0xd1, 0x6b, 0x67, 0x43,
    0x6f, 0x2e, 0x64, 0x42, 0x47, 0x79, 0x68, 0x61, 0x48, 0x50, 0x69, 0x6e,
    0x4b, 0x4b, 0x4b, 0x4d, 0x6b, 0x74, 0x6c, 0x6d, 0x6c, 0x6e, 0x6c, 0x6f,
    0x67, 0x6c, 0x78, 0x6d, 0x62, 0x6d, 0x69, 0x6c, 0x6d, 0x6f, 0x6c, 0x50,
    0x48, 0x70, 0x2e, 0x6d, 0x2e, 0x50, 0x50, 0x4d, 0x50, 0x52, 0x73, 0x72,
    0x53, 0x76, 0x57, 0x62, 0x56, 0xd1, 0x6d, 0x41, 0xd1, 0x6d, 0x31, 0x00,
    0xe5, 0x65, 0x31, 0x00, 0x30, 0x00, 0xe5, 0x65, 0x32, 0x00, 0x30, 0x00,
    0xe5, 0x65, 0x33, 0x00, 0x30, 0x00, 0xe5, 0x65, 0x67, 0x61, 0x6c, 0x4a,
    0x04, 0x4c, 0x04, 0x26, 0x01, 0x53, 0x01, 0x27, 0xa7, 0x37, 0xab, 0x6b,
    0x02, 0x52, 0xab, 0x48, 0x8c, 0xf4, 0x66, 0xca, 0x8e, 0xc8, 0x8c, 0xd1,
    0x6e, 0x32, 0x4e, 0xe5, 0x53, 0x9c, 0x9f, 0x9c, 0x9f, 0x51, 0x59, 0xd1,
    0x91, 0x87, 0x55, 0x48, 0x59, 0xf6, 0x61, 0x69, 0x76, 0x85, 0x7f, 0x3f,
    0x86, 0xba, 0x87, 0xf8, 0x88, 0x8f, 0x90, 0x02, 0x6a, 0x1b, 0x6d, 0xd9,
    0x70, 0xde, 0x73, 0x3d, 0x84, 0x6a, 0x91, 0xf1, 0x99, 0x82, 0x4e, 0x75,
    0x53, 0x04, 0x6b, 0x1b, 0x72, 0x2d, 0x86, 0x1e, 0x9e, 0x50, 0x5d, 0xeb,
    0x6f, 0xcd, 0x85, 0x64, 0x89, 0xc9, 0x62, 0xd8, 0x81, 0x1f, 0x88, 0xca,
    0x5e, 0x17, 0x67, 0x6a, 0x6d, 0xfc, 0x72, 0xce, 0x90, 0x86, 0x4f, 0xb7,
    0x51, 0xde, 0x52, 0xc4, 0x64, 0xd3, 0x6a, 0x10, 0x72, 0xe7, 0x76, 0x01,
    0x80, 0x06, 0x86, 0x5c, 0x86, 0xef, 0x8d, 0x32, 0x97, 0x6f, 0x9b, 0xfa,
    0x9d, 0x8c, 0x78, 0x7f, 0x79, 0xa0, 0x7d, 0xc9, 0x83, 0x04, 0x93, 0x7f,
    0x9e, 0xd6, 0x8a, 0xdf, 0x58, 0x04, 0x5f, 0x60, 0x7c, 0x7e, 0x80, 0x62,
    0x72, 0xca, 0x78, 0xc2, 0x8c, 0xf7, 0x96, 0xd8, 0x58, 0x62, 0x5c, 0x13,
    0x6a, 0xda, 0x6d, 0x0f, 0x6f, 0x2f, 0x7d, 0x37, 0x7e, 0x4b, 0x96, 0xd2,
    0x52, 0x8b, 0x80, 0xdc, 0x51, 0xcc, 0x51, 0x1c, 0x7a, 0xbe, 0x7d, 0xf1,
    0x83, 0x75, 0x96, 0x80, 0x8b, 0xcf, 0x62, 0x02, 0x6a, 0xfe, 0x8a, 0x39,
    0x4e, 0xe7, 0x5b, 0x12, 0x60, 0x87, 0x73, 0x70, 0x75, 0x17, 0x53, 0xfb,
    0x78, 0xbf, 0x4f, 0xa9, 0x5f, 0x0d, 0x4e, 0xcc, 0x6c, 0x78, 0x65, 0x22,
    0x7d, 0xc3, 0x53, 0x5e, 0x58, 0x01, 0x77, 0x49, 0x84, 0xaa, 0x8a, 0xba,
    0x6b, 0xb0, 0x8f, 0x88, 0x6c, 0xfe, 0x62, 0xe5, 0x82, 0xa0, 0x63, 0x65,
    0x75, 0xae, 0x4e, 0x69, 0x51, 0xc9, 0x51, 0x81, 0x68, 0xe7, 0x7c, 0x6f,
    0x82, 0xd2, 0x8a, 0xcf, 0x91, 0xf5, 0x52, 0x42, 0x54, 0x73, 0x59, 0xec,
    0x5e, 0xc5, 0x65, 0xfe, 0x6f, 0x2a, 0x79, 0xad, 0x95, 0x6a, 0x9a, 0x97,
    0x9e, 0xce, 0x9e, 0x9b, 0x52, 0xc6, 0x66, 0x77, 0x6b, 0x62, 0x8f, 0x74,
    0x5e, 0x90, 0x61, 0x00, 0x62, 0x9a, 0x64, 0x23, 0x6f, 0x49, 0x71, 0x89,
    0x74, 0xca, 0x79, 0xf4, 0x7d, 0x6f, 0x80, 0x26, 0x8f, 0xee, 0x84, 0x23,
    0x90, 0x4a, 0x93, 0x17, 0x52, 0xa3, 0x52, 0xbd, 0x54, 0xc8, 0x70, 0xc2,
    0x88, 0xaa, 0x8a, 0xc9, 0x5e, 0xf5, 0x5f, 0x7b, 0x63, 0xae, 0x6b, 0x3e,
    0x7c, 0x75, 0x73, 0xe4, 0x4e, 0xf9, 0x56, 0xe7, 0x5b, 0xba, 0x5d, 0x1c,
    0x60, 0xb2, 0x73, 0x69, 0x74, 0x9a, 0x7f, 0x46, 0x80, 0x34, 0x92, 0xf6,
    0x96, 0x48, 0x97, 0x18, 0x98, 0x8b, 0x4f, 0xae, 0x79, 0xb4, 0x91, 0xb8,
    0x96, 0xe1, 0x60, 0x86, 0x4e, 0xda, 0x50, 0xee, 0x5b, 0x3f, 0x5c, 0x99,
    0x65, 0x02, 0x6a, 0xce, 0x71, 0x42, 0x76, 0xfc, 0x84, 0x7c, 0x90, 0x8d,
    0x9f, 0x88, 0x66, 0x2e, 0x96, 0x89, 0x52, 0x7b, 0x67, 0xf3, 0x67, 0x41,
    0x6d, 0x9c, 0x6e, 0x09, 0x74, 0x59, 0x75, 0x6b, 0x78, 0x10, 0x7d, 0x5e,
    0x98, 0x6d, 0x51, 0x2e, 0x62, 0x78, 0x96, 0x2b, 0x50, 0x19, 0x5d, 0xea,
    0x6d, 0x2a, 0x8f, 0x8b, 0x5f, 0x44, 0x61, 0x17, 0x68, 0x87, 0x73, 0x86,
    0x96, 0x29, 0x52, 0x0f, 0x54, 0x65, 0x5c, 0x13, 0x66, 0x4e, 0x67, 0xa8,
    0x68, 0xe5, 0x6c, 0x06, 0x74, 0xe2, 0x75, 0x79, 0x7f, 0xcf, 0x88, 0xe1,
    0x88, 0xcc, 0x91, 0xe2, 0x96, 0x3f, 0x53, 0xba, 0x6e, 0x1d, 0x54, 0xd0,
    0x71, 0x98, 0x74, 0xfa, 0x85, 0xa3, 0x96, 0x57, 0x9c, 0x9f, 0x9e, 0x97,
    0x67, 0xcb, 0x6d, 0xe8, 0x81, 0xcb, 0x7a, 0x20, 0x7b, 0x92, 0x7c, 0xc0,
    0x72, 0x99, 0x70, 0x58, 0x8b, 0xc0, 0x4e, 0x36, 0x83, 0x3a, 0x52, 0x07,
    0x52, 0xa6, 0x5e, 0xd3, 0x62, 0xd6, 0x7c, 0x85, 0x5b, 0x1e, 0x6d, 0xb4,
    0x66, 0x3b, 0x8f, 0x4c, 0x88, 0x4d, 0x96, 0x8b, 0x89, 0xd3, 0x5e, 0x40,
    0x51, 0xc0, 0x55, 0x00, 0x00, 0x00, 0x00, 0x5a, 0x58, 0x00, 0x00, 0x74,
    0x66, 0x00, 0x00, 0x00, 0x00, 0xde, 0x51, 0x2a, 0x73, 0xca, 0x76, 0x3c,
    0x79, 0x5e, 0x79, 0x65, 0x79, 0x8f, 0x79, 0x56, 0x97, 0xbe, 0x7c, 0xbd,
    0x7f, 0x00, 0x00, 0x12, 0x86, 0x00, 0x00, 0xf8, 0x8a, 0x00, 0x00, 0x00,
    0x00, 0x38, 0x90, 0xfd, 0x90, 0xef, 0x98, 0xfc, 0x98, 0x28, 0x99, 0xb4,
    0x9d, 0xde, 0x90, 0xb7, 0x96, 0xae, 0x4f, 0xe7, 0x50, 0x4d, 0x51, 0xc9,
    0x52, 0xe4, 0x52, 0x51, 0x53, 0x9d, 0x55, 0x06, 0x56, 0x68, 0x56, 0x40,
    0x58, 0xa8, 0x58, 0x64, 0x5c, 0x6e, 0x5c, 0x94, 0x60, 0x68, 0x61, 0x8e,
    0x61, 0xf2, 0x61, 0x4f, 0x65, 0xe2, 0x65, 0x91, 0x66, 0x85, 0x68, 0x77,
    0x6d, 0x1a, 0x6e, 0x22, 0x6f, 0x6e, 0x71, 0x2b, 0x72, 0x22, 0x74, 0x91,
    0x78, 0x3e, 0x79, 0x49, 0x79, 0x48, 0x79, 0x50, 0x79, 0x56, 0x79, 0x5d,
    0x79, 0x8d, 0x79, 0x8e, 0x79, 0x40, 0x7a, 0x81, 0x7a, 0xc0, 0x7b, 0xf4,
    0x7d, 0x09, 0x7e, 0x41, 0x7e, 0x72, 0x7f, 0x05, 0x80, 0xed, 0x81, 0x79,
    0x82, 0x79, 0x82, 0x57, 0x84, 0x10, 0x89, 0x96, 0x89, 0x01, 0x8b, 0x39,
    0x8b, 0xd3, 0x8c, 0x08, 0x8d, 0xb6, 0x8f, 0x38, 0x90, 0xe3, 0x96, 0xff,
    0x97, 0x3b, 0x98, 0x75, 0x60, 0xee, 0x42, 0x18, 0x82, 0x02, 0x26, 0x4e,
    0xb5, 0x51, 0x68, 0x51, 0x80, 0x4f, 0x45, 0x51, 0x80, 0x51, 0xc7, 0x52,
    0xfa, 0x52, 0x9d, 0x55, 0x55, 0x55, 0x99, 0x55, 0xe2, 0x55, 0x5a, 0x58,
    0xb3, 0x58, 0x44, 0x59, 0x54, 0x59, 0x62, 0x5a, 0x28, 0x5b, 0xd2, 0x5e,
    0xd9, 0x5e, 0x69, 0x5f, 0xad, 0x5f, 0xd8, 0x60, 0x4e, 0x61, 0x08, 0x61,
    0x8e, 0x61, 0x60, 0x61, 0xf2, 0x61, 0x34, 0x62, 0xc4, 0x63, 0x1c, 0x64,
    0x52, 0x64, 0x56, 0x65, 0x74, 0x66, 0x17, 0x67, 0x1b, 0x67, 0x56, 0x67,
    0x79, 0x6b, 0xba, 0x6b, 0x41, 0x6d, 0xdb, 0x6e, 0xcb, 0x6e, 0x22, 0x6f,
    0x1e, 0x70, 0x6e, 0x71, 0xa7, 0x77, 0x35, 0x72, 0xaf, 0x72, 0x2a, 0x73,
    0x71, 0x74, 0x06, 0x75, 0x3b, 0x75, 0x1d, 0x76, 0x1f, 0x76, 0xca, 0x76,
    0xdb, 0x76, 0xf4, 0x76, 0x4a, 0x77, 0x40, 0x77, 0xcc, 0x78, 0xb1, 0x7a,
    0xc0, 0x7b, 0x7b, 0x7c, 0x5b, 0x7d, 0xf4, 0x7d, 0x3e, 0x7f, 0x05, 0x80,
    0x52, 0x83, 0xef, 0x83, 0x79, 0x87, 0x41, 0x89, 0x86, 0x89, 0x96, 0x89,
    0xbf, 0x8a, 0xf8, 0x8a, 0xcb, 0x8a, 0x01, 0x8b, 0xfe, 0x8a, 0xed, 0x8a,
    0x39, 0x8b, 0x8a, 0x8b, 0x08, 0x8d, 0x38, 0x8f, 0x72, 0x90, 0x99, 0x91,
    0x76, 0x92, 0x7c, 0x96, 0xe3, 0x96, 0x56, 0x97, 0xdb, 0x97, 0xff, 0x97,
    0x0b, 0x98, 0x3b, 0x98, 0x12, 0x9b, 0x9c, 0x9f, 0x4a, 0x28, 0x44, 0x28,
    0xd5, 0x33, 0x9d, 0x3b, 0x18, 0x40, 0x39, 0x40, 0x49, 0x52, 0xd0, 0x5c,
    0xd3, 0x7e, 0x43, 0x9f, 0x8e, 0x9f, 0x2a, 0xa0, 0x02, 0x66, 0x66, 0x66,
    0x69, 0x66, 0x6c, 0x66, 0x66, 0x69, 0x66, 0x66, 0x6c, 0x7f, 0x01, 0x74,
    0x73, 0x00, 0x74, 0x65, 0x05, 0x0f, 0x11, 0x0f, 0x00, 0x0f, 0x06, 0x19,
    0x11, 0x0f, 0x08, 0xd9, 0x05, 0xb4, 0x05, 0x00, 0x00, 0x00, 0x00, 0xf2,
    0x05, 0xb7, 0x05, 0xd0, 0x05, 0x12, 0x00, 0x03, 0x04, 0x0b, 0x0c, 0x0d,
    0x18, 0x1a, 0xe9, 0x05, 0xc1, 0x05, 0xe9, 0x05, 0xc2, 0x05, 0x49, 0xfb,
    0xc1, 0x05, 0x49, 0xfb, 0xc2, 0x05, 0xd0, 0x05, 0xb7, 0x05, 0xd0, 0x05,
    0xb8, 0x05, 0xd0, 0x05, 0xbc, 0x05, 0xd8, 0x05, 0xbc, 0x05, 0xde, 0x05,
    0xbc, 0x05, 0xe0, 0x05, 0xbc, 0x05, 0xe3, 0x05, 0xbc, 0x05, 0xb9, 0x05,
    0x2d, 0x03, 0x2e, 0x03, 0x2f, 0x03, 0x30, 0x03, 0x31, 0x03, 0x1c, 0x00,
    0x18, 0x06, 0x22, 0x06, 0x2b, 0x06, 0xd0, 0x05, 0xdc, 0x05, 0x71, 0x06,
    0x00, 0x00, 0x0a, 0x0a, 0x0a, 0x0a, 0x0d, 0x0d, 0x0d, 0x0d, 0x0f, 0x0f,
    0x0f, 0x0f, 0x09, 0x09, 0x09, 0x09, 0x0e, 0x0e, 0x0e, 0x0e, 0x08, 0x08,
    0x08, 0x08, 0x33, 0x33, 0x33, 0x33, 0x35, 0x35, 0x35, 0x35, 0x13, 0x13,
    0x13, 0x13, 0x12, 0x12, 0x12, 0x12, 0x15, 0x15, 0x15, 0x15, 0x16, 0x16,
    0x16, 0x16, 0x1c, 0x1c, 0x1b, 0x1b, 0x1d, 0x1d, 0x17, 0x17, 0x27, 0x27,
    0x20, 0x20, 0x38, 0x38, 0x38, 0x38, 0x3e, 0x3e, 0x3e, 0x3e, 0x42, 0x42,
    0x42, 0x42, 0x40, 0x40, 0x40, 0x40, 0x49, 0x49, 0x4a, 0x4a, 0x4a, 0x4a,
    0x4f, 0x4f, 0x50, 0x50, 0x50, 0x50, 0x4d, 0x4d, 0x4d, 0x4d, 0x61, 0x61,
    0x62, 0x62, 0x49, 0x06, 0x64, 0x64, 0x64, 0x64, 0x7e, 0x7e, 0x7d, 0x7d,
    0x7f, 0x7f, 0x2e, 0x82, 0x82, 0x7c, 0x7c, 0x80, 0x80, 0x87, 0x87, 0x87,
    0x87, 0x00, 0x00, 0x26, 0x06, 0x00, 0x01, 0x00, 0x01, 0x00, 0xaf, 0x00,
    0xaf, 0x00, 0x22, 0x00, 0x22, 0x00, 0xa1, 0x00, 0xa1, 0x00, 0xa0, 0x00,
    0xa0, 0x00, 0xa2, 0x00, 0xa2, 0x00, 0xaa, 0x00, 0xaa, 0x00, 0xaa, 0x00,
    0x23, 0x00, 0x23, 0x00, 0x23, 0xcc, 0x06, 0x00, 0x00, 0x00, 0x00, 0x26,
    0x06, 0x00, 0x06, 0x00, 0x07, 0x00, 0x1f, 0x00, 0x23, 0x00, 0x24, 0x02,
    0x06, 0x02, 0x07, 0x02, 0x08, 0x02, 0x1f, 0x02, 0x23, 0x02, 0x24, 0x04,
    0x06, 0x04, 0x07, 0x04, 0x08, 0x04, 0x1f, 0x04, 0x23, 0x04, 0x24, 0x05,
    0x06, 0x05, 0x1f, 0x05, 0x23, 0x05, 0x24, 0x06, 0x07, 0x06, 0x1f, 0x07,
    0x06, 0x07, 0x1f, 0x08, 0x06, 0x08, 0x07, 0x08, 0x1f, 0x0d, 0x06, 0x0d,
    0x07, 0x0d, 0x08, 0x0d, 0x1f, 0x0f, 0x07, 0x0f, 0x1f, 0x10, 0x06, 0x10,
    0x07, 0x10, 0x08, 0x10, 0x1f, 0x11, 0x07, 0x11, 0x1f, 0x12, 0x1f, 0x13,
    0x06, 0x13, 0x1f, 0x14, 0x06, 0x14, 0x1f, 0x1b, 0x06, 0x1b, 0x07, 0x1b,
    0x08, 0x1b, 0x1f, 0x1b, 0x23, 0x1b, 0x24, 0x1c, 0x07, 0x1c, 0x1f, 0x1c,
    0x23, 0x1c, 0x24, 0x1d, 0x01, 0x1d, 0x06, 0x1d, 0x07, 0x1d, 0x08, 0x1d,
    0x1e, 0x1d, 0x1f, 0x1d, 0x23, 0x1d, 0x24, 0x1e, 0x06, 0x1e, 0x07, 0x1e,
    0x08, 0x1e, 0x1f, 0x1e, 0x23, 0x1e, 0x24, 0x1f, 0x06, 0x1f, 0x07, 0x1f,
    0x08, 0x1f, 0x1f, 0x1f, 0x23, 0x1f, 0x24, 0x20, 0x06, 0x20, 0x07, 0x20,
    0x08, 0x20, 0x1f, 0x20, 0x23, 0x20, 0x24, 0x21, 0x06, 0x21, 0x1f, 0x21,
    0x23, 0x21, 0x24, 0x24, 0x06, 0x24, 0x07, 0x24, 0x08, 0x24, 0x1f, 0x24,
    0x23, 0x24, 0x24, 0x0a, 0x4a, 0x0b, 0x4a, 0x23, 0x4a, 0x20, 0x00, 0x4c,
    0x06, 0x51, 0x06, 0x51, 0x06, 0xff, 0x00, 0x1f, 0x26, 0x06, 0x00, 0x0b,
    0x00, 0x0c, 0x00, 0x1f, 0x00, 0x20, 0x00, 0x23, 0x00, 0x24, 0x02, 0x0b,
    0x02, 0x0c, 0x02, 0x1f, 0x02, 0x20, 0x02, 0x23, 0x02, 0x24, 0x04, 0x0b,
    0x04, 0x0c, 0x04, 0x1f, 0x26, 0x06, 0x04, 0x20, 0x04, 0x23, 0x04, 0x24,
    0x05, 0x0b, 0x05, 0x0c, 0x05, 0x1f, 0x05, 0x20, 0x05, 0x23, 0x05, 0x24,
    0x1b, 0x23, 0x1b, 0x24, 0x1c, 0x23, 0x1c, 0x24, 0x1d, 0x01, 0x1d, 0x1e,
    0x1d, 0x1f, 0x1d, 0x23, 0x1d, 0x24, 0x1e, 0x1f, 0x1e, 0x23, 0x1e, 0x24,
    0x1f, 0x01, 0x1f, 0x1f, 0x20, 0x0b, 0x20, 0x0c, 0x20, 0x1f, 0x20, 0x20,
    0x20, 0x23, 0x20, 0x24, 0x23, 0x4a, 0x24, 0x0b, 0x24, 0x0c, 0x24, 0x1f,
    0x24, 0x20, 0x24, 0x23, 0x24, 0x24, 0x00, 0x06, 0x00, 0x07, 0x00, 0x08,
    0x00, 0x1f, 0x00, 0x21, 0x02, 0x06, 0x02, 0x07, 0x02, 0x08, 0x02, 0x1f,
    0x02, 0x21, 0x04, 0x06, 0x04, 0x07, 0x04, 0x08, 0x04, 0x1f, 0x04, 0x21,
    0x05, 0x1f, 0x06, 0x07, 0x06, 0x1f, 0x07, 0x06, 0x07, 0x1f, 0x08, 0x06,
    0x08, 0x1f, 0x0d, 0x06, 0x0d, 0x07, 0x0d, 0x08, 0x0d, 0x1f, 0x0f, 0x07,
    0x0f, 0x08, 0x0f, 0x1f, 0x10, 0x06, 0x10, 0x07, 0x10, 0x08, 0x10, 0x1f,
    0x11, 0x07, 0x12, 0x1f, 0x13, 0x06, 0x13, 0x1f, 0x14, 0x06, 0x14, 0x1f,
    0x1b, 0x06, 0x1b, 0x07, 0x1b, 0x08, 0x1b, 0x1f, 0x1c, 0x07, 0x1c, 0x1f,
    0x1d, 0x06, 0x1d, 0x07, 0x1d, 0x08, 0x1d, 0x1e, 0x1d, 0x1f, 0x1e, 0x06,
    0x1e, 0x07, 0x1e, 0x08, 0x1e, 0x1f, 0x1e, 0x21, 0x1f, 0x06, 0x1f, 0x07,
    0x1f, 0x08, 0x1f, 0x1f, 0x20, 0x06, 0x20, 0x07, 0x20, 0x08, 0x20, 0x1f,
    0x20, 0x21, 0x21, 0x06, 0x21, 0x1f, 0x21, 0x4a, 0x24, 0x06, 0x24, 0x07,
    0x24, 0x08, 0x24, 0x1f, 0x24, 0x21, 0x00, 0x1f, 0x00, 0x21, 0x02, 0x1f,
    0x02, 0x21, 0x04, 0x1f, 0x04, 0x21, 0x05, 0x1f, 0x05, 0x21, 0x0d, 0x1f,
    0x0d, 0x21, 0x0e, 0x1f, 0x0e, 0x21, 0x1d, 0x1e, 0x1d, 0x1f, 0x1e, 0x1f,
    0x20, 0x1f, 0x20, 0x21, 0x24, 0x1f, 0x24, 0x21, 0x40, 0x06, 0x4e, 0x06,
    0x51, 0x06, 0x27, 0x06, 0x10, 0x22, 0x10, 0x23, 0x12, 0x22, 0x12, 0x23,
    0x13, 0x22, 0x13, 0x23, 0x0c, 0x22, 0x0c, 0x23, 0x0d, 0x22, 0x0d, 0x23,
    0x06, 0x22, 0x06, 0x23, 0x05, 0x22, 0x05, 0x23, 0x07, 0x22, 0x07, 0x23,
    0x0e, 0x22, 0x0e, 0x23, 0x0f, 0x22, 0x0f, 0x23, 0x0d, 0x05, 0x0d, 0x06,
    0x0d, 0x07, 0x0d, 0x1e, 0x0d, 0x0a, 0x0c, 0x0a, 0x0e, 0x0a, 0x0f, 0x0a,
    0x10, 0x22, 0x10, 0x23, 0x12, 0x22, 0x12, 0x23, 0x13, 0x22, 0x13, 0x23,
    0x0c, 0x22, 0x0c, 0x23, 0x0d, 0x22, 0x0d, 0x23, 0x06, 0x22, 0x06, 0x23,
    0x05, 0x22, 0x05, 0x23, 0x07, 0x22, 0x07, 0x23, 0x0e, 0x22, 0x0e, 0x23,
    0x0f, 0x22, 0x0f, 0x23, 0x0d, 0x05, 0x0d, 0x06, 0x0d, 0x07, 0x0d, 0x1e,
    0x0d, 0x0a, 0x0c, 0x0a, 0x0e, 0x0a, 0x0f, 0x0a, 0x0d, 0x05, 0x0d, 0x06,
    0x0d, 0x07, 0x0d, 0x1e, 0x0c, 0x20, 0x0d, 0x20, 0x10, 0x1e, 0x0c, 0x05,
    0x0c, 0x06, 0x0c, 0x07, 0x0d, 0x05, 0x0d, 0x06, 0x0d, 0x07, 0x10, 0x1e,
    0x11, 0x1e, 0x00, 0x24, 0x00, 0x24, 0x2a, 0x06, 0x00, 0x02, 0x1b, 0x00,
    0x03, 0x02, 0x00, 0x03, 0x02, 0x00, 0x03, 0x1b, 0x00, 0x04, 0x1b, 0x00,
    0x1b, 0x02, 0x00, 0x1b, 0x03, 0x00, 0x1b, 0x04, 0x02, 0x1b, 0x03, 0x02,
    0x1b, 0x03, 0x03, 0x1b, 0x20, 0x03, 0x1b, 0x1f, 0x09, 0x03, 0x02, 0x09,
    0x02, 0x03, 0x09, 0x02, 0x1f, 0x09, 0x1b, 0x03, 0x09, 0x1b, 0x03, 0x09,
    0x1b, 0x02, 0x09, 0x1b, 0x1b, 0x09, 0x1b, 0x1b, 0x0b, 0x03, 0x03, 0x0b,
    0x03, 0x03, 0x0b, 0x1b, 0x1b, 0x0a, 0x03, 0x1b, 0x0a, 0x03, 0x1b, 0x0a,
    0x02, 0x20, 0x0a, 0x1b, 0x04, 0x0a, 0x1b, 0x04, 0x0a, 0x1b, 0x1b, 0x0a,
    0x1b, 0x1b, 0x0c, 0x03, 0x1f, 0x0c, 0x04, 0x1b, 0x0c, 0x04, 0x1b, 0x0d,
    0x1b, 0x03, 0x0d, 0x1b, 0x03, 0x0d, 0x1b, 0x1b, 0x0d, 0x1b, 0x20, 0x0f,
    0x02, 0x1b, 0x0f, 0x1b, 0x1b, 0x0f, 0x1b, 0x1b, 0x0f, 0x1b, 0x1f, 0x10,
    0x1b, 0x1b, 0x10, 0x1b, 0x20, 0x10, 0x1b, 0x1f, 0x17, 0x04, 0x1b, 0x17,
    0x04, 0x1b, 0x18, 0x1b, 0x03, 0x18, 0x1b, 0x1b, 0x1a, 0x03, 0x1b, 0x1a,
    0x03, 0x20, 0x1a, 0x03, 0x1f, 0x1a, 0x02, 0x02, 0x1a, 0x02, 0x02, 0x1a,
    0x04, 0x1b, 0x1a, 0x04, 0x1b, 0x1a, 0x1b, 0x03, 0x1a, 0x1b, 0x03, 0x1b,
    0x03, 0x02, 0x1b, 0x03, 0x1b, 0x1b, 0x03, 0x20, 0x1b, 0x02, 0x03, 0x1b,
    0x02, 0x1b, 0x1b, 0x04, 0x02, 0x1b, 0x04, 0x1b, 0x28, 0x06, 0x1d, 0x04,
    0x06, 0x1f, 0x1d, 0x04, 0x1f, 0x1d, 0x1d, 0x1e, 0x05, 0x1d, 0x1e, 0x05,
    0x21, 0x1e, 0x04, 0x1d, 0x1e, 0x04, 0x1d, 0x1e, 0x04, 0x21, 0x1e, 0x1d,
    0x22, 0x1e, 0x1d, 0x21, 0x22, 0x1d, 0x1d, 0x22, 0x1d, 0x1d, 0x00, 0x06,
    0x22, 0x02, 0x04, 0x22, 0x02, 0x04, 0x21, 0x02, 0x06, 0x22, 0x02, 0x06,
    0x21, 0x02, 0x1d, 0x22, 0x02, 0x1d, 0x21, 0x04, 0x1d, 0x22, 0x04, 0x05,
    0x21, 0x04, 0x1d, 0x21, 0x0b, 0x06, 0x21, 0x0d, 0x05, 0x22, 0x0c, 0x05,
    0x22, 0x0e, 0x05, 0x22, 0x1c, 0x04, 0x22, 0x1c, 0x1d, 0x22, 0x22, 0x05,
    0x22, 0x22, 0x04, 0x22, 0x22, 0x1d, 0x22, 0x1d, 0x1d, 0x22, 0x1a, 0x1d,
    0x22, 0x1e, 0x05, 0x22, 0x1a, 0x1d, 0x05, 0x1c, 0x05, 0x1d, 0x11, 0x1d,
    0x22, 0x1b, 0x1d, 0x22, 0x1e, 0x04, 0x05, 0x1d, 0x06, 0x22, 0x1c, 0x04,
    0x1d, 0x1b, 0x1d, 0x1d, 0x1c, 0x04, 0x1d, 0x1e, 0x04, 0x05, 0x04, 0x05,
    0x22, 0x05, 0x04, 0x22, 0x1d, 0x04, 0x22, 0x19, 0x1d, 0x22, 0x00, 0x05,
    0x22, 0x1b, 0x1d, 0x1d, 0x11, 0x04, 0x1d, 0x0d, 0x1d, 0x1d, 0x0b, 0x06,
    0x22, 0x1e, 0x04, 0x22, 0x35, 0x06, 0x00, 0x0f, 0x9d, 0x0d, 0x0f, 0x9d,
    0x27, 0x06, 0x00, 0x1d, 0x1d, 0x20, 0x00, 0x1c, 0x01, 0x0a, 0x1e, 0x06,
    0x1e, 0x08, 0x0e, 0x1d, 0x12, 0x1e, 0x0a, 0x0c, 0x21, 0x1d, 0x12, 0x1d,
    0x23, 0x20, 0x21, 0x0c, 0x1d, 0x1e, 0x35, 0x06, 0x00, 0x0f, 0x14, 0x27,
    0x06, 0x0e, 0x1d, 0x22, 0xff, 0x00, 0x1d, 0x1d, 0x20, 0xff, 0x12, 0x1d,
    0x23, 0x20, 0xff, 0x21, 0x0c, 0x1d, 0x1e, 0x27, 0x06, 0x05, 0x1d, 0xff,
    0x05, 0x1d, 0x00, 0x1d, 0x20, 0x27, 0x06, 0x0a, 0xa5, 0x00, 0x1d, 0x2c,
    0x00, 0x01, 0x30, 0x02, 0x30, 0x3a, 0x00, 0x3b, 0x00, 0x21, 0x00, 0x3f,
    0x00, 0x16, 0x30, 0x17, 0x30, 0x26, 0x20, 0x13, 0x20, 0x12, 0x01, 0x00,
    0x5f, 0x5f, 0x28, 0x29, 0x7b, 0x7d, 0x08, 0x30, 0x0c, 0x0d, 0x08, 0x09,
    0x02, 0x03, 0x00, 0x01, 0x04, 0x05, 0x06, 0x07, 0x5b, 0x00, 0x5d, 0x00,
    0x3e, 0x20, 0x3e, 0x20, 0x3e, 0x20, 0x3e, 0x20, 0x5f, 0x00, 0x5f, 0x00,
    0x5f, 0x00, 0x2c, 0x00, 0x01, 0x30, 0x2e, 0x00, 0x00, 0x00, 0x3b, 0x00,
    0x3a, 0x00, 0x3f, 0x00, 0x21, 0x00, 0x14, 0x20, 0x28, 0x00, 0x29, 0x00,
    0x7b, 0x00, 0x7d, 0x00, 0x14, 0x30, 0x15, 0x30, 0x23, 0x26, 0x2a, 0x2b,
    0x2d, 0x3c, 0x3e, 0x3d, 0x00, 0x5c, 0x24, 0x25, 0x40, 0x40, 0x06, 0xff,
    0x0b, 0x00, 0x0b, 0xff, 0x0c, 0x20, 0x00, 0x4d, 0x06, 0x40, 0x06, 0xff,
    0x0e, 0x00, 0x0e, 0xff, 0x0f, 0x00, 0x0f, 0xff, 0x10, 0x00, 0x10, 0xff,
    0x11, 0x00, 0x11, 0xff, 0x12, 0x00, 0x12, 0x21, 0x06, 0x00, 0x01, 0x01,
    0x02, 0x02, 0x03, 0x03, 0x04, 0x04, 0x05, 0x05, 0x05, 0x05, 0x06, 0x06,
    0x07, 0x07, 0x07, 0x07, 0x08, 0x08, 0x09, 0x09, 0x09, 0x09, 0x0a, 0x0a,
    0x0a, 0x0a, 0x0b, 0x0b, 0x0b, 0x0b, 0x0c, 0x0c, 0x0c, 0x0c, 0x0d, 0x0d,
    0x0d, 0x0d, 0x0e, 0x0e, 0x0f, 0x0f, 0x10, 0x10, 0x11, 0x11, 0x12, 0x12,
    0x12, 0x12, 0x13, 0x13, 0x13, 0x13, 0x14, 0x14, 0x14, 0x14, 0x15, 0x15,
    0x15, 0x15, 0x16, 0x16, 0x16, 0x16, 0x17, 0x17, 0x17, 0x17, 0x18, 0x18,
    0x18, 0x18, 0x19, 0x19, 0x19, 0x19, 0x20, 0x20, 0x20, 0x20, 0x21, 0x21,
    0x21, 0x21, 0x22, 0x22, 0x22, 0x22, 0x23, 0x23, 0x23, 0x23, 0x24, 0x24,
    0x24, 0x24, 0x25, 0x25, 0x25, 0x25, 0x26, 0x26, 0x26, 0x26, 0x27, 0x27,
    0x28, 0x28, 0x29, 0x29, 0x29, 0x29, 0x22, 0x06, 0x22, 0x00, 0x22, 0x00,
    0x22, 0x01, 0x22, 0x01, 0x22, 0x03, 0x22, 0x03, 0x22, 0x05, 0x22, 0x05,
    0x21, 0x00, 0x85, 0x29, 0x01, 0x30, 0x01, 0x0b, 0x0c, 0x00, 0xfa, 0xf1,
    0xa0, 0xa2, 0xa4, 0xa6, 0xa8, 0xe2, 0xe4, 0xe6, 0xc2, 0xfb, 0xa1, 0xa3,
    0xa5, 0xa7, 0xa9, 0xaa, 0xac, 0xae, 0xb0, 0xb2, 0xb4, 0xb6, 0xb8, 0xba,
    0xbc, 0xbe, 0xc0, 0xc3, 0xc5, 0xc7, 0xc9, 0xca, 0xcb, 0xcc, 0xcd, 0xce,
    0xd1, 0xd4, 0xd7, 0xda, 0xdd, 0xde, 0xdf, 0xe0, 0xe1, 0xe3, 0xe5, 0xe7,
    0xe8, 0xe9, 0xea, 0xeb, 0xec, 0xee, 0xf2, 0x98, 0x99, 0x31, 0x31, 0x4f,
    0x31, 0x55, 0x31, 0x5b, 0x31, 0x61, 0x31, 0xa2, 0x00, 0xa3, 0x00, 0xac,
    0x00, 0xaf, 0x00, 0xa6, 0x00, 0xa5, 0x00, 0xa9, 0x20, 0x00, 0x00, 0x02,
    0x25, 0x90, 0x21, 0x91, 0x21, 0x92, 0x21, 0x93, 0x21, 0xa0, 0x25, 0xcb,
    0x25, 0x99, 0x10, 0xba, 0x10, 0x00, 0x00, 0x00, 0x00, 0x9b, 0x10, 0xba,
    0x10, 0x05, 0x05, 0xa5, 0x10, 0xba, 0x10, 0x05, 0x31, 0x11, 0x27, 0x11,
    0x32, 0x11, 0x27, 0x11, 0x55, 0x47, 0x13, 0x3e, 0x13, 0x47, 0x13, 0x57,
    0x13, 0x55, 0xb9, 0x14, 0xba, 0x14, 0xb9, 0x14, 0xb0, 0x14, 0x00, 0x00,
    0x00, 0x00, 0xb9, 0x14, 0xbd, 0x14, 0x55, 0x50, 0xb8, 0x15, 0xaf, 0x15,
    0xb9, 0x15, 0xaf, 0x15, 0x55, 0x57, 0xd1, 0x65, 0xd1, 0x58, 0xd1, 0x65,
    0xd1, 0x5f, 0xd1, 0x6e, 0xd1, 0x5f, 0xd1, 0x6f, 0xd1, 0x5f, 0xd1, 0x70,
    0xd1, 0x5f, 0xd1, 0x71, 0xd1, 0x5f, 0xd1, 0x72, 0xd1, 0x55, 0x55, 0x55,
    0x05, 0xb9, 0xd1, 0x65, 0xd1, 0xba, 0xd1, 0x65, 0xd1, 0xbb, 0xd1, 0x6e,
    0xd1, 0xbc, 0xd1, 0x6e, 0xd1, 0xbb, 0xd1, 0x6f, 0xd1, 0xbc, 0xd1, 0x6f,
    0xd1, 0x55, 0x55, 0x55, 0x41, 0x00, 0x61, 0x00, 0x41, 0x00, 0x61, 0x00,
    0x69, 0x00, 0x41, 0x00, 0x61, 0x00, 0x41, 0x00, 0x43, 0x44, 0x00, 0x00,
    0x47, 0x00, 0x00, 0x4a, 0x4b, 0x00, 0x00, 0x4e, 0x4f, 0x50, 0x51, 0x00,
    0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x61, 0x62, 0x63, 0x64,
    0x00, 0x66, 0x68, 0x00, 0x70, 0x00, 0x41, 0x00, 0x61, 0x00, 0x41, 0x42,
    0x00, 0x44, 0x45, 0x46, 0x47, 0x4a, 0x00, 0x53, 0x00, 0x61, 0x00, 0x41,
    0x42, 0x00, 0x44, 0x45, 0x46, 0x47, 0x00, 0x49, 0x4a, 0x4b, 0x4c, 0x4d,
    0x00, 0x4f, 0x53, 0x00, 0x61, 0x00, 0x41, 0x00, 0x61, 0x00, 0x41, 0x00,
    0x61, 0x00, 0x41, 0x00, 0x61, 0x00, 0x41, 0x00, 0x61, 0x00, 0x41, 0x00,
    0x61, 0x00, 0x41, 0x00, 0x61, 0x00, 0x31, 0x01, 0x37, 0x02, 0x91, 0x03,
    0xa3, 0x03, 0xb1, 0x03, 0xd1, 0x03, 0x24, 0x00, 0x1f, 0x04, 0x20, 0x05,
    0x91, 0x03, 0xa3, 0x03, 0xb1, 0x03, 0xd1, 0x03, 0x24, 0x00, 0x1f, 0x04,
    0x20, 0x05, 0x91, 0x03, 0xa3, 0x03, 0xb1, 0x03, 0xd1, 0x03, 0x24, 0x00,
    0x1f, 0x04, 0x20, 0x05, 0x91, 0x03, 0xa3, 0x03, 0xb1, 0x03, 0xd1, 0x03,
    0x24, 0x00, 0x1f, 0x04, 0x20, 0x05, 0x91, 0x03, 0xa3, 0x03, 0xb1, 0x03,
    0xd1, 0x03, 0x24, 0x00, 0x1f, 0x04, 0x20, 0x05, 0x0b, 0x0c, 0x30, 0x00,
    0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x30, 0x00, 0x27, 0x06, 0x00, 0x01,
    0x05, 0x08, 0x2a, 0x06, 0x1e, 0x08, 0x03, 0x0d, 0x20, 0x19, 0x1a, 0x1b,
    0x1c, 0x09, 0x0f, 0x17, 0x0b, 0x18, 0x07, 0x0a, 0x00, 0x01, 0x04, 0x06,
    0x0c, 0x0e, 0x10, 0x44, 0x90, 0x77, 0x45, 0x28, 0x06, 0x2c, 0x06, 0x00,
    0x00, 0x47, 0x06, 0x33, 0x06, 0x17, 0x10, 0x11, 0x12, 0x13, 0x00, 0x06,
    0x0e, 0x02, 0x0f, 0x34, 0x06, 0x2a, 0x06, 0x2b, 0x06, 0x2e, 0x06, 0x00,
    0x00, 0x36, 0x06, 0x00, 0x00, 0x3a, 0x06, 0x2d, 0x06, 0x00, 0x00, 0x4a,
    0x06, 0x00, 0x00, 0x44, 0x06, 0x00, 0x00, 0x46, 0x06, 0x33, 0x06, 0x39,
    0x06, 0x00, 0x00, 0x35, 0x06, 0x42, 0x06, 0x00, 0x00, 0x34, 0x06, 0x00,
    0x00, 0x00, 0x00, 0x2e, 0x06, 0x00, 0x00, 0x36, 0x06, 0x00, 0x00, 0x3a,
    0x06, 0x00, 0x00, 0xba, 0x06, 0x00, 0x00, 0x6f, 0x06, 0x00, 0x00, 0x28,
    0x06, 0x2c, 0x06, 0x00, 0x00, 0x47, 0x06, 0x00, 0x00, 0x00, 0x00, 0x2d,
    0x06, 0x37, 0x06, 0x4a, 0x06, 0x43, 0x06, 0x00, 0x00, 0x45, 0x06, 0x46,
    0x06, 0x33, 0x06, 0x39, 0x06, 0x41, 0x06, 0x35, 0x06, 0x42, 0x06, 0x00,
    0x00, 0x34, 0x06, 0x2a, 0x06, 0x2b, 0x06, 0x2e, 0x06, 0x00, 0x00, 0x36,
    0x06, 0x38, 0x06, 0x3a, 0x06, 0x6e, 0x06, 0x00, 0x00, 0xa1, 0x06, 0x27,
    0x06, 0x00, 0x01, 0x05, 0x08, 0x20, 0x21, 0x0b, 0x06, 0x10, 0x23, 0x2a,
    0x06, 0x1a, 0x1b, 0x1c, 0x09, 0x0f, 0x17, 0x0b, 0x18, 0x07, 0x0a, 0x00,
    0x01, 0x04, 0x06, 0x0c, 0x0e, 0x10, 0x28, 0x06, 0x2c, 0x06, 0x2f, 0x06,
    0x00, 0x00, 0x48, 0x06, 0x32, 0x06, 0x2d, 0x06, 0x37, 0x06, 0x4a, 0x06,
    0x2a, 0x06, 0x1a, 0x1b, 0x1c, 0x09, 0x0f, 0x17, 0x0b, 0x18, 0x07, 0x0a,
    0x00, 0x01, 0x04, 0x06, 0x0c, 0x0e, 0x10, 0x30, 0x2e, 0x30, 0x00, 0x2c,
    0x00, 0x28, 0x00, 0x41, 0x00, 0x29, 0x00, 0x14, 0x30, 0x53, 0x00, 0x15,
    0x30, 0x43, 0x52, 0x43, 0x44, 0x57, 0x5a, 0x41, 0x00, 0x48, 0x56, 0x4d,
    0x56, 0x53, 0x44, 0x53, 0x53, 0x50, 0x50, 0x56, 0x57, 0x43, 0x4d, 0x43,
    0x4d, 0x44, 0x4d, 0x52, 0x44, 0x4a, 0x4b, 0x30, 0x30, 0x00, 0x68, 0x68,
    0x4b, 0x62, 0x57, 0x5b, 0xcc, 0x53, 0xc7, 0x30, 0x8c, 0x4e, 0x1a, 0x59,
    0xe3, 0x89, 0x29, 0x59, 0xa4, 0x4e, 0x20, 0x66, 0x21, 0x71, 0x99, 0x65,
    0x4d, 0x52, 0x8c, 0x5f, 0x8d, 0x51, 0xb0, 0x65, 0x1d, 0x52, 0x42, 0x7d,
    0x1f, 0x75, 0xa9, 0x8c, 0xf0, 0x58, 0x39, 0x54, 0x14, 0x6f, 0x95, 0x62,
    0x55, 0x63, 0x00, 0x4e, 0x09, 0x4e, 0x4a, 0x90, 0xe6, 0x5d, 0x2d, 0x4e,
    0xf3, 0x53, 0x07, 0x63, 0x70, 0x8d, 0x53, 0x62, 0x81, 0x79, 0x7a, 0x7a,
    0x08, 0x54, 0x80, 0x6e, 0x09, 0x67, 0x08, 0x67, 0x33, 0x75, 0x72, 0x52,
    0xb6, 0x55, 0x4d, 0x91, 0x14, 0x30, 0x15, 0x30, 0x2c, 0x67, 0x09, 0x4e,
    0x8c, 0x4e, 0x89, 0x5b, 0xb9, 0x70, 0x53, 0x62, 0xd7, 0x76, 0xdd, 0x52,
    0x57, 0x65, 0x97, 0x5f, 0xef, 0x53, 0x38, 0x4e, 0x05, 0x00, 0x09, 0x22,
    0x01, 0x60, 0x4f, 0xae, 0x4f, 0xbb, 0x4f, 0x02, 0x50, 0x7a, 0x50, 0x99,
    0x50, 0xe7, 0x50, 0xcf, 0x50, 0x9e, 0x34, 0x3a, 0x06, 0x4d, 0x51, 0x54,
    0x51, 0x64, 0x51, 0x77, 0x51, 0x1c, 0x05, 0xb9, 0x34, 0x67, 0x51, 0x8d,
    0x51, 0x4b, 0x05, 0x97, 0x51, 0xa4, 0x51, 0xcc, 0x4e, 0xac, 0x51, 0xb5,
    0x51, 0xdf, 0x91, 0xf5, 0x51, 0x03, 0x52, 0xdf, 0x34, 0x3b, 0x52, 0x46,
    0x52, 0x72, 0x52, 0x77, 0x52, 0x15, 0x35, 0x02, 0x00, 0x20, 0x80, 0x80,
    0x00, 0x08, 0x00, 0x00, 0xc7, 0x52, 0x00, 0x02, 0x1d, 0x33, 0x3e, 0x3f,
    0x50, 0x82, 0x8a, 0x93, 0xac, 0xb6, 0xb8, 0xb8, 0xb8, 0x2c, 0x0a, 0x70,
    0x70, 0xca, 0x53, 0xdf, 0x53, 0x63, 0x0b, 0xeb, 0x53, 0xf1, 0x53, 0x06,
    0x54, 0x9e, 0x54, 0x38, 0x54, 0x48, 0x54, 0x68, 0x54, 0xa2, 0x54, 0xf6,
    0x54, 0x10, 0x55, 0x53, 0x55, 0x63, 0x55, 0x84, 0x55, 0x84, 0x55, 0x99,
    0x55, 0xab, 0x55, 0xb3, 0x55, 0xc2, 0x55, 0x16, 0x57, 0x06, 0x56, 0x17,
    0x57, 0x51, 0x56, 0x74, 0x56, 0x07, 0x52, 0xee, 0x58, 0xce, 0x57, 0xf4,
    0x57, 0x0d, 0x58, 0x8b, 0x57, 0x32, 0x58, 0x31, 0x58, 0xac, 0x58, 0xe4,
    0x14, 0xf2, 0x58, 0xf7, 0x58, 0x06, 0x59, 0x1a, 0x59, 0x22, 0x59, 0x62,
    0x59, 0xa8, 0x16, 0xea, 0x16, 0xec, 0x59, 0x1b, 0x5a, 0x27, 0x5a, 0xd8,
    0x59, 0x66, 0x5a, 0xee, 0x36, 0xfc, 0x36, 0x08, 0x5b, 0x3e, 0x5b, 0x3e,
    0x5b, 0xc8, 0x19, 0xc3, 0x5b, 0xd8, 0x5b, 0xe7, 0x5b, 0xf3, 0x5b, 0x18,
    0x1b, 0xff, 0x5b, 0x06, 0x5c, 0x53, 0x5f, 0x22, 0x5c, 0x81, 0x37, 0x60,
    0x5c, 0x6e, 0x5c, 0xc0, 0x5c, 0x8d, 0x5c, 0xe4, 0x1d, 0x43, 0x5d, 0xe6,
    0x1d, 0x6e, 0x5d, 0x6b, 0x5d, 0x7c, 0x5d, 0xe1, 0x5d, 0xe2, 0x5d, 0x2f,
    0x38, 0xfd, 0x5d, 0x28, 0x5e, 0x3d, 0x5e, 0x69, 0x5e, 0x62, 0x38, 0x83,
    0x21, 0x7c, 0x38, 0xb0, 0x5e, 0xb3, 0x5e, 0xb6, 0x5e, 0xca, 0x5e, 0x92,
    0xa3, 0xfe, 0x5e, 0x31, 0x23, 0x31, 0x23, 0x01, 0x82, 0x22, 0x5f, 0x22,
    0x5f, 0xc7, 0x38, 0xb8, 0x32, 0xda, 0x61, 0x62, 0x5f, 0x6b, 0x5f, 0xe3,
    0x38, 0x9a, 0x5f, 0xcd, 0x5f, 0xd7, 0x5f, 0xf9, 0x5f, 0x81, 0x60, 0x3a,
    0x39, 0x1c, 0x39, 0x94, 0x60, 0xd4, 0x26, 0xc7, 0x60, 0x02, 0x02, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x0a, 0x00, 0x00, 0x02,
    0x08, 0x00, 0x80, 0x08, 0x00, 0x00, 0x08, 0x80, 0x28, 0x80, 0x02, 0x00,
    0x00, 0x02, 0x48, 0x61, 0x00, 0x04, 0x06, 0x04, 0x32, 0x46, 0x6a, 0x5c,
    0x67, 0x96, 0xaa, 0xae, 0xc8, 0xd3, 0x5d, 0x62, 0x00, 0x54, 0x77, 0xf3,
    0x0c, 0x2b, 0x3d, 0x63, 0xfc, 0x62, 0x68, 0x63, 0x83, 0x63, 0xe4, 0x63,
    0xf1, 0x2b, 0x22, 0x64, 0xc5, 0x63, 0xa9, 0x63, 0x2e, 0x3a, 0x69, 0x64,
    0x7e, 0x64, 0x9d, 0x64, 0x77, 0x64, 0x6c, 0x3a, 0x4f, 0x65, 0x6c, 0x65,
    0x0a, 0x30, 0xe3, 0x65, 0xf8, 0x66, 0x49, 0x66, 0x19, 0x3b, 0x91, 0x66,
    0x08, 0x3b, 0xe4, 0x3a, 0x92, 0x51, 0x95, 0x51, 0x00, 0x67, 0x9c, 0x66,
    0xad, 0x80, 0xd9, 0x43, 0x17, 0x67, 0x1b, 0x67, 0x21, 0x67, 0x5e, 0x67,
    0x53, 0x67, 0xc3, 0x33, 0x49, 0x3b, 0xfa, 0x67, 0x85, 0x67, 0x52, 0x68,
    0x85, 0x68, 0x6d, 0x34, 0x8e, 0x68, 0x1f, 0x68, 0x14, 0x69, 0x9d, 0x3b,
    0x42, 0x69, 0xa3, 0x69, 0xea, 0x69, 0xa8, 0x6a, 0xa3, 0x36, 0xdb, 0x6a,
    0x18, 0x3c, 0x21, 0x6b, 0xa7, 0x38, 0x54, 0x6b, 0x4e, 0x3c, 0x72, 0x6b,
    0x9f, 0x6b, 0xba, 0x6b, 0xbb, 0x6b, 0x8d, 0x3a, 0x0b, 0x1d, 0xfa, 0x3a,
    0x4e, 0x6c, 0xbc, 0x3c, 0xbf, 0x6c, 0xcd, 0x6c, 0x67, 0x6c, 0x16, 0x6d,
    0x3e, 0x6d, 0x77, 0x6d, 0x41, 0x6d, 0x69, 0x6d, 0x78, 0x6d, 0x85, 0x6d,
    0x1e, 0x3d, 0x34, 0x6d, 0x2f, 0x6e, 0x6e, 0x6e, 0x33, 0x3d, 0xcb, 0x6e,
    0xc7, 0x6e, 0xd1, 0x3e, 0xf9, 0x6d, 0x6e, 0x6f, 0x5e, 0x3f, 0x8e, 0x3f,
    0xc6, 0x6f, 0x39, 0x70, 0x1e, 0x70, 0x1b, 0x70, 0x96, 0x3d, 0x4a, 0x70,
    0x7d, 0x70, 0x77, 0x70, 0xad, 0x70, 0x25, 0x05, 0x45, 0x71, 0x63, 0x42,
    0x9c, 0x71, 0xab, 0x43, 0x28, 0x72, 0x35, 0x72, 0x50, 0x72, 0x08, 0x46,
    0x80, 0x72, 0x95, 0x72, 0x35, 0x47, 0x02, 0x20, 0x00, 0x00, 0x20, 0x00,
    0x00, 0x00, 0x00, 0x08, 0x80, 0x00, 0x00, 0x02, 0x02, 0x80, 0x8a, 0x00,
    0x00, 0x20, 0x00, 0x08, 0x0a, 0x00, 0x80, 0x88, 0x80, 0x20, 0x14, 0x48,
    0x7a, 0x73, 0x8b, 0x73, 0xac, 0x3e, 0xa5, 0x73, 0xb8, 0x3e, 0xb8, 0x3e,
    0x47, 0x74, 0x5c, 0x74, 0x71, 0x74, 0x85, 0x74, 0xca, 0x74, 0x1b, 0x3f,
    0x24, 0x75, 0x36, 0x4c, 0x3e, 0x75, 0x92, 0x4c, 0x70, 0x75, 0x9f, 0x21,
    0x10, 0x76, 0xa1, 0x4f, 0xb8, 0x4f, 0x44, 0x50, 0xfc, 0x3f, 0x08, 0x40,
    0xf4, 0x76, 0xf3, 0x50, 0xf2, 0x50, 0x19, 0x51, 0x33, 0x51, 0x1e, 0x77,
    0x1f, 0x77, 0x1f, 0x77, 0x4a, 0x77, 0x39, 0x40, 0x8b, 0x77, 0x46, 0x40,
    0x96, 0x40, 0x1d, 0x54, 0x4e, 0x78, 0x8c, 0x78, 0xcc, 0x78, 0xe3, 0x40,
    0x26, 0x56, 0x56, 0x79, 0x9a, 0x56, 0xc5, 0x56, 0x8f, 0x79, 0xeb, 0x79,
    0x2f, 0x41, 0x40, 0x7a, 0x4a, 0x7a, 0x4f, 0x7a, 0x7c, 0x59, 0xa7, 0x5a,
    0xa7, 0x5a, 0xee, 0x7a, 0x02, 0x42, 0xab, 0x5b, 0xc6, 0x7b, 0xc9, 0x7b,
    0x27, 0x42, 0x80, 0x5c, 0xd2, 0x7c, 0xa0, 0x42, 0xe8, 0x7c, 0xe3, 0x7c,
    0x00, 0x7d, 0x86, 0x5f, 0x63, 0x7d, 0x01, 0x43, 0xc7, 0x7d, 0x02, 0x7e,
    0x45, 0x7e, 0x34, 0x43, 0x28, 0x62, 0x47, 0x62, 0x59, 0x43, 0xd9, 0x62,
    0x7a, 0x7f, 0x3e, 0x63, 0x95, 0x7f, 0xfa, 0x7f, 0x05, 0x80, 0xda, 0x64,
    0x23, 0x65, 0x60, 0x80, 0xa8, 0x65, 0x70, 0x80, 0x5f, 0x33, 0xd5, 0x43,
    0xb2, 0x80, 0x03, 0x81, 0x0b, 0x44, 0x3e, 0x81, 0xb5, 0x5a, 0xa7, 0x67,
    0xb5, 0x67, 0x93, 0x33, 0x9c, 0x33, 0x01, 0x82, 0x04, 0x82, 0x9e, 0x8f,
    0x6b, 0x44, 0x91, 0x82, 0x8b, 0x82, 0x9d, 0x82, 0xb3, 0x52, 0xb1, 0x82,
    0xb3, 0x82, 0xbd, 0x82, 0xe6, 0x82, 0x3c, 0x6b, 0xe5, 0x82, 0x1d, 0x83,
    0x63, 0x83, 0xad, 0x83, 0x23, 0x83, 0xbd, 0x83, 0xe7, 0x83, 0x57, 0x84,
    0x53, 0x83, 0xca, 0x83, 0xcc, 0x83, 0xdc, 0x83, 0x36, 0x6c, 0x6b, 0x6d,
    0x02, 0x00, 0x00, 0x20, 0x22, 0x2a, 0xa0, 0x0a, 0x00, 0x20, 0x80, 0x28,
    0x00, 0xa8, 0x20, 0x20, 0x00, 0x02, 0x80, 0x22, 0x02, 0x8a, 0x08, 0x00,
    0xaa, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x28, 0xd5, 0x6c, 0x2b, 0x45,
    0xf1, 0x84, 0xf3, 0x84, 0x16, 0x85, 0xca, 0x73, 0x64, 0x85, 0x2c, 0x6f,
    0x5d, 0x45, 0x61, 0x45, 0xb1, 0x6f, 0xd2, 0x70, 0x6b, 0x45, 0x50, 0x86,
    0x5c, 0x86, 0x67, 0x86, 0x69, 0x86, 0xa9, 0x86, 0x88, 0x86, 0x0e, 0x87,
    0xe2, 0x86, 0x79, 0x87, 0x28, 0x87, 0x6b, 0x87, 0x86, 0x87, 0xd7, 0x45,
    0xe1, 0x87, 0x01, 0x88, 0xf9, 0x45, 0x60, 0x88, 0x63, 0x88, 0x67, 0x76,
    0xd7, 0x88, 0xde, 0x88, 0x35, 0x46, 0xfa, 0x88, 0xbb, 0x34, 0xae, 0x78,
    0x66, 0x79, 0xbe, 0x46, 0xc7, 0x46, 0xa0, 0x8a, 0xed, 0x8a, 0x8a, 0x8b,
    0x55, 0x8c, 0xa8, 0x7c, 0xab, 0x8c, 0xc1, 0x8c, 0x1b, 0x8d, 0x77, 0x8d,
    0x2f, 0x7f, 0x04, 0x08, 0xcb, 0x8d, 0xbc, 0x8d, 0xf0, 0x8d, 0xde, 0x08,
    0xd4, 0x8e, 0x38, 0x8f, 0xd2, 0x85, 0xed, 0x85, 0x94, 0x90, 0xf1, 0x90,
    0x11, 0x91, 0x2e, 0x87, 0x1b, 0x91, 0x38, 0x92, 0xd7, 0x92, 0xd8, 0x92,
    0x7c, 0x92, 0xf9, 0x93, 0x15, 0x94, 0xfa, 0x8b, 0x8b, 0x95, 0x95, 0x49,
    0xb7, 0x95, 0x77, 0x8d, 0xe6, 0x49, 0xc3, 0x96, 0xb2, 0x5d, 0x23, 0x97,
    0x45, 0x91, 0x1a, 0x92, 0x6e, 0x4a, 0x76, 0x4a, 0xe0, 0x97, 0x0a, 0x94,
    0xb2, 0x4a, 0x96, 0x94, 0x0b, 0x98, 0x0b, 0x98, 0x29, 0x98, 0xb6, 0x95,
    0xe2, 0x98, 0x33, 0x4b, 0x29, 0x99, 0xa7, 0x99, 0xc2, 0x99, 0xfe, 0x99,
    0xce, 0x4b, 0x30, 0x9b, 0x12, 0x9b, 0x40, 0x9c, 0xfd, 0x9c, 0xce, 0x4c,
    0xed, 0x4c, 0x67, 0x9d, 0xce, 0xa0, 0xf8, 0x4c, 0x05, 0xa1, 0x0e, 0xa2,
    0x91, 0xa2, 0xbb, 0x9e, 0x56, 0x4d, 0xf9, 0x9e, 0xfe, 0x9e, 0x05, 0x9f,
    0x0f, 0x9f, 0x16, 0x9f, 0x3b, 0x9f, 0x00, 0xa6, 0x02, 0x88, 0xa0, 0x00,
    0x00, 0x00, 0x00, 0x80, 0x00, 0x28, 0x00, 0x08, 0xa0, 0x80, 0xa0, 0x80,
    0x00, 0x80, 0x80, 0x00, 0x0a, 0x88, 0x80, 0x00, 0x80, 0x00, 0x20, 0x2a,
    0x00, 0x80,
};

static const uint16_t unicode_comp_table[944] = {
    0x4a01, 0x49c0, 0x4a02, 0x0280, 0x0281, 0x0282, 0x0283, 0x02c0, 0x02c2,
    0x0a00, 0x0284, 0x2442, 0x0285, 0x07c0, 0x0980, 0x0982, 0x2440, 0x2280,
    0x02c4, 0x2282, 0x2284, 0x2286, 0x02c6, 0x02c8, 0x02ca, 0x02cc, 0x0287,
    0x228a, 0x02ce, 0x228c, 0x2290, 0x2292, 0x228e, 0x0288, 0x0289, 0x028a,
    0x2482, 0x0300, 0x0302, 0x0304, 0x028b, 0x2480, 0x0308, 0x0984, 0x0986,
    0x2458, 0x0a02, 0x0306, 0x2298, 0x229a, 0x229e, 0x0900, 0x030a, 0x22a0,
    0x030c, 0x030e, 0x0840, 0x0310, 0x0312, 0x22a2, 0x22a6, 0x09c0, 0x22a4,
    0x22a8, 0x22aa, 0x028c, 0x028d, 0x028e, 0x0340, 0x0342, 0x0344, 0x0380,
    0x028f, 0x248e, 0x07c2, 0x0988, 0x098a, 0x2490, 0x0346, 0x22ac, 0x0400,
    0x22b0, 0x0842, 0x22b2, 0x0402, 0x22b4, 0x0440, 0x0444, 0x22b6, 0x0442,
    0x22c2, 0x22c0, 0x22c4, 0x22c6, 0x22c8, 0x0940, 0x04c0, 0x0291, 0x22ca,
    0x04c4, 0x22cc, 0x04c2, 0x22d0, 0x22ce, 0x0292, 0x0293, 0x0294, 0x0295,
    0x0540, 0x0542, 0x0a08, 0x0296, 0x2494, 0x0544, 0x07c4, 0x098c, 0x098e,
    0x06c0, 0x2492, 0x0844, 0x2308, 0x230a, 0x0580, 0x230c, 0x0584, 0x0990,
    0x0992, 0x230e, 0x0582, 0x2312, 0x0586, 0x0588, 0x2314, 0x058c, 0x2316,
    0x0998, 0x058a, 0x231e, 0x0590, 0x2320, 0x099a, 0x058e, 0x2324, 0x2322,
    0x0299, 0x029a, 0x029b, 0x05c0, 0x05c2, 0x05c4, 0x029c, 0x24ac, 0x05c6,
    0x05c8, 0x07c6, 0x0994, 0x0996, 0x0700, 0x24aa, 0x2326, 0x05ca, 0x232a,
    0x2328, 0x2340, 0x2342, 0x2344, 0x2346, 0x05cc, 0x234a, 0x2348, 0x234c,
    0x234e, 0x2350, 0x24b8, 0x029d, 0x05ce, 0x24be, 0x0a0c, 0x2352, 0x0600,
    0x24bc, 0x24ba, 0x0640, 0x2354, 0x0642, 0x0644, 0x2356, 0x2358, 0x02a0,
    0x02a1, 0x02a2, 0x02a3, 0x02c1, 0x02c3, 0x0a01, 0x02a4, 0x2443, 0x02a5,
    0x07c1, 0x0981, 0x0983, 0x2441, 0x2281, 0x02c5, 0x2283, 0x2285, 0x2287,
    0x02c7, 0x02c9, 0x02cb, 0x02cd, 0x02a7, 0x228b, 0x02cf, 0x228d, 0x2291,
    0x2293, 0x228f, 0x02a8, 0x02a9, 0x02aa, 0x2483, 0x0301, 0x0303, 0x0305,
    0x02ab, 0x2481, 0x0309, 0x0985, 0x0987, 0x2459, 0x0a03, 0x0307, 0x2299,
    0x229b, 0x229f, 0x0901, 0x030b, 0x22a1, 0x030d, 0x030f, 0x0841, 0x0311,
    0x0313, 0x22a3, 0x22a7, 0x09c1, 0x22a5, 0x22a9, 0x22ab, 0x2380, 0x02ac,
    0x02ad, 0x02ae, 0x0341, 0x0343, 0x0345, 0x02af, 0x248f, 0x07c3, 0x0989,
    0x098b, 0x2491, 0x0347, 0x22ad, 0x0401, 0x0884, 0x22b1, 0x0843, 0x22b3,
    0x0403, 0x22b5, 0x0441, 0x0445, 0x22b7, 0x0443, 0x22c3, 0x22c1, 0x22c5,
    0x22c7, 0x22c9, 0x0941, 0x04c1, 0x02b1, 0x22cb, 0x04c5, 0x22cd, 0x04c3,
    0x22d1, 0x22cf, 0x02b2, 0x02b3, 0x02b4, 0x02b5, 0x0541, 0x0543, 0x0a09,
    0x02b6, 0x2495, 0x0545, 0x07c5, 0x098d, 0x098f, 0x06c1, 0x2493, 0x0845,
    0x2309, 0x230b, 0x0581, 0x230d, 0x0585, 0x0991, 0x0993, 0x230f, 0x0583,
    0x2313, 0x0587, 0x0589, 0x2315, 0x058d, 0x2317, 0x0999, 0x058b, 0x231f,
    0x2381, 0x0591, 0x2321, 0x099b, 0x058f, 0x2325, 0x2323, 0x02b9, 0x02ba,
    0x02bb, 0x05c1, 0x05c3, 0x05c5, 0x02bc, 0x24ad, 0x05c7, 0x05c9, 0x07c7,
    0x0995, 0x0997, 0x0701, 0x24ab, 0x2327, 0x05cb, 0x232b, 0x2329, 0x2341,
    0x2343, 0x2345, 0x2347, 0x05cd, 0x234b, 0x2349, 0x2382, 0x234d, 0x234f,
    0x2351, 0x24b9, 0x02bd, 0x05cf, 0x24bf, 0x0a0d, 0x2353, 0x02bf, 0x24bd,
    0x2383, 0x24bb, 0x0641, 0x2355, 0x0643, 0x0645, 0x2357, 0x2359, 0x3101,
    0x0c80, 0x2e00, 0x2446, 0x2444, 0x244a, 0x2448, 0x0800, 0x0942, 0x0944,
    0x0804, 0x2288, 0x2486, 0x2484, 0x248a, 0x2488, 0x22ae, 0x2498, 0x2496,
    0x249c, 0x249a, 0x2300, 0x0a06, 0x2302, 0x0a04, 0x0946, 0x07ce, 0x07ca,
    0x07c8, 0x07cc, 0x2447, 0x2445, 0x244b, 0x2449, 0x0801, 0x0943, 0x0945,
    0x0805, 0x2289, 0x2487, 0x2485, 0x248b, 0x2489, 0x22af, 0x2499, 0x2497,
    0x249d, 0x249b, 0x2301, 0x0a07, 0x2303, 0x0a05, 0x0947, 0x07cf, 0x07cb,
    0x07c9, 0x07cd, 0x2450, 0x244e, 0x2454, 0x2452, 0x2451, 0x244f, 0x2455,
    0x2453, 0x2294, 0x2296, 0x2295, 0x2297, 0x2304, 0x2306, 0x2305, 0x2307,
    0x2318, 0x2319, 0x231a, 0x231b, 0x232c, 0x232d, 0x232e, 0x232f, 0x2400,
    0x24a2, 0x24a0, 0x24a6, 0x24a4, 0x24a8, 0x24a3, 0x24a1, 0x24a7, 0x24a5,
    0x24a9, 0x24b0, 0x24ae, 0x24b4, 0x24b2, 0x24b6, 0x24b1, 0x24af, 0x24b5,
    0x24b3, 0x24b7, 0x0882, 0x0880, 0x0881, 0x0802, 0x0803, 0x229c, 0x229d,
    0x0a0a, 0x0a0b, 0x0883, 0x0b40, 0x2c8a, 0x0c81, 0x2c89, 0x2c88, 0x2540,
    0x2541, 0x2d00, 0x2e07, 0x0d00, 0x2640, 0x2641, 0x2e80, 0x0d01, 0x26c8,
    0x26c9, 0x2f00, 0x2f84, 0x0d02, 0x2f83, 0x2f82, 0x0d40, 0x26d8, 0x26d9,
    0x3186, 0x0d04, 0x2740, 0x2741, 0x3100, 0x3086, 0x0d06, 0x3085, 0x3084,
    0x0d41, 0x2840, 0x3200, 0x0d07, 0x284f, 0x2850, 0x3280, 0x2c84, 0x2e03,
    0x2857, 0x0d42, 0x2c81, 0x2c80, 0x24c0, 0x24c1, 0x2c86, 0x2c83, 0x28c0,
    0x0d43, 0x25c0, 0x25c1, 0x2940, 0x0d44, 0x26c0, 0x26c1, 0x2e05, 0x2e02,
    0x29c0, 0x0d45, 0x2f05, 0x2f04, 0x0d80, 0x26d0, 0x26d1, 0x2f80, 0x2a40,
    0x0d82, 0x26e0, 0x26e1, 0x3080, 0x3081, 0x2ac0, 0x0d83, 0x3004, 0x3003,
    0x0d81, 0x27c0, 0x27c1, 0x3082, 0x2b40, 0x0d84, 0x2847, 0x2848, 0x3184,
    0x3181, 0x2f06, 0x0d08, 0x2f81, 0x3005, 0x0d46, 0x3083, 0x3182, 0x0e00,
    0x0e01, 0x0f40, 0x1180, 0x1182, 0x0f03, 0x0f00, 0x11c0, 0x0f01, 0x1140,
    0x1202, 0x1204, 0x0f81, 0x1240, 0x0fc0, 0x1242, 0x0f80, 0x1244, 0x1284,
    0x0f82, 0x1286, 0x1288, 0x128a, 0x12c0, 0x1282, 0x1181, 0x1183, 0x1043,
    0x1040, 0x11c1, 0x1041, 0x1141, 0x1203, 0x1205, 0x10c1, 0x1241, 0x1000,
    0x1243, 0x10c0, 0x1245, 0x1285, 0x10c2, 0x1287, 0x1289, 0x128b, 0x12c1,
    0x1283, 0x1080, 0x1100, 0x1101, 0x1200, 0x1201, 0x1280, 0x1281, 0x1340,
    0x1341, 0x1343, 0x1342, 0x1344, 0x13c2, 0x1400, 0x13c0, 0x1440, 0x1480,
    0x14c0, 0x1540, 0x1541, 0x1740, 0x1700, 0x1741, 0x17c0, 0x1800, 0x1802,
    0x1801, 0x1840, 0x1880, 0x1900, 0x18c0, 0x18c1, 0x1901, 0x1940, 0x1942,
    0x1941, 0x1980, 0x19c0, 0x19c2, 0x19c1, 0x1c80, 0x1cc0, 0x1dc0, 0x1f80,
    0x2000, 0x2002, 0x2004, 0x2006, 0x2008, 0x2040, 0x2080, 0x2082, 0x20c0,
    0x20c1, 0x2100, 0x22b8, 0x22b9, 0x2310, 0x2311, 0x231c, 0x231d, 0x244c,
    0x2456, 0x244d, 0x2457, 0x248c, 0x248d, 0x249e, 0x249f, 0x2500, 0x2502,
    0x2504, 0x2bc0, 0x2501, 0x2503, 0x2505, 0x2bc1, 0x2bc2, 0x2bc3, 0x2bc4,
    0x2bc5, 0x2bc6, 0x2bc7, 0x2580, 0x2582, 0x2584, 0x2bc8, 0x2581, 0x2583,
    0x2585, 0x2bc9, 0x2bca, 0x2bcb, 0x2bcc, 0x2bcd, 0x2bce, 0x2bcf, 0x2600,
    0x2602, 0x2601, 0x2603, 0x2680, 0x2682, 0x2681, 0x2683, 0x26c2, 0x26c4,
    0x26c6, 0x2c00, 0x26c3, 0x26c5, 0x26c7, 0x2c01, 0x2c02, 0x2c03, 0x2c04,
    0x2c05, 0x2c06, 0x2c07, 0x26ca, 0x26cc, 0x26ce, 0x2c08, 0x26cb, 0x26cd,
    0x26cf, 0x2c09, 0x2c0a, 0x2c0b, 0x2c0c, 0x2c0d, 0x2c0e, 0x2c0f, 0x26d2,
    0x26d4, 0x26d6, 0x26d3, 0x26d5, 0x26d7, 0x26da, 0x26dc, 0x26de, 0x26db,
    0x26dd, 0x26df, 0x2700, 0x2702, 0x2701, 0x2703, 0x2780, 0x2782, 0x2781,
    0x2783, 0x2800, 0x2802, 0x2804, 0x2801, 0x2803, 0x2805, 0x2842, 0x2844,
    0x2846, 0x2849, 0x284b, 0x284d, 0x2c40, 0x284a, 0x284c, 0x284e, 0x2c41,
    0x2c42, 0x2c43, 0x2c44, 0x2c45, 0x2c46, 0x2c47, 0x2851, 0x2853, 0x2855,
    0x2c48, 0x2852, 0x2854, 0x2856, 0x2c49, 0x2c4a, 0x2c4b, 0x2c4c, 0x2c4d,
    0x2c4e, 0x2c4f, 0x2c82, 0x2e01, 0x3180, 0x2c87, 0x2f01, 0x2f02, 0x2f03,
    0x2e06, 0x3185, 0x3000, 0x3001, 0x3002, 0x4640, 0x4641, 0x4680, 0x46c0,
    0x46c2, 0x46c1, 0x4700, 0x4740, 0x4780, 0x47c0, 0x47c2, 0x4900, 0x4940,
    0x4980, 0x4982, 0x4a00, 0x49c2, 0x4a03, 0x4a04, 0x4a40, 0x4a41, 0x4a80,
    0x4a81, 0x4ac0, 0x4ac1, 0x4bc0, 0x4bc1, 0x4b00, 0x4b01, 0x4b40, 0x4b41,
    0x4bc2, 0x4bc3, 0x4b80, 0x4b81, 0x4b82, 0x4b83, 0x4c00, 0x4c01, 0x4c02,
    0x4c03, 0x5600, 0x5440, 0x5442, 0x5444, 0x5446, 0x5448, 0x544a, 0x544c,
    0x544e, 0x5450, 0x5452, 0x5454, 0x5456, 0x5480, 0x5482, 0x5484, 0x54c0,
    0x54c1, 0x5500, 0x5501, 0x5540, 0x5541, 0x5580, 0x5581, 0x55c0, 0x55c1,
    0x5680, 0x58c0, 0x5700, 0x5702, 0x5704, 0x5706, 0x5708, 0x570a, 0x570c,
    0x570e, 0x5710, 0x5712, 0x5714, 0x5716, 0x5740, 0x5742, 0x5744, 0x5780,
    0x5781, 0x57c0, 0x57c1, 0x5800, 0x5801, 0x5840, 0x5841, 0x5880, 0x5881,
    0x5900, 0x5901, 0x5902, 0x5903, 0x5940, 0x8e40, 0x8e42, 0x8e80, 0x8ec0,
    0x8ec1, 0x8f00, 0x8f01, 0x8f41, 0x8f40, 0x8f43, 0x8f80, 0x8f81,
};

typedef enum {
  UNICODE_GC_Cn,
  UNICODE_GC_Lu,
  UNICODE_GC_Ll,
  UNICODE_GC_Lt,
  UNICODE_GC_Lm,
  UNICODE_GC_Lo,
  UNICODE_GC_Mn,
  UNICODE_GC_Mc,
  UNICODE_GC_Me,
  UNICODE_GC_Nd,
  UNICODE_GC_Nl,
  UNICODE_GC_No,
  UNICODE_GC_Sm,
  UNICODE_GC_Sc,
  UNICODE_GC_Sk,
  UNICODE_GC_So,
  UNICODE_GC_Pc,
  UNICODE_GC_Pd,
  UNICODE_GC_Ps,
  UNICODE_GC_Pe,
  UNICODE_GC_Pi,
  UNICODE_GC_Pf,
  UNICODE_GC_Po,
  UNICODE_GC_Zs,
  UNICODE_GC_Zl,
  UNICODE_GC_Zp,
  UNICODE_GC_Cc,
  UNICODE_GC_Cf,
  UNICODE_GC_Cs,
  UNICODE_GC_Co,
  UNICODE_GC_LC,
  UNICODE_GC_L,
  UNICODE_GC_M,
  UNICODE_GC_N,
  UNICODE_GC_S,
  UNICODE_GC_P,
  UNICODE_GC_Z,
  UNICODE_GC_C,
  UNICODE_GC_COUNT,
} UnicodeGCEnum;

static const char unicode_gc_name_table[] =
    "Cn,Unassigned"
    "\0"
    "Lu,Uppercase_Letter"
    "\0"
    "Ll,Lowercase_Letter"
    "\0"
    "Lt,Titlecase_Letter"
    "\0"
    "Lm,Modifier_Letter"
    "\0"
    "Lo,Other_Letter"
    "\0"
    "Mn,Nonspacing_Mark"
    "\0"
    "Mc,Spacing_Mark"
    "\0"
    "Me,Enclosing_Mark"
    "\0"
    "Nd,Decimal_Number,digit"
    "\0"
    "Nl,Letter_Number"
    "\0"
    "No,Other_Number"
    "\0"
    "Sm,Math_Symbol"
    "\0"
    "Sc,Currency_Symbol"
    "\0"
    "Sk,Modifier_Symbol"
    "\0"
    "So,Other_Symbol"
    "\0"
    "Pc,Connector_Punctuation"
    "\0"
    "Pd,Dash_Punctuation"
    "\0"
    "Ps,Open_Punctuation"
    "\0"
    "Pe,Close_Punctuation"
    "\0"
    "Pi,Initial_Punctuation"
    "\0"
    "Pf,Final_Punctuation"
    "\0"
    "Po,Other_Punctuation"
    "\0"
    "Zs,Space_Separator"
    "\0"
    "Zl,Line_Separator"
    "\0"
    "Zp,Paragraph_Separator"
    "\0"
    "Cc,Control,cntrl"
    "\0"
    "Cf,Format"
    "\0"
    "Cs,Surrogate"
    "\0"
    "Co,Private_Use"
    "\0"
    "LC,Cased_Letter"
    "\0"
    "L,Letter"
    "\0"
    "M,Mark,Combining_Mark"
    "\0"
    "N,Number"
    "\0"
    "S,Symbol"
    "\0"
    "P,Punctuation,punct"
    "\0"
    "Z,Separator"
    "\0"
    "C,Other"
    "\0";

static const uint8_t unicode_gc_table[3719] = {
    0xfa, 0x18, 0x17, 0x56, 0x0d, 0x56, 0x12, 0x13, 0x16, 0x0c, 0x16, 0x11,
    0x36, 0xe9, 0x02, 0x36, 0x4c, 0x36, 0xe1, 0x12, 0x12, 0x16, 0x13, 0x0e,
    0x10, 0x0e, 0xe2, 0x12, 0x12, 0x0c, 0x13, 0x0c, 0xfa, 0x19, 0x17, 0x16,
    0x6d, 0x0f, 0x16, 0x0e, 0x0f, 0x05, 0x14, 0x0c, 0x1b, 0x0f, 0x0e, 0x0f,
    0x0c, 0x2b, 0x0e, 0x02, 0x36, 0x0e, 0x0b, 0x05, 0x15, 0x4b, 0x16, 0xe1,
    0x0f, 0x0c, 0xc1, 0xe2, 0x10, 0x0c, 0xe2, 0x00, 0xff, 0x30, 0x02, 0xff,
    0x08, 0x02, 0xff, 0x27, 0xbf, 0x22, 0x21, 0x02, 0x5f, 0x5f, 0x21, 0x22,
    0x61, 0x02, 0x21, 0x02, 0x41, 0x42, 0x21, 0x02, 0x21, 0x02, 0x9f, 0x7f,
    0x02, 0x5f, 0x5f, 0x21, 0x02, 0x5f, 0x3f, 0x02, 0x05, 0x3f, 0x22, 0x65,
    0x01, 0x03, 0x02, 0x01, 0x03, 0x02, 0x01, 0x03, 0x02, 0xff, 0x08, 0x02,
    0xff, 0x0a, 0x02, 0x01, 0x03, 0x02, 0x5f, 0x21, 0x02, 0xff, 0x32, 0xa2,
    0x21, 0x02, 0x21, 0x22, 0x5f, 0x41, 0x02, 0xff, 0x00, 0xe2, 0x3c, 0x05,
    0xe2, 0x13, 0xe4, 0x0a, 0x6e, 0xe4, 0x04, 0xee, 0x06, 0x84, 0xce, 0x04,
    0x0e, 0x04, 0xee, 0x09, 0xe6, 0x68, 0x7f, 0x04, 0x0e, 0x3f, 0x20, 0x04,
    0x42, 0x16, 0x01, 0x60, 0x2e, 0x01, 0x16, 0x41, 0x00, 0x01, 0x00, 0x21,
    0x02, 0xe1, 0x09, 0x00, 0xe1, 0x01, 0xe2, 0x1b, 0x3f, 0x02, 0x41, 0x42,
    0xff, 0x10, 0x62, 0x3f, 0x0c, 0x5f, 0x3f, 0x02, 0xe1, 0x2b, 0xe2, 0x28,
    0xff, 0x1a, 0x0f, 0x86, 0x28, 0xff, 0x2f, 0xff, 0x06, 0x02, 0xff, 0x58,
    0x00, 0xe1, 0x1e, 0x20, 0x04, 0xb6, 0xe2, 0x21, 0x16, 0x11, 0x20, 0x2f,
    0x0d, 0x00, 0xe6, 0x25, 0x11, 0x06, 0x16, 0x26, 0x16, 0x26, 0x16, 0x06,
    0xe0, 0x00, 0xe5, 0x13, 0x60, 0x65, 0x36, 0xe0, 0x03, 0xbb, 0x4c, 0x36,
    0x0d, 0x36, 0x2f, 0xe6, 0x03, 0x16, 0x1b, 0x00, 0x36, 0xe5, 0x18, 0x04,
    0xe5, 0x02, 0xe6, 0x0d, 0xe9, 0x02, 0x76, 0x25, 0x06, 0xe5, 0x5b, 0x16,
    0x05, 0xc6, 0x1b, 0x0f, 0xa6, 0x24, 0x26, 0x0f, 0x66, 0x25, 0xe9, 0x02,
    0x45, 0x2f, 0x05, 0xf6, 0x06, 0x00, 0x1b, 0x05, 0x06, 0xe5, 0x16, 0xe6,
    0x13, 0x20, 0xe5, 0x51, 0xe6, 0x03, 0x05, 0xe0, 0x06, 0xe9, 0x02, 0xe5,
    0x19, 0xe6, 0x01, 0x24, 0x0f, 0x56, 0x04, 0x20, 0x06, 0x2d, 0xe5, 0x0e,
    0x66, 0x04, 0xe6, 0x01, 0x04, 0x46, 0x04, 0x86, 0x20, 0xf6, 0x07, 0x00,
    0xe5, 0x11, 0x46, 0x20, 0x16, 0x00, 0xe5, 0x03, 0xe0, 0x2d, 0xe5, 0x0d,
    0x00, 0xe5, 0x00, 0xe0, 0x0d, 0xe6, 0x07, 0x1b, 0xe6, 0x18, 0x07, 0xe5,
    0x2e, 0x06, 0x07, 0x06, 0x05, 0x47, 0xe6, 0x00, 0x67, 0x06, 0x27, 0x05,
    0xc6, 0xe5, 0x02, 0x26, 0x36, 0xe9, 0x02, 0x16, 0x04, 0xe5, 0x07, 0x06,
    0x27, 0x00, 0xe5, 0x00, 0x20, 0x25, 0x20, 0xe5, 0x0e, 0x00, 0xc5, 0x00,
    0x05, 0x40, 0x65, 0x20, 0x06, 0x05, 0x47, 0x66, 0x20, 0x27, 0x20, 0x27,
    0x06, 0x05, 0xe0, 0x00, 0x07, 0x60, 0x25, 0x00, 0x45, 0x26, 0x20, 0xe9,
    0x02, 0x25, 0x2d, 0xab, 0x0f, 0x0d, 0x05, 0x16, 0x06, 0x20, 0x26, 0x07,
    0x00, 0xa5, 0x60, 0x25, 0x20, 0xe5, 0x0e, 0x00, 0xc5, 0x00, 0x25, 0x00,
    0x25, 0x00, 0x25, 0x20, 0x06, 0x00, 0x47, 0x26, 0x60, 0x26, 0x20, 0x46,
    0x40, 0x06, 0xc0, 0x65, 0x00, 0x05, 0xc0, 0xe9, 0x02, 0x26, 0x45, 0x06,
    0x16, 0xe0, 0x02, 0x26, 0x07, 0x00, 0xe5, 0x01, 0x00, 0x45, 0x00, 0xe5,
    0x0e, 0x00, 0xc5, 0x00, 0x25, 0x00, 0x85, 0x20, 0x06, 0x05, 0x47, 0x86,
    0x00, 0x26, 0x07, 0x00, 0x27, 0x06, 0x20, 0x05, 0xe0, 0x07, 0x25, 0x26,
    0x20, 0xe9, 0x02, 0x16, 0x0d, 0xc0, 0x05, 0xa6, 0x00, 0x06, 0x27, 0x00,
    0xe5, 0x00, 0x20, 0x25, 0x20, 0xe5, 0x0e, 0x00, 0xc5, 0x00, 0x25, 0x00,
    0x85, 0x20, 0x06, 0x05, 0x07, 0x06, 0x07, 0x66, 0x20, 0x27, 0x20, 0x27,
    0x06, 0xe0, 0x00, 0x06, 0x07, 0x60, 0x25, 0x00, 0x45, 0x26, 0x20, 0xe9,
    0x02, 0x0f, 0x05, 0xab, 0xe0, 0x02, 0x06, 0x05, 0x00, 0xa5, 0x40, 0x45,
    0x00, 0x65, 0x40, 0x25, 0x00, 0x05, 0x00, 0x25, 0x40, 0x25, 0x40, 0x45,
    0x40, 0xe5, 0x04, 0x60, 0x27, 0x06, 0x27, 0x40, 0x47, 0x00, 0x47, 0x06,
    0x20, 0x05, 0xa0, 0x07, 0xe0, 0x06, 0xe9, 0x02, 0x4b, 0xaf, 0x0d, 0x0f,
    0x80, 0x06, 0x47, 0x06, 0xe5, 0x00, 0x00, 0x45, 0x00, 0xe5, 0x0f, 0x00,
    0xe5, 0x08, 0x40, 0x05, 0x46, 0x67, 0x00, 0x46, 0x00, 0x66, 0xc0, 0x26,
    0x00, 0x45, 0x80, 0x25, 0x26, 0x20, 0xe9, 0x02, 0xc0, 0x16, 0xcb, 0x0f,
    0x05, 0x06, 0x27, 0x16, 0xe5, 0x00, 0x00, 0x45, 0x00, 0xe5, 0x0f, 0x00,
    0xe5, 0x02, 0x00, 0x85, 0x20, 0x06, 0x05, 0x07, 0x06, 0x87, 0x00, 0x06,
    0x27, 0x00, 0x27, 0x26, 0xc0, 0x27, 0xc0, 0x05, 0x00, 0x25, 0x26, 0x20,
    0xe9, 0x02, 0x00, 0x25, 0xe0, 0x05, 0x26, 0x27, 0x00, 0xe5, 0x00, 0x00,
    0x45, 0x00, 0xe5, 0x21, 0x26, 0x05, 0x47, 0x66, 0x00, 0x47, 0x00, 0x47,
    0x06, 0x05, 0x0f, 0x60, 0x45, 0x07, 0xcb, 0x45, 0x26, 0x20, 0xe9, 0x02,
    0xeb, 0x01, 0x0f, 0xa5, 0x20, 0x27, 0x00, 0xe5, 0x0a, 0x40, 0xe5, 0x10,
    0x00, 0xe5, 0x01, 0x00, 0x05, 0x20, 0xc5, 0x40, 0x06, 0x60, 0x47, 0x46,
    0x00, 0x06, 0x00, 0xe7, 0x00, 0xa0, 0xe9, 0x02, 0x20, 0x27, 0x16, 0xe0,
    0x04, 0xe5, 0x28, 0x06, 0x25, 0xc6, 0x60, 0x0d, 0xa5, 0x04, 0xe6, 0x00,
    0x16, 0xe9, 0x02, 0x36, 0xe0, 0x1d, 0x25, 0x00, 0x05, 0x00, 0x85, 0x00,
    0xe5, 0x10, 0x00, 0x05, 0x00, 0xe5, 0x02, 0x06, 0x25, 0xe6, 0x01, 0x05,
    0x20, 0x85, 0x00, 0x04, 0x00, 0xa6, 0x20, 0xe9, 0x02, 0x20, 0x65, 0xe0,
    0x18, 0x05, 0x4f, 0xf6, 0x07, 0x0f, 0x16, 0x4f, 0x26, 0xaf, 0xe9, 0x02,
    0xeb, 0x02, 0x0f, 0x06, 0x0f, 0x06, 0x0f, 0x06, 0x12, 0x13, 0x12, 0x13,
    0x27, 0xe5, 0x00, 0x00, 0xe5, 0x1c, 0x60, 0xe6, 0x06, 0x07, 0x86, 0x16,
    0x26, 0x85, 0xe6, 0x03, 0x00, 0xe6, 0x1c, 0x00, 0xef, 0x00, 0x06, 0xaf,
    0x00, 0x2f, 0x96, 0x6f, 0x36, 0xe0, 0x1d, 0xe5, 0x23, 0x27, 0x66, 0x07,
    0xa6, 0x07, 0x26, 0x27, 0x26, 0x05, 0xe9, 0x02, 0xb6, 0xa5, 0x27, 0x26,
    0x65, 0x46, 0x05, 0x47, 0x25, 0xc7, 0x45, 0x66, 0xe5, 0x05, 0x06, 0x27,
    0x26, 0xa7, 0x06, 0x05, 0x07, 0xe9, 0x02, 0x47, 0x06, 0x2f, 0xe1, 0x1e,
    0x00, 0x01, 0x80, 0x01, 0x20, 0xe2, 0x23, 0x16, 0x04, 0x42, 0xe5, 0x80,
    0xc1, 0x00, 0x65, 0x20, 0xc5, 0x00, 0x05, 0x00, 0x65, 0x20, 0xe5, 0x21,
    0x00, 0x65, 0x20, 0xe5, 0x19, 0x00, 0x65, 0x20, 0xc5, 0x00, 0x05, 0x00,
    0x65, 0x20, 0xe5, 0x07, 0x00, 0xe5, 0x31, 0x00, 0x65, 0x20, 0xe5, 0x3b,
    0x20, 0x46, 0xf6, 0x01, 0xeb, 0x0c, 0x40, 0xe5, 0x08, 0xef, 0x02, 0xa0,
    0xe1, 0x4e, 0x20, 0xa2, 0x20, 0x11, 0xe5, 0x81, 0xe4, 0x0f, 0x16, 0xe5,
    0x09, 0x17, 0xe5, 0x12, 0x12, 0x13, 0x40, 0xe5, 0x43, 0x56, 0x4a, 0xe5,
    0x00, 0xc0, 0xe5, 0x05, 0x00, 0x65, 0x46, 0xe0, 0x03, 0xe5, 0x0a, 0x46,
    0x36, 0xe0, 0x01, 0xe5, 0x0a, 0x26, 0xe0, 0x04, 0xe5, 0x05, 0x00, 0x45,
    0x00, 0x26, 0xe0, 0x04, 0xe5, 0x2c, 0x26, 0x07, 0xc6, 0xe7, 0x00, 0x06,
    0x27, 0xe6, 0x03, 0x56, 0x04, 0x56, 0x0d, 0x05, 0x06, 0x20, 0xe9, 0x02,
    0xa0, 0xeb, 0x02, 0xa0, 0xb6, 0x11, 0x76, 0x46, 0x1b, 0x00, 0xe9, 0x02,
    0xa0, 0xe5, 0x1b, 0x04, 0xe5, 0x2d, 0xc0, 0x85, 0x26, 0xe5, 0x1a, 0x06,
    0x05, 0x80, 0xe5, 0x3e, 0xe0, 0x02, 0xe5, 0x17, 0x00, 0x46, 0x67, 0x26,
    0x47, 0x60, 0x27, 0x06, 0xa7, 0x46, 0x60, 0x0f, 0x40, 0x36, 0xe9, 0x02,
    0xe5, 0x16, 0x20, 0x85, 0xe0, 0x03, 0xe5, 0x24, 0x60, 0xe5, 0x12, 0xa0,
    0xe9, 0x02, 0x0b, 0x40, 0xef, 0x1a, 0xe5, 0x0f, 0x26, 0x27, 0x06, 0x20,
    0x36, 0xe5, 0x2d, 0x07, 0x06, 0x07, 0xc6, 0x00, 0x06, 0x07, 0x06, 0x27,
    0xe6, 0x00, 0xa7, 0xe6, 0x02, 0x20, 0x06, 0xe9, 0x02, 0xa0, 0xe9, 0x02,
    0xa0, 0xd6, 0x04, 0xb6, 0x20, 0xe6, 0x06, 0x08, 0xe0, 0x39, 0x66, 0x07,
    0xe5, 0x27, 0x06, 0x07, 0x86, 0x07, 0x06, 0x87, 0x06, 0x27, 0xc5, 0x60,
    0xe9, 0x02, 0xd6, 0xef, 0x02, 0xe6, 0x01, 0xef, 0x01, 0x40, 0x26, 0x07,
    0xe5, 0x16, 0x07, 0x66, 0x27, 0x26, 0x07, 0x46, 0x25, 0xe9, 0x02, 0xe5,
    0x24, 0x06, 0x07, 0x26, 0x47, 0x06, 0x07, 0x46, 0x27, 0xe0, 0x00, 0x76,
    0xe5, 0x1c, 0xe7, 0x00, 0xe6, 0x00, 0x27, 0x26, 0x40, 0x96, 0xe9, 0x02,
    0x40, 0x45, 0xe9, 0x02, 0xe5, 0x16, 0xa4, 0x36, 0xe2, 0x01, 0xc0, 0xe1,
    0x23, 0x20, 0x41, 0xf6, 0x00, 0xe0, 0x00, 0x46, 0x16, 0xe6, 0x05, 0x07,
    0xc6, 0x65, 0x06, 0xa5, 0x06, 0x25, 0x07, 0x26, 0x05, 0x80, 0xe2, 0x24,
    0xe4, 0x37, 0xe2, 0x05, 0x04, 0xe2, 0x1a, 0xe4, 0x1d, 0xe6, 0x32, 0x00,
    0x86, 0xff, 0x80, 0x0e, 0xe2, 0x00, 0xff, 0x5a, 0xe2, 0x00, 0xe1, 0x00,
    0xa2, 0x20, 0xa1, 0x20, 0xe2, 0x00, 0xe1, 0x00, 0xe2, 0x00, 0xe1, 0x00,
    0xa2, 0x20, 0xa1, 0x20, 0xe2, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x01,
    0x00, 0x3f, 0xc2, 0xe1, 0x00, 0xe2, 0x06, 0x20, 0xe2, 0x00, 0xe3, 0x00,
    0xe2, 0x00, 0xe3, 0x00, 0xe2, 0x00, 0xe3, 0x00, 0x82, 0x00, 0x22, 0x61,
    0x03, 0x0e, 0x02, 0x4e, 0x42, 0x00, 0x22, 0x61, 0x03, 0x4e, 0x62, 0x20,
    0x22, 0x61, 0x00, 0x4e, 0xe2, 0x00, 0x81, 0x4e, 0x20, 0x42, 0x00, 0x22,
    0x61, 0x03, 0x2e, 0x00, 0xf7, 0x03, 0x9b, 0xb1, 0x36, 0x14, 0x15, 0x12,
    0x34, 0x15, 0x12, 0x14, 0xf6, 0x00, 0x18, 0x19, 0x9b, 0x17, 0xf6, 0x01,
    0x14, 0x15, 0x76, 0x30, 0x56, 0x0c, 0x12, 0x13, 0xf6, 0x03, 0x0c, 0x16,
    0x10, 0xf6, 0x02, 0x17, 0x9b, 0x00, 0xfb, 0x02, 0x0b, 0x04, 0x20, 0xab,
    0x4c, 0x12, 0x13, 0x04, 0xeb, 0x02, 0x4c, 0x12, 0x13, 0x00, 0xe4, 0x05,
    0x40, 0xed, 0x18, 0xe0, 0x08, 0xe6, 0x05, 0x68, 0x06, 0x48, 0xe6, 0x04,
    0xe0, 0x07, 0x2f, 0x01, 0x6f, 0x01, 0x2f, 0x02, 0x41, 0x22, 0x41, 0x02,
    0x0f, 0x01, 0x2f, 0x0c, 0x81, 0xaf, 0x01, 0x0f, 0x01, 0x0f, 0x01, 0x0f,
    0x61, 0x0f, 0x02, 0x61, 0x02, 0x65, 0x02, 0x2f, 0x22, 0x21, 0x8c, 0x3f,
    0x42, 0x0f, 0x0c, 0x2f, 0x02, 0x0f, 0xeb, 0x08, 0xea, 0x1b, 0x3f, 0x6a,
    0x0b, 0x2f, 0x60, 0x8c, 0x8f, 0x2c, 0x6f, 0x0c, 0x2f, 0x0c, 0x2f, 0x0c,
    0xcf, 0x0c, 0xef, 0x17, 0x2c, 0x2f, 0x0c, 0x0f, 0x0c, 0xef, 0x17, 0xec,
    0x80, 0x84, 0xef, 0x00, 0x12, 0x13, 0x12, 0x13, 0xef, 0x0c, 0x2c, 0xcf,
    0x12, 0x13, 0xef, 0x49, 0x0c, 0xef, 0x16, 0xec, 0x11, 0xef, 0x20, 0xac,
    0xef, 0x3d, 0xe0, 0x11, 0xef, 0x03, 0xe0, 0x0d, 0xeb, 0x34, 0xef, 0x46,
    0xeb, 0x0e, 0xef, 0x80, 0x2f, 0x0c, 0xef, 0x01, 0x0c, 0xef, 0x2e, 0xec,
    0x00, 0xef, 0x67, 0x0c, 0xef, 0x80, 0x70, 0x12, 0x13, 0x12, 0x13, 0x12,
    0x13, 0x12, 0x13, 0x12, 0x13, 0x12, 0x13, 0x12, 0x13, 0xeb, 0x16, 0xef,
    0x24, 0x8c, 0x12, 0x13, 0xec, 0x17, 0x12, 0x13, 0x12, 0x13, 0x12, 0x13,
    0x12, 0x13, 0x12, 0x13, 0xec, 0x08, 0xef, 0x80, 0x78, 0xec, 0x7b, 0x12,
    0x13, 0x12, 0x13, 0x12, 0x13, 0x12, 0x13, 0x12, 0x13, 0x12, 0x13, 0x12,
    0x13, 0x12, 0x13, 0x12, 0x13, 0x12, 0x13, 0x12, 0x13, 0xec, 0x37, 0x12,
    0x13, 0x12, 0x13, 0xec, 0x18, 0x12, 0x13, 0xec, 0x80, 0x7a, 0xef, 0x28,
    0xec, 0x0d, 0x2f, 0xac, 0xef, 0x1f, 0x20, 0xef, 0x18, 0x20, 0xef, 0x60,
    0xe1, 0x27, 0x00, 0xe2, 0x27, 0x00, 0x5f, 0x21, 0x22, 0xdf, 0x41, 0x02,
    0x3f, 0x02, 0x3f, 0x82, 0x24, 0x41, 0x02, 0xff, 0x5a, 0x02, 0xaf, 0x7f,
    0x46, 0x3f, 0x80, 0x76, 0x0b, 0x36, 0xe2, 0x1e, 0x00, 0x02, 0x80, 0x02,
    0x20, 0xe5, 0x30, 0xc0, 0x04, 0x16, 0xe0, 0x06, 0x06, 0xe5, 0x0f, 0xe0,
    0x01, 0xc5, 0x00, 0xc5, 0x00, 0xc5, 0x00, 0xc5, 0x00, 0xc5, 0x00, 0xc5,
    0x00, 0xc5, 0x00, 0xc5, 0x00, 0xe6, 0x18, 0x36, 0x14, 0x15, 0x14, 0x15,
    0x56, 0x14, 0x15, 0x16, 0x14, 0x15, 0xf6, 0x01, 0x11, 0x36, 0x11, 0x16,
    0x14, 0x15, 0x36, 0x14, 0x15, 0x12, 0x13, 0x12, 0x13, 0x12, 0x13, 0x12,
    0x13, 0x96, 0x04, 0xf6, 0x02, 0x31, 0x76, 0x11, 0x16, 0x12, 0xf6, 0x05,
    0xe0, 0x28, 0xef, 0x12, 0x00, 0xef, 0x51, 0xe0, 0x04, 0xef, 0x80, 0x4e,
    0xe0, 0x12, 0xef, 0x04, 0x60, 0x17, 0x56, 0x0f, 0x04, 0x05, 0x0a, 0x12,
    0x13, 0x12, 0x13, 0x12, 0x13, 0x12, 0x13, 0x12, 0x13, 0x2f, 0x12, 0x13,
    0x12, 0x13, 0x12, 0x13, 0x12, 0x13, 0x11, 0x12, 0x33, 0x0f, 0xea, 0x01,
    0x66, 0x27, 0x11, 0x84, 0x2f, 0x4a, 0x04, 0x05, 0x16, 0x2f, 0x00, 0xe5,
    0x4e, 0x20, 0x26, 0x2e, 0x24, 0x05, 0x11, 0xe5, 0x52, 0x16, 0x44, 0x05,
    0x80, 0xe5, 0x23, 0x00, 0xe5, 0x56, 0x00, 0x2f, 0x6b, 0xef, 0x02, 0xe5,
    0x13, 0x80, 0xef, 0x1c, 0xe0, 0x04, 0xe5, 0x08, 0xef, 0x17, 0x00, 0xeb,
    0x02, 0xef, 0x16, 0xeb, 0x00, 0x0f, 0xeb, 0x07, 0xef, 0x18, 0xeb, 0x02,
    0xef, 0x1f, 0xeb, 0x07, 0xef, 0x80, 0xb8, 0xe5, 0x99, 0x2e, 0xe0, 0x02,
    0xef, 0x38, 0xe5, 0xc0, 0x11, 0x68, 0xe0, 0x08, 0xe5, 0x0d, 0x04, 0xe5,
    0x83, 0xef, 0x40, 0xef, 0x2f, 0xe0, 0x01, 0xe5, 0x20, 0xa4, 0x36, 0xe5,
    0x80, 0x84, 0x04, 0x56, 0xe5, 0x08, 0xe9, 0x02, 0x25, 0xe0, 0x0c, 0xff,
    0x26, 0x05, 0x06, 0x48, 0x16, 0xe6, 0x02, 0x16, 0x04, 0xff, 0x14, 0x24,
    0x26, 0xe5, 0x3e, 0xea, 0x02, 0x26, 0xb6, 0xe0, 0x00, 0xee, 0x0f, 0xe4,
    0x01, 0x2e, 0xff, 0x06, 0x22, 0xff, 0x36, 0x04, 0xe2, 0x00, 0x9f, 0xff,
    0x02, 0x04, 0x2e, 0x7f, 0x05, 0x7f, 0x22, 0xff, 0x0d, 0x61, 0x02, 0x81,
    0x02, 0xff, 0x02, 0x20, 0x5f, 0x21, 0xe0, 0x28, 0x05, 0x24, 0x02, 0xc5,
    0x06, 0x45, 0x06, 0x65, 0x06, 0xe5, 0x0f, 0x27, 0x26, 0x07, 0x6f, 0x60,
    0xab, 0x2f, 0x0d, 0x0f, 0xa0, 0xe5, 0x2c, 0x76, 0xe0, 0x00, 0x27, 0xe5,
    0x2a, 0xe7, 0x08, 0x26, 0xe0, 0x00, 0x36, 0xe9, 0x02, 0xa0, 0xe6, 0x0a,
    0xa5, 0x56, 0x05, 0x16, 0x25, 0x06, 0xe9, 0x02, 0xe5, 0x14, 0xe6, 0x00,
    0x36, 0xe5, 0x0f, 0xe6, 0x03, 0x27, 0xe0, 0x03, 0x16, 0xe5, 0x15, 0x40,
    0x46, 0x07, 0xe5, 0x27, 0x06, 0x27, 0x66, 0x27, 0x26, 0x47, 0xf6, 0x05,
    0x00, 0x04, 0xe9, 0x02, 0x60, 0x36, 0x85, 0x06, 0x04, 0xe5, 0x01, 0xe9,
    0x02, 0x85, 0x00, 0xe5, 0x21, 0xa6, 0x27, 0x26, 0x27, 0x26, 0xe0, 0x01,
    0x45, 0x06, 0xe5, 0x00, 0x06, 0x07, 0x20, 0xe9, 0x02, 0x20, 0x76, 0xe5,
    0x08, 0x04, 0xa5, 0x4f, 0x05, 0x07, 0x06, 0x07, 0xe5, 0x2a, 0x06, 0x05,
    0x46, 0x25, 0x26, 0x85, 0x26, 0x05, 0x06, 0x05, 0xe0, 0x10, 0x25, 0x04,
    0x36, 0xe5, 0x03, 0x07, 0x26, 0x27, 0x36, 0x05, 0x24, 0x07, 0x06, 0xe0,
    0x02, 0xa5, 0x20, 0xa5, 0x20, 0xa5, 0xe0, 0x01, 0xc5, 0x00, 0xc5, 0x00,
    0xe2, 0x23, 0x0e, 0x64, 0xe2, 0x00, 0xe0, 0x00, 0xe2, 0x48, 0xe5, 0x1b,
    0x27, 0x06, 0x27, 0x06, 0x27, 0x16, 0x07, 0x06, 0x20, 0xe9, 0x02, 0xa0,
    0xe5, 0xab, 0x1c, 0xe0, 0x04, 0xe5, 0x0f, 0x60, 0xe5, 0x29, 0x60, 0xfc,
    0x87, 0x78, 0xfd, 0x98, 0x78, 0xe5, 0x80, 0xe6, 0x20, 0xe5, 0x62, 0xe0,
    0x1e, 0xc2, 0xe0, 0x04, 0x82, 0x80, 0x05, 0x06, 0xe5, 0x02, 0x0c, 0xe5,
    0x05, 0x00, 0x85, 0x00, 0x05, 0x00, 0x25, 0x00, 0x25, 0x00, 0xe5, 0x64,
    0xee, 0x08, 0xe0, 0x09, 0xe5, 0x80, 0xe3, 0x13, 0x12, 0xe0, 0x08, 0xe5,
    0x38, 0x20, 0xe5, 0x2e, 0xe0, 0x20, 0xe5, 0x04, 0x0d, 0x0f, 0x20, 0xe6,
    0x08, 0xd6, 0x12, 0x13, 0x16, 0xa0, 0xe6, 0x08, 0x16, 0x31, 0x30, 0x12,
    0x13, 0x12, 0x13, 0x12, 0x13, 0x12, 0x13, 0x12, 0x13, 0x12, 0x13, 0x12,
    0x13, 0x12, 0x13, 0x36, 0x12, 0x13, 0x76, 0x50, 0x56, 0x00, 0x76, 0x11,
    0x12, 0x13, 0x12, 0x13, 0x12, 0x13, 0x56, 0x0c, 0x11, 0x4c, 0x00, 0x16,
    0x0d, 0x36, 0x60, 0x85, 0x00, 0xe5, 0x7f, 0x20, 0x1b, 0x00, 0x56, 0x0d,
    0x56, 0x12, 0x13, 0x16, 0x0c, 0x16, 0x11, 0x36, 0xe9, 0x02, 0x36, 0x4c,
    0x36, 0xe1, 0x12, 0x12, 0x16, 0x13, 0x0e, 0x10, 0x0e, 0xe2, 0x12, 0x12,
    0x0c, 0x13, 0x0c, 0x12, 0x13, 0x16, 0x12, 0x13, 0x36, 0xe5, 0x02, 0x04,
    0xe5, 0x25, 0x24, 0xe5, 0x17, 0x40, 0xa5, 0x20, 0xa5, 0x20, 0xa5, 0x20,
    0x45, 0x40, 0x2d, 0x0c, 0x0e, 0x0f, 0x2d, 0x00, 0x0f, 0x6c, 0x2f, 0xe0,
    0x02, 0x5b, 0x2f, 0x20, 0xe5, 0x04, 0x00, 0xe5, 0x12, 0x00, 0xe5, 0x0b,
    0x00, 0x25, 0x00, 0xe5, 0x07, 0x20, 0xe5, 0x06, 0xe0, 0x1a, 0xe5, 0x73,
    0x80, 0x56, 0x60, 0xeb, 0x25, 0x40, 0xef, 0x01, 0xea, 0x2d, 0x6b, 0xef,
    0x09, 0x2b, 0x4f, 0x00, 0xef, 0x04, 0x60, 0x0f, 0xe0, 0x27, 0xef, 0x25,
    0x06, 0xe0, 0x7a, 0xe5, 0x15, 0x40, 0xe5, 0x29, 0xe0, 0x07, 0x06, 0xeb,
    0x13, 0x60, 0xe5, 0x18, 0x6b, 0xe0, 0x01, 0xe5, 0x0c, 0x0a, 0xe5, 0x00,
    0x0a, 0x80, 0xe5, 0x1e, 0x86, 0x80, 0xe5, 0x16, 0x00, 0x16, 0xe5, 0x1c,
    0x60, 0xe5, 0x00, 0x16, 0x8a, 0xe0, 0x22, 0xe1, 0x20, 0xe2, 0x20, 0xe5,
    0x46, 0x20, 0xe9, 0x02, 0xa0, 0xe1, 0x1c, 0x60, 0xe2, 0x1c, 0x60, 0xe5,
    0x20, 0xe0, 0x00, 0xe5, 0x2c, 0xe0, 0x03, 0x16, 0xe0, 0x80, 0x08, 0xe5,
    0x80, 0xaf, 0xe0, 0x01, 0xe5, 0x0e, 0xe0, 0x02, 0xe5, 0x00, 0xe0, 0x80,
    0x10, 0xa5, 0x20, 0x05, 0x00, 0xe5, 0x24, 0x00, 0x25, 0x40, 0x05, 0x20,
    0xe5, 0x0f, 0x00, 0x16, 0xeb, 0x00, 0xe5, 0x0f, 0x2f, 0xcb, 0xe5, 0x17,
    0xe0, 0x00, 0xeb, 0x01, 0xe0, 0x28, 0xe5, 0x0b, 0x00, 0x25, 0x80, 0x8b,
    0xe5, 0x0e, 0xab, 0x40, 0x16, 0xe5, 0x12, 0x80, 0x16, 0xe0, 0x38, 0xe5,
    0x30, 0x60, 0x2b, 0x25, 0xeb, 0x08, 0x20, 0xeb, 0x26, 0x05, 0x46, 0x00,
    0x26, 0x80, 0x66, 0x65, 0x00, 0x45, 0x00, 0xe5, 0x15, 0x20, 0x46, 0x60,
    0x06, 0xeb, 0x01, 0xc0, 0xf6, 0x01, 0xc0, 0xe5, 0x15, 0x2b, 0x16, 0xe5,
    0x15, 0x4b, 0xe0, 0x18, 0xe5, 0x00, 0x0f, 0xe5, 0x14, 0x26, 0x60, 0x8b,
    0xd6, 0xe0, 0x01, 0xe5, 0x2e, 0x40, 0xd6, 0xe5, 0x0e, 0x20, 0xeb, 0x00,
    0xe5, 0x0b, 0x80, 0xeb, 0x00, 0xe5, 0x0a, 0xc0, 0x76, 0xe0, 0x04, 0xcb,
    0xe0, 0x48, 0xe5, 0x41, 0xe0, 0x2f, 0xe1, 0x2b, 0xe0, 0x05, 0xe2, 0x2b,
    0xc0, 0xab, 0xe5, 0x1c, 0x66, 0xe0, 0x00, 0xe9, 0x02, 0xe0, 0x80, 0x9e,
    0xeb, 0x17, 0xe0, 0x79, 0xe5, 0x15, 0xeb, 0x02, 0x05, 0xe0, 0x00, 0xe5,
    0x0e, 0xe6, 0x03, 0x6b, 0x96, 0xe0, 0x7e, 0xe5, 0x0f, 0xe0, 0x01, 0x07,
    0x06, 0x07, 0xe5, 0x2d, 0xe6, 0x07, 0xd6, 0x60, 0xeb, 0x0c, 0xe9, 0x02,
    0xe0, 0x07, 0x46, 0x07, 0xe5, 0x25, 0x47, 0x66, 0x27, 0x26, 0x36, 0x1b,
    0x76, 0xe0, 0x03, 0x1b, 0x20, 0xe5, 0x11, 0xc0, 0xe9, 0x02, 0xa0, 0x46,
    0xe5, 0x1c, 0x86, 0x07, 0xe6, 0x00, 0x00, 0xe9, 0x02, 0x76, 0x05, 0x27,
    0xe0, 0x01, 0xe5, 0x1b, 0x06, 0x36, 0x05, 0xe0, 0x01, 0x26, 0x07, 0xe5,
    0x28, 0x47, 0xe6, 0x01, 0x27, 0x65, 0x76, 0x66, 0x16, 0x20, 0xe9, 0x02,
    0x05, 0x16, 0x05, 0x56, 0x00, 0xeb, 0x0c, 0xe0, 0x03, 0xe5, 0x0a, 0x00,
    0xe5, 0x11, 0x47, 0x46, 0x27, 0x06, 0x07, 0x26, 0xb6, 0x06, 0xe0, 0x39,
    0xc5, 0x00, 0x05, 0x00, 0x65, 0x00, 0xe5, 0x07, 0x00, 0xe5, 0x02, 0x16,
    0xa0, 0xe5, 0x27, 0x06, 0x47, 0xe6, 0x00, 0x80, 0xe9, 0x02, 0xa0, 0x26,
    0x27, 0x00, 0xe5, 0x00, 0x20, 0x25, 0x20, 0xe5, 0x0e, 0x00, 0xc5, 0x00,
    0x25, 0x00, 0x85, 0x00, 0x26, 0x05, 0x27, 0x06, 0x67, 0x20, 0x27, 0x20,
    0x47, 0x20, 0x05, 0xa0, 0x07, 0x80, 0x85, 0x27, 0x20, 0xc6, 0x40, 0x86,
    0xe0, 0x80, 0x03, 0xe5, 0x2d, 0x47, 0xe6, 0x00, 0x27, 0x46, 0x07, 0x06,
    0x65, 0x96, 0xe9, 0x02, 0x00, 0x16, 0x00, 0x16, 0x06, 0x05, 0xe0, 0x18,
    0xe5, 0x28, 0x47, 0xa6, 0x07, 0x06, 0x67, 0x26, 0x07, 0x26, 0x25, 0x16,
    0x05, 0xe0, 0x00, 0xe9, 0x02, 0xe0, 0x80, 0x1e, 0xe5, 0x27, 0x47, 0x66,
    0x20, 0x67, 0x26, 0x07, 0x26, 0xf6, 0x0f, 0x65, 0x26, 0xe0, 0x1a, 0xe5,
    0x28, 0x47, 0xe6, 0x00, 0x27, 0x06, 0x07, 0x26, 0x56, 0x05, 0xe0, 0x03,
    0xe9, 0x02, 0xa0, 0xf6, 0x05, 0xe0, 0x0b, 0xe5, 0x23, 0x06, 0x07, 0x06,
    0x27, 0xa6, 0x07, 0x06, 0x05, 0xc0, 0xe9, 0x02, 0xe0, 0x2e, 0xe5, 0x13,
    0x20, 0x46, 0x27, 0x66, 0x07, 0x86, 0x60, 0xe9, 0x02, 0x2b, 0x56, 0x0f,
    0xe0, 0x80, 0x38, 0xe5, 0x24, 0x47, 0xe6, 0x01, 0x07, 0x26, 0x16, 0xe0,
    0x5c, 0xe1, 0x18, 0xe2, 0x18, 0xe9, 0x02, 0xeb, 0x01, 0xe0, 0x04, 0x05,
    0xe0, 0x80, 0x18, 0xe5, 0x00, 0x20, 0xe5, 0x1f, 0x47, 0x66, 0x20, 0x26,
    0x67, 0x06, 0x05, 0x16, 0x05, 0x07, 0xe0, 0x13, 0x05, 0xe6, 0x02, 0xe5,
    0x20, 0xa6, 0x07, 0x05, 0x66, 0xf6, 0x00, 0x06, 0xe0, 0x00, 0x05, 0xa6,
    0x27, 0x46, 0xe5, 0x26, 0xe6, 0x05, 0x07, 0x26, 0x56, 0x05, 0x96, 0xe0,
    0x15, 0xe5, 0x31, 0xe0, 0x80, 0x7f, 0xe5, 0x01, 0x00, 0xe5, 0x1d, 0x07,
    0xc6, 0x00, 0xa6, 0x07, 0x06, 0x05, 0x96, 0xe0, 0x02, 0xe9, 0x02, 0xeb,
    0x0b, 0x40, 0x36, 0xe5, 0x16, 0x20, 0xe6, 0x0e, 0x00, 0x07, 0xc6, 0x07,
    0x26, 0x07, 0x26, 0xe0, 0x41, 0xc5, 0x00, 0x25, 0x00, 0xe5, 0x1e, 0xa6,
    0x40, 0x06, 0x00, 0x26, 0x00, 0xc6, 0x05, 0x06, 0xe0, 0x00, 0xe9, 0x02,
    0xa0, 0xa5, 0x00, 0x25, 0x00, 0xe5, 0x18, 0x87, 0x00, 0x26, 0x00, 0x27,
    0x06, 0x07, 0x06, 0x05, 0xc0, 0xe9, 0x02, 0xe0, 0x80, 0xae, 0xe5, 0x0b,
    0x26, 0x27, 0x36, 0xe0, 0x80, 0x3f, 0xeb, 0x0d, 0xef, 0x00, 0x6d, 0xef,
    0x09, 0xe0, 0x05, 0x16, 0xe5, 0x83, 0x12, 0xe0, 0x5e, 0xea, 0x67, 0x00,
    0x96, 0xe0, 0x03, 0xe5, 0x80, 0x3c, 0xe0, 0x8a, 0x34, 0xe5, 0x83, 0xa7,
    0x00, 0xfb, 0x01, 0xe0, 0x8f, 0x3f, 0xe5, 0x81, 0xbf, 0xe0, 0xa1, 0x31,
    0xe5, 0x81, 0xb1, 0xc0, 0xe5, 0x17, 0x00, 0xe9, 0x02, 0x60, 0x36, 0xe0,
    0x58, 0xe5, 0x16, 0x20, 0x86, 0x16, 0xe0, 0x02, 0xe5, 0x28, 0xc6, 0x96,
    0x6f, 0x64, 0x16, 0x0f, 0xe0, 0x02, 0xe9, 0x02, 0x00, 0xcb, 0x00, 0xe5,
    0x0d, 0x80, 0xe5, 0x0b, 0xe0, 0x82, 0x28, 0xe1, 0x18, 0xe2, 0x18, 0xeb,
    0x0f, 0x76, 0xe0, 0x5d, 0xe5, 0x43, 0x60, 0x06, 0x05, 0xe7, 0x2f, 0xc0,
    0x66, 0xe4, 0x05, 0xe0, 0x38, 0x24, 0x16, 0x04, 0xe0, 0x14, 0xe5, 0x97,
    0x70, 0xe0, 0x00, 0xe5, 0x82, 0x6b, 0xe0, 0xa4, 0x85, 0xe5, 0x80, 0x97,
    0xe0, 0x29, 0x45, 0xe0, 0x09, 0x65, 0xe0, 0x00, 0xe5, 0x81, 0x04, 0xe0,
    0x88, 0x7c, 0xe5, 0x63, 0x80, 0xe5, 0x05, 0x40, 0xe5, 0x01, 0xc0, 0xe5,
    0x02, 0x20, 0x0f, 0x26, 0x16, 0x7b, 0xe0, 0x92, 0xd4, 0xef, 0x80, 0x6e,
    0xe0, 0x02, 0xef, 0x1f, 0x20, 0xef, 0x34, 0x27, 0x46, 0x4f, 0xa7, 0xfb,
    0x00, 0xe6, 0x00, 0x2f, 0xc6, 0xef, 0x16, 0x66, 0xef, 0x33, 0xe0, 0x0f,
    0xef, 0x3a, 0x46, 0x0f, 0xe0, 0x80, 0x12, 0xeb, 0x0c, 0xe0, 0x04, 0xef,
    0x4f, 0xe0, 0x01, 0xeb, 0x11, 0xe0, 0x7f, 0xe1, 0x12, 0xe2, 0x12, 0xe1,
    0x12, 0xc2, 0x00, 0xe2, 0x0a, 0xe1, 0x12, 0xe2, 0x12, 0x01, 0x00, 0x21,
    0x20, 0x01, 0x20, 0x21, 0x20, 0x61, 0x00, 0xe1, 0x00, 0x62, 0x00, 0x02,
    0x00, 0xc2, 0x00, 0xe2, 0x03, 0xe1, 0x12, 0xe2, 0x12, 0x21, 0x00, 0x61,
    0x20, 0xe1, 0x00, 0x00, 0xc1, 0x00, 0xe2, 0x12, 0x21, 0x00, 0x61, 0x00,
    0x81, 0x00, 0x01, 0x40, 0xc1, 0x00, 0xe2, 0x12, 0xe1, 0x12, 0xe2, 0x12,
    0xe1, 0x12, 0xe2, 0x12, 0xe1, 0x12, 0xe2, 0x12, 0xe1, 0x12, 0xe2, 0x12,
    0xe1, 0x12, 0xe2, 0x12, 0xe1, 0x12, 0xe2, 0x14, 0x20, 0xe1, 0x11, 0x0c,
    0xe2, 0x11, 0x0c, 0xa2, 0xe1, 0x11, 0x0c, 0xe2, 0x11, 0x0c, 0xa2, 0xe1,
    0x11, 0x0c, 0xe2, 0x11, 0x0c, 0xa2, 0xe1, 0x11, 0x0c, 0xe2, 0x11, 0x0c,
    0xa2, 0xe1, 0x11, 0x0c, 0xe2, 0x11, 0x0c, 0xa2, 0x3f, 0x20, 0xe9, 0x2a,
    0xef, 0x81, 0x78, 0xe6, 0x2f, 0x6f, 0xe6, 0x2a, 0xef, 0x00, 0x06, 0xef,
    0x06, 0x06, 0x2f, 0x96, 0xe0, 0x07, 0x86, 0x00, 0xe6, 0x07, 0xe0, 0x84,
    0xc8, 0xc6, 0x00, 0xe6, 0x09, 0x20, 0xc6, 0x00, 0x26, 0x00, 0x86, 0xe0,
    0x80, 0x4d, 0xe5, 0x25, 0x40, 0xc6, 0xc4, 0x20, 0xe9, 0x02, 0x60, 0x05,
    0x0f, 0xe0, 0x80, 0xe8, 0xe5, 0x24, 0x66, 0xe9, 0x02, 0x80, 0x0d, 0xe0,
    0x84, 0x78, 0xe5, 0x80, 0x3d, 0x20, 0xeb, 0x01, 0xc6, 0xe0, 0x21, 0xe1,
    0x1a, 0xe2, 0x1a, 0xc6, 0x04, 0x60, 0xe9, 0x02, 0x60, 0x36, 0xe0, 0x82,
    0x89, 0xeb, 0x33, 0x0f, 0x4b, 0x0d, 0x6b, 0xe0, 0x44, 0xeb, 0x25, 0x0f,
    0xeb, 0x07, 0xe0, 0x80, 0x3a, 0x65, 0x00, 0xe5, 0x13, 0x00, 0x25, 0x00,
    0x05, 0x20, 0x05, 0x00, 0xe5, 0x02, 0x00, 0x65, 0x00, 0x05, 0x00, 0x05,
    0xa0, 0x05, 0x60, 0x05, 0x00, 0x05, 0x00, 0x05, 0x00, 0x45, 0x00, 0x25,
    0x00, 0x05, 0x20, 0x05, 0x00, 0x05, 0x00, 0x05, 0x00, 0x05, 0x00, 0x05,
    0x00, 0x25, 0x00, 0x05, 0x20, 0x65, 0x00, 0xc5, 0x00, 0x65, 0x00, 0x65,
    0x00, 0x05, 0x00, 0xe5, 0x02, 0x00, 0xe5, 0x09, 0x80, 0x45, 0x00, 0x85,
    0x00, 0xe5, 0x09, 0xe0, 0x2c, 0x2c, 0xe0, 0x80, 0x86, 0xef, 0x24, 0x60,
    0xef, 0x5c, 0xe0, 0x04, 0xef, 0x07, 0x20, 0xef, 0x07, 0x00, 0xef, 0x07,
    0x00, 0xef, 0x1d, 0xe0, 0x02, 0xeb, 0x05, 0x40, 0xef, 0x55, 0x40, 0xef,
    0x35, 0xe0, 0x31, 0xef, 0x15, 0xe0, 0x05, 0xef, 0x24, 0x60, 0xef, 0x01,
    0xc0, 0x2f, 0xe0, 0x06, 0xaf, 0xe0, 0x80, 0x12, 0xef, 0x80, 0x73, 0x8e,
    0xef, 0x82, 0x4e, 0xe0, 0x02, 0xef, 0x05, 0x40, 0xef, 0x03, 0x80, 0xef,
    0x6c, 0xe0, 0x04, 0xef, 0x51, 0xc0, 0xef, 0x04, 0xe0, 0x0c, 0xef, 0x04,
    0x60, 0xef, 0x30, 0xe0, 0x00, 0xef, 0x02, 0xa0, 0xef, 0x20, 0xe0, 0x00,
    0xef, 0x16, 0xe0, 0x4a, 0xef, 0x04, 0x00, 0xef, 0x5d, 0x00, 0x6f, 0x40,
    0xef, 0x21, 0x20, 0xaf, 0x40, 0xef, 0x15, 0x20, 0xef, 0x7f, 0xe0, 0x04,
    0xef, 0x06, 0x20, 0x6f, 0x60, 0x4f, 0x80, 0x4f, 0xe0, 0x05, 0xaf, 0xe0,
    0x84, 0xe2, 0xe5, 0xc0, 0x66, 0x4f, 0xe0, 0x21, 0xe5, 0x8f, 0xad, 0xe0,
    0x03, 0xe5, 0x80, 0x56, 0x20, 0xe5, 0x95, 0xfa, 0xe0, 0x06, 0xe5, 0x9c,
    0xa9, 0xe0, 0x8b, 0x97, 0xe5, 0x81, 0x96, 0xe0, 0xca, 0xc5, 0x5b, 0x1b,
    0xe0, 0x16, 0xfb, 0x58, 0xe0, 0x78, 0xe6, 0x80, 0x68, 0xe0, 0xc0, 0xbd,
    0x88, 0xfd, 0xc0, 0xbf, 0x76, 0x20, 0xfd, 0xc0, 0xbf, 0x76, 0x20,
};

typedef enum {
  UNICODE_SCRIPT_Unknown,
  UNICODE_SCRIPT_Adlam,
  UNICODE_SCRIPT_Ahom,
  UNICODE_SCRIPT_Anatolian_Hieroglyphs,
  UNICODE_SCRIPT_Arabic,
  UNICODE_SCRIPT_Armenian,
  UNICODE_SCRIPT_Avestan,
  UNICODE_SCRIPT_Balinese,
  UNICODE_SCRIPT_Bamum,
  UNICODE_SCRIPT_Bassa_Vah,
  UNICODE_SCRIPT_Batak,
  UNICODE_SCRIPT_Bengali,
  UNICODE_SCRIPT_Bhaiksuki,
  UNICODE_SCRIPT_Bopomofo,
  UNICODE_SCRIPT_Brahmi,
  UNICODE_SCRIPT_Braille,
  UNICODE_SCRIPT_Buginese,
  UNICODE_SCRIPT_Buhid,
  UNICODE_SCRIPT_Canadian_Aboriginal,
  UNICODE_SCRIPT_Carian,
  UNICODE_SCRIPT_Caucasian_Albanian,
  UNICODE_SCRIPT_Chakma,
  UNICODE_SCRIPT_Cham,
  UNICODE_SCRIPT_Cherokee,
  UNICODE_SCRIPT_Common,
  UNICODE_SCRIPT_Coptic,
  UNICODE_SCRIPT_Cuneiform,
  UNICODE_SCRIPT_Cypriot,
  UNICODE_SCRIPT_Cyrillic,
  UNICODE_SCRIPT_Deseret,
  UNICODE_SCRIPT_Devanagari,
  UNICODE_SCRIPT_Dogra,
  UNICODE_SCRIPT_Duployan,
  UNICODE_SCRIPT_Egyptian_Hieroglyphs,
  UNICODE_SCRIPT_Elbasan,
  UNICODE_SCRIPT_Elymaic,
  UNICODE_SCRIPT_Ethiopic,
  UNICODE_SCRIPT_Georgian,
  UNICODE_SCRIPT_Glagolitic,
  UNICODE_SCRIPT_Gothic,
  UNICODE_SCRIPT_Grantha,
  UNICODE_SCRIPT_Greek,
  UNICODE_SCRIPT_Gujarati,
  UNICODE_SCRIPT_Gunjala_Gondi,
  UNICODE_SCRIPT_Gurmukhi,
  UNICODE_SCRIPT_Han,
  UNICODE_SCRIPT_Hangul,
  UNICODE_SCRIPT_Hanifi_Rohingya,
  UNICODE_SCRIPT_Hanunoo,
  UNICODE_SCRIPT_Hatran,
  UNICODE_SCRIPT_Hebrew,
  UNICODE_SCRIPT_Hiragana,
  UNICODE_SCRIPT_Imperial_Aramaic,
  UNICODE_SCRIPT_Inherited,
  UNICODE_SCRIPT_Inscriptional_Pahlavi,
  UNICODE_SCRIPT_Inscriptional_Parthian,
  UNICODE_SCRIPT_Javanese,
  UNICODE_SCRIPT_Kaithi,
  UNICODE_SCRIPT_Kannada,
  UNICODE_SCRIPT_Katakana,
  UNICODE_SCRIPT_Kayah_Li,
  UNICODE_SCRIPT_Kharoshthi,
  UNICODE_SCRIPT_Khmer,
  UNICODE_SCRIPT_Khojki,
  UNICODE_SCRIPT_Khudawadi,
  UNICODE_SCRIPT_Lao,
  UNICODE_SCRIPT_Latin,
  UNICODE_SCRIPT_Lepcha,
  UNICODE_SCRIPT_Limbu,
  UNICODE_SCRIPT_Linear_A,
  UNICODE_SCRIPT_Linear_B,
  UNICODE_SCRIPT_Lisu,
  UNICODE_SCRIPT_Lycian,
  UNICODE_SCRIPT_Lydian,
  UNICODE_SCRIPT_Makasar,
  UNICODE_SCRIPT_Mahajani,
  UNICODE_SCRIPT_Malayalam,
  UNICODE_SCRIPT_Mandaic,
  UNICODE_SCRIPT_Manichaean,
  UNICODE_SCRIPT_Marchen,
  UNICODE_SCRIPT_Masaram_Gondi,
  UNICODE_SCRIPT_Medefaidrin,
  UNICODE_SCRIPT_Meetei_Mayek,
  UNICODE_SCRIPT_Mende_Kikakui,
  UNICODE_SCRIPT_Meroitic_Cursive,
  UNICODE_SCRIPT_Meroitic_Hieroglyphs,
  UNICODE_SCRIPT_Miao,
  UNICODE_SCRIPT_Modi,
  UNICODE_SCRIPT_Mongolian,
  UNICODE_SCRIPT_Mro,
  UNICODE_SCRIPT_Multani,
  UNICODE_SCRIPT_Myanmar,
  UNICODE_SCRIPT_Nabataean,
  UNICODE_SCRIPT_Nandinagari,
  UNICODE_SCRIPT_New_Tai_Lue,
  UNICODE_SCRIPT_Newa,
  UNICODE_SCRIPT_Nko,
  UNICODE_SCRIPT_Nushu,
  UNICODE_SCRIPT_Nyiakeng_Puachue_Hmong,
  UNICODE_SCRIPT_Ogham,
  UNICODE_SCRIPT_Ol_Chiki,
  UNICODE_SCRIPT_Old_Hungarian,
  UNICODE_SCRIPT_Old_Italic,
  UNICODE_SCRIPT_Old_North_Arabian,
  UNICODE_SCRIPT_Old_Permic,
  UNICODE_SCRIPT_Old_Persian,
  UNICODE_SCRIPT_Old_Sogdian,
  UNICODE_SCRIPT_Old_South_Arabian,
  UNICODE_SCRIPT_Old_Turkic,
  UNICODE_SCRIPT_Oriya,
  UNICODE_SCRIPT_Osage,
  UNICODE_SCRIPT_Osmanya,
  UNICODE_SCRIPT_Pahawh_Hmong,
  UNICODE_SCRIPT_Palmyrene,
  UNICODE_SCRIPT_Pau_Cin_Hau,
  UNICODE_SCRIPT_Phags_Pa,
  UNICODE_SCRIPT_Phoenician,
  UNICODE_SCRIPT_Psalter_Pahlavi,
  UNICODE_SCRIPT_Rejang,
  UNICODE_SCRIPT_Runic,
  UNICODE_SCRIPT_Samaritan,
  UNICODE_SCRIPT_Saurashtra,
  UNICODE_SCRIPT_Sharada,
  UNICODE_SCRIPT_Shavian,
  UNICODE_SCRIPT_Siddham,
  UNICODE_SCRIPT_SignWriting,
  UNICODE_SCRIPT_Sinhala,
  UNICODE_SCRIPT_Sogdian,
  UNICODE_SCRIPT_Sora_Sompeng,
  UNICODE_SCRIPT_Soyombo,
  UNICODE_SCRIPT_Sundanese,
  UNICODE_SCRIPT_Syloti_Nagri,
  UNICODE_SCRIPT_Syriac,
  UNICODE_SCRIPT_Tagalog,
  UNICODE_SCRIPT_Tagbanwa,
  UNICODE_SCRIPT_Tai_Le,
  UNICODE_SCRIPT_Tai_Tham,
  UNICODE_SCRIPT_Tai_Viet,
  UNICODE_SCRIPT_Takri,
  UNICODE_SCRIPT_Tamil,
  UNICODE_SCRIPT_Tangut,
  UNICODE_SCRIPT_Telugu,
  UNICODE_SCRIPT_Thaana,
  UNICODE_SCRIPT_Thai,
  UNICODE_SCRIPT_Tibetan,
  UNICODE_SCRIPT_Tifinagh,
  UNICODE_SCRIPT_Tirhuta,
  UNICODE_SCRIPT_Ugaritic,
  UNICODE_SCRIPT_Vai,
  UNICODE_SCRIPT_Wancho,
  UNICODE_SCRIPT_Warang_Citi,
  UNICODE_SCRIPT_Yi,
  UNICODE_SCRIPT_Zanabazar_Square,
  UNICODE_SCRIPT_COUNT,
} UnicodeScriptEnum;

static const char unicode_script_name_table[] =
    "Adlam,Adlm"
    "\0"
    "Ahom,Ahom"
    "\0"
    "Anatolian_Hieroglyphs,Hluw"
    "\0"
    "Arabic,Arab"
    "\0"
    "Armenian,Armn"
    "\0"
    "Avestan,Avst"
    "\0"
    "Balinese,Bali"
    "\0"
    "Bamum,Bamu"
    "\0"
    "Bassa_Vah,Bass"
    "\0"
    "Batak,Batk"
    "\0"
    "Bengali,Beng"
    "\0"
    "Bhaiksuki,Bhks"
    "\0"
    "Bopomofo,Bopo"
    "\0"
    "Brahmi,Brah"
    "\0"
    "Braille,Brai"
    "\0"
    "Buginese,Bugi"
    "\0"
    "Buhid,Buhd"
    "\0"
    "Canadian_Aboriginal,Cans"
    "\0"
    "Carian,Cari"
    "\0"
    "Caucasian_Albanian,Aghb"
    "\0"
    "Chakma,Cakm"
    "\0"
    "Cham,Cham"
    "\0"
    "Cherokee,Cher"
    "\0"
    "Common,Zyyy"
    "\0"
    "Coptic,Copt,Qaac"
    "\0"
    "Cuneiform,Xsux"
    "\0"
    "Cypriot,Cprt"
    "\0"
    "Cyrillic,Cyrl"
    "\0"
    "Deseret,Dsrt"
    "\0"
    "Devanagari,Deva"
    "\0"
    "Dogra,Dogr"
    "\0"
    "Duployan,Dupl"
    "\0"
    "Egyptian_Hieroglyphs,Egyp"
    "\0"
    "Elbasan,Elba"
    "\0"
    "Elymaic,Elym"
    "\0"
    "Ethiopic,Ethi"
    "\0"
    "Georgian,Geor"
    "\0"
    "Glagolitic,Glag"
    "\0"
    "Gothic,Goth"
    "\0"
    "Grantha,Gran"
    "\0"
    "Greek,Grek"
    "\0"
    "Gujarati,Gujr"
    "\0"
    "Gunjala_Gondi,Gong"
    "\0"
    "Gurmukhi,Guru"
    "\0"
    "Han,Hani"
    "\0"
    "Hangul,Hang"
    "\0"
    "Hanifi_Rohingya,Rohg"
    "\0"
    "Hanunoo,Hano"
    "\0"
    "Hatran,Hatr"
    "\0"
    "Hebrew,Hebr"
    "\0"
    "Hiragana,Hira"
    "\0"
    "Imperial_Aramaic,Armi"
    "\0"
    "Inherited,Zinh,Qaai"
    "\0"
    "Inscriptional_Pahlavi,Phli"
    "\0"
    "Inscriptional_Parthian,Prti"
    "\0"
    "Javanese,Java"
    "\0"
    "Kaithi,Kthi"
    "\0"
    "Kannada,Knda"
    "\0"
    "Katakana,Kana"
    "\0"
    "Kayah_Li,Kali"
    "\0"
    "Kharoshthi,Khar"
    "\0"
    "Khmer,Khmr"
    "\0"
    "Khojki,Khoj"
    "\0"
    "Khudawadi,Sind"
    "\0"
    "Lao,Laoo"
    "\0"
    "Latin,Latn"
    "\0"
    "Lepcha,Lepc"
    "\0"
    "Limbu,Limb"
    "\0"
    "Linear_A,Lina"
    "\0"
    "Linear_B,Linb"
    "\0"
    "Lisu,Lisu"
    "\0"
    "Lycian,Lyci"
    "\0"
    "Lydian,Lydi"
    "\0"
    "Makasar,Maka"
    "\0"
    "Mahajani,Mahj"
    "\0"
    "Malayalam,Mlym"
    "\0"
    "Mandaic,Mand"
    "\0"
    "Manichaean,Mani"
    "\0"
    "Marchen,Marc"
    "\0"
    "Masaram_Gondi,Gonm"
    "\0"
    "Medefaidrin,Medf"
    "\0"
    "Meetei_Mayek,Mtei"
    "\0"
    "Mende_Kikakui,Mend"
    "\0"
    "Meroitic_Cursive,Merc"
    "\0"
    "Meroitic_Hieroglyphs,Mero"
    "\0"
    "Miao,Plrd"
    "\0"
    "Modi,Modi"
    "\0"
    "Mongolian,Mong"
    "\0"
    "Mro,Mroo"
    "\0"
    "Multani,Mult"
    "\0"
    "Myanmar,Mymr"
    "\0"
    "Nabataean,Nbat"
    "\0"
    "Nandinagari,Nand"
    "\0"
    "New_Tai_Lue,Talu"
    "\0"
    "Newa,Newa"
    "\0"
    "Nko,Nkoo"
    "\0"
    "Nushu,Nshu"
    "\0"
    "Nyiakeng_Puachue_Hmong,Hmnp"
    "\0"
    "Ogham,Ogam"
    "\0"
    "Ol_Chiki,Olck"
    "\0"
    "Old_Hungarian,Hung"
    "\0"
    "Old_Italic,Ital"
    "\0"
    "Old_North_Arabian,Narb"
    "\0"
    "Old_Permic,Perm"
    "\0"
    "Old_Persian,Xpeo"
    "\0"
    "Old_Sogdian,Sogo"
    "\0"
    "Old_South_Arabian,Sarb"
    "\0"
    "Old_Turkic,Orkh"
    "\0"
    "Oriya,Orya"
    "\0"
    "Osage,Osge"
    "\0"
    "Osmanya,Osma"
    "\0"
    "Pahawh_Hmong,Hmng"
    "\0"
    "Palmyrene,Palm"
    "\0"
    "Pau_Cin_Hau,Pauc"
    "\0"
    "Phags_Pa,Phag"
    "\0"
    "Phoenician,Phnx"
    "\0"
    "Psalter_Pahlavi,Phlp"
    "\0"
    "Rejang,Rjng"
    "\0"
    "Runic,Runr"
    "\0"
    "Samaritan,Samr"
    "\0"
    "Saurashtra,Saur"
    "\0"
    "Sharada,Shrd"
    "\0"
    "Shavian,Shaw"
    "\0"
    "Siddham,Sidd"
    "\0"
    "SignWriting,Sgnw"
    "\0"
    "Sinhala,Sinh"
    "\0"
    "Sogdian,Sogd"
    "\0"
    "Sora_Sompeng,Sora"
    "\0"
    "Soyombo,Soyo"
    "\0"
    "Sundanese,Sund"
    "\0"
    "Syloti_Nagri,Sylo"
    "\0"
    "Syriac,Syrc"
    "\0"
    "Tagalog,Tglg"
    "\0"
    "Tagbanwa,Tagb"
    "\0"
    "Tai_Le,Tale"
    "\0"
    "Tai_Tham,Lana"
    "\0"
    "Tai_Viet,Tavt"
    "\0"
    "Takri,Takr"
    "\0"
    "Tamil,Taml"
    "\0"
    "Tangut,Tang"
    "\0"
    "Telugu,Telu"
    "\0"
    "Thaana,Thaa"
    "\0"
    "Thai,Thai"
    "\0"
    "Tibetan,Tibt"
    "\0"
    "Tifinagh,Tfng"
    "\0"
    "Tirhuta,Tirh"
    "\0"
    "Ugaritic,Ugar"
    "\0"
    "Vai,Vaii"
    "\0"
    "Wancho,Wcho"
    "\0"
    "Warang_Citi,Wara"
    "\0"
    "Yi,Yiii"
    "\0"
    "Zanabazar_Square,Zanb"
    "\0";

static const uint8_t unicode_script_table[2565] = {
    0xc0, 0x18, 0x99, 0x42, 0x85, 0x18, 0x99, 0x42, 0xae, 0x18, 0x80, 0x42,
    0x8e, 0x18, 0x80, 0x42, 0x84, 0x18, 0x96, 0x42, 0x80, 0x18, 0x9e, 0x42,
    0x80, 0x18, 0xe1, 0x60, 0x42, 0xa6, 0x18, 0x84, 0x42, 0x84, 0x18, 0x81,
    0x0d, 0x93, 0x18, 0xe0, 0x0f, 0x35, 0x83, 0x29, 0x80, 0x18, 0x82, 0x29,
    0x01, 0x83, 0x29, 0x80, 0x18, 0x80, 0x29, 0x03, 0x80, 0x29, 0x80, 0x18,
    0x80, 0x29, 0x80, 0x18, 0x82, 0x29, 0x00, 0x80, 0x29, 0x00, 0x93, 0x29,
    0x00, 0xbe, 0x29, 0x8d, 0x19, 0x8f, 0x29, 0xe0, 0x24, 0x1c, 0x81, 0x35,
    0xe0, 0x48, 0x1c, 0x00, 0xa5, 0x05, 0x01, 0xaf, 0x05, 0x80, 0x18, 0x80,
    0x05, 0x01, 0x82, 0x05, 0x00, 0xb6, 0x32, 0x07, 0x9a, 0x32, 0x03, 0x85,
    0x32, 0x0a, 0x84, 0x04, 0x80, 0x18, 0x85, 0x04, 0x80, 0x18, 0x8d, 0x04,
    0x80, 0x18, 0x80, 0x04, 0x00, 0x80, 0x04, 0x80, 0x18, 0x9f, 0x04, 0x80,
    0x18, 0x89, 0x04, 0x8a, 0x35, 0x99, 0x04, 0x80, 0x35, 0xe0, 0x0b, 0x04,
    0x80, 0x18, 0xa1, 0x04, 0x8d, 0x84, 0x00, 0xbb, 0x84, 0x01, 0x82, 0x84,
    0xaf, 0x04, 0xb1, 0x8e, 0x0d, 0xba, 0x60, 0x01, 0x82, 0x60, 0xad, 0x78,
    0x01, 0x8e, 0x78, 0x00, 0x9b, 0x4d, 0x01, 0x80, 0x4d, 0x00, 0x8a, 0x84,
    0x34, 0x94, 0x04, 0x00, 0x87, 0x04, 0x14, 0x8e, 0x04, 0x80, 0x18, 0x9c,
    0x04, 0xd0, 0x1e, 0x83, 0x35, 0x8e, 0x1e, 0x81, 0x18, 0x99, 0x1e, 0x83,
    0x0b, 0x00, 0x87, 0x0b, 0x01, 0x81, 0x0b, 0x01, 0x95, 0x0b, 0x00, 0x86,
    0x0b, 0x00, 0x80, 0x0b, 0x02, 0x83, 0x0b, 0x01, 0x88, 0x0b, 0x01, 0x81,
    0x0b, 0x01, 0x83, 0x0b, 0x07, 0x80, 0x0b, 0x03, 0x81, 0x0b, 0x00, 0x84,
    0x0b, 0x01, 0x98, 0x0b, 0x01, 0x82, 0x2c, 0x00, 0x85, 0x2c, 0x03, 0x81,
    0x2c, 0x01, 0x95, 0x2c, 0x00, 0x86, 0x2c, 0x00, 0x81, 0x2c, 0x00, 0x81,
    0x2c, 0x00, 0x81, 0x2c, 0x01, 0x80, 0x2c, 0x00, 0x84, 0x2c, 0x03, 0x81,
    0x2c, 0x01, 0x82, 0x2c, 0x02, 0x80, 0x2c, 0x06, 0x83, 0x2c, 0x00, 0x80,
    0x2c, 0x06, 0x90, 0x2c, 0x09, 0x82, 0x2a, 0x00, 0x88, 0x2a, 0x00, 0x82,
    0x2a, 0x00, 0x95, 0x2a, 0x00, 0x86, 0x2a, 0x00, 0x81, 0x2a, 0x00, 0x84,
    0x2a, 0x01, 0x89, 0x2a, 0x00, 0x82, 0x2a, 0x00, 0x82, 0x2a, 0x01, 0x80,
    0x2a, 0x0e, 0x83, 0x2a, 0x01, 0x8b, 0x2a, 0x06, 0x86, 0x2a, 0x00, 0x82,
    0x6d, 0x00, 0x87, 0x6d, 0x01, 0x81, 0x6d, 0x01, 0x95, 0x6d, 0x00, 0x86,
    0x6d, 0x00, 0x81, 0x6d, 0x00, 0x84, 0x6d, 0x01, 0x88, 0x6d, 0x01, 0x81,
    0x6d, 0x01, 0x82, 0x6d, 0x07, 0x81, 0x6d, 0x03, 0x81, 0x6d, 0x00, 0x84,
    0x6d, 0x01, 0x91, 0x6d, 0x09, 0x81, 0x8b, 0x00, 0x85, 0x8b, 0x02, 0x82,
    0x8b, 0x00, 0x83, 0x8b, 0x02, 0x81, 0x8b, 0x00, 0x80, 0x8b, 0x00, 0x81,
    0x8b, 0x02, 0x81, 0x8b, 0x02, 0x82, 0x8b, 0x02, 0x8b, 0x8b, 0x03, 0x84,
    0x8b, 0x02, 0x82, 0x8b, 0x00, 0x83, 0x8b, 0x01, 0x80, 0x8b, 0x05, 0x80,
    0x8b, 0x0d, 0x94, 0x8b, 0x04, 0x8c, 0x8d, 0x00, 0x82, 0x8d, 0x00, 0x96,
    0x8d, 0x00, 0x8f, 0x8d, 0x02, 0x87, 0x8d, 0x00, 0x82, 0x8d, 0x00, 0x83,
    0x8d, 0x06, 0x81, 0x8d, 0x00, 0x82, 0x8d, 0x04, 0x83, 0x8d, 0x01, 0x89,
    0x8d, 0x06, 0x88, 0x8d, 0x8c, 0x3a, 0x00, 0x82, 0x3a, 0x00, 0x96, 0x3a,
    0x00, 0x89, 0x3a, 0x00, 0x84, 0x3a, 0x01, 0x88, 0x3a, 0x00, 0x82, 0x3a,
    0x00, 0x83, 0x3a, 0x06, 0x81, 0x3a, 0x06, 0x80, 0x3a, 0x00, 0x83, 0x3a,
    0x01, 0x89, 0x3a, 0x00, 0x81, 0x3a, 0x0c, 0x83, 0x4c, 0x00, 0x87, 0x4c,
    0x00, 0x82, 0x4c, 0x00, 0xb2, 0x4c, 0x00, 0x82, 0x4c, 0x00, 0x85, 0x4c,
    0x03, 0x8f, 0x4c, 0x01, 0x99, 0x4c, 0x01, 0x81, 0x7e, 0x00, 0x91, 0x7e,
    0x02, 0x97, 0x7e, 0x00, 0x88, 0x7e, 0x00, 0x80, 0x7e, 0x01, 0x86, 0x7e,
    0x02, 0x80, 0x7e, 0x03, 0x85, 0x7e, 0x00, 0x80, 0x7e, 0x00, 0x87, 0x7e,
    0x05, 0x89, 0x7e, 0x01, 0x82, 0x7e, 0x0b, 0xb9, 0x8f, 0x03, 0x80, 0x18,
    0x9b, 0x8f, 0x24, 0x81, 0x41, 0x00, 0x80, 0x41, 0x00, 0x84, 0x41, 0x00,
    0x97, 0x41, 0x00, 0x80, 0x41, 0x00, 0x96, 0x41, 0x01, 0x84, 0x41, 0x00,
    0x80, 0x41, 0x00, 0x85, 0x41, 0x01, 0x89, 0x41, 0x01, 0x83, 0x41, 0x1f,
    0xc7, 0x90, 0x00, 0xa3, 0x90, 0x03, 0xa6, 0x90, 0x00, 0xa3, 0x90, 0x00,
    0x8e, 0x90, 0x00, 0x86, 0x90, 0x83, 0x18, 0x81, 0x90, 0x24, 0xe0, 0x3f,
    0x5b, 0xa5, 0x25, 0x00, 0x80, 0x25, 0x04, 0x80, 0x25, 0x01, 0xaa, 0x25,
    0x80, 0x18, 0x83, 0x25, 0xe0, 0x9f, 0x2e, 0xc8, 0x24, 0x00, 0x83, 0x24,
    0x01, 0x86, 0x24, 0x00, 0x80, 0x24, 0x00, 0x83, 0x24, 0x01, 0xa8, 0x24,
    0x00, 0x83, 0x24, 0x01, 0xa0, 0x24, 0x00, 0x83, 0x24, 0x01, 0x86, 0x24,
    0x00, 0x80, 0x24, 0x00, 0x83, 0x24, 0x01, 0x8e, 0x24, 0x00, 0xb8, 0x24,
    0x00, 0x83, 0x24, 0x01, 0xc2, 0x24, 0x01, 0x9f, 0x24, 0x02, 0x99, 0x24,
    0x05, 0xd5, 0x17, 0x01, 0x85, 0x17, 0x01, 0xe2, 0x1f, 0x12, 0x9c, 0x63,
    0x02, 0xca, 0x77, 0x82, 0x18, 0x8a, 0x77, 0x06, 0x8c, 0x85, 0x00, 0x86,
    0x85, 0x0a, 0x94, 0x30, 0x81, 0x18, 0x08, 0x93, 0x11, 0x0b, 0x8c, 0x86,
    0x00, 0x82, 0x86, 0x00, 0x81, 0x86, 0x0b, 0xdd, 0x3e, 0x01, 0x89, 0x3e,
    0x05, 0x89, 0x3e, 0x05, 0x81, 0x58, 0x81, 0x18, 0x80, 0x58, 0x80, 0x18,
    0x88, 0x58, 0x00, 0x89, 0x58, 0x05, 0xd8, 0x58, 0x06, 0xaa, 0x58, 0x04,
    0xc5, 0x12, 0x09, 0x9e, 0x44, 0x00, 0x8b, 0x44, 0x03, 0x8b, 0x44, 0x03,
    0x80, 0x44, 0x02, 0x8b, 0x44, 0x9d, 0x87, 0x01, 0x84, 0x87, 0x0a, 0xab,
    0x5e, 0x03, 0x99, 0x5e, 0x05, 0x8a, 0x5e, 0x02, 0x81, 0x5e, 0x9f, 0x3e,
    0x9b, 0x10, 0x01, 0x81, 0x10, 0xbe, 0x88, 0x00, 0x9c, 0x88, 0x01, 0x8a,
    0x88, 0x05, 0x89, 0x88, 0x05, 0x8d, 0x88, 0x01, 0x8e, 0x35, 0x40, 0xcb,
    0x07, 0x03, 0xac, 0x07, 0x02, 0xbf, 0x82, 0xb3, 0x0a, 0x07, 0x83, 0x0a,
    0xb7, 0x43, 0x02, 0x8e, 0x43, 0x02, 0x82, 0x43, 0xaf, 0x64, 0x88, 0x1c,
    0x06, 0xaa, 0x25, 0x01, 0x82, 0x25, 0x87, 0x82, 0x07, 0x82, 0x35, 0x80,
    0x18, 0x8c, 0x35, 0x80, 0x18, 0x86, 0x35, 0x83, 0x18, 0x80, 0x35, 0x85,
    0x18, 0x80, 0x35, 0x82, 0x18, 0x81, 0x35, 0x80, 0x18, 0x04, 0xa5, 0x42,
    0x84, 0x29, 0x80, 0x1c, 0xb0, 0x42, 0x84, 0x29, 0x83, 0x42, 0x84, 0x29,
    0x8c, 0x42, 0x80, 0x1c, 0xc5, 0x42, 0x80, 0x29, 0xb9, 0x35, 0x00, 0x84,
    0x35, 0xe0, 0x9f, 0x42, 0x95, 0x29, 0x01, 0x85, 0x29, 0x01, 0xa5, 0x29,
    0x01, 0x85, 0x29, 0x01, 0x87, 0x29, 0x00, 0x80, 0x29, 0x00, 0x80, 0x29,
    0x00, 0x80, 0x29, 0x00, 0x9e, 0x29, 0x01, 0xb4, 0x29, 0x00, 0x8e, 0x29,
    0x00, 0x8d, 0x29, 0x01, 0x85, 0x29, 0x00, 0x92, 0x29, 0x01, 0x82, 0x29,
    0x00, 0x88, 0x29, 0x00, 0x8b, 0x18, 0x81, 0x35, 0xd6, 0x18, 0x00, 0x8a,
    0x18, 0x80, 0x42, 0x01, 0x8a, 0x18, 0x80, 0x42, 0x8e, 0x18, 0x00, 0x8c,
    0x42, 0x02, 0x9f, 0x18, 0x0f, 0xa0, 0x35, 0x0e, 0xa5, 0x18, 0x80, 0x29,
    0x82, 0x18, 0x81, 0x42, 0x85, 0x18, 0x80, 0x42, 0x9a, 0x18, 0x80, 0x42,
    0x90, 0x18, 0xa8, 0x42, 0x82, 0x18, 0x03, 0xe2, 0x36, 0x18, 0x18, 0x8a,
    0x18, 0x14, 0xe3, 0x3f, 0x18, 0xe0, 0x9f, 0x0f, 0xe2, 0x13, 0x18, 0x01,
    0x9f, 0x18, 0x01, 0xe0, 0x07, 0x18, 0xae, 0x26, 0x00, 0xae, 0x26, 0x00,
    0x9f, 0x42, 0xe0, 0x13, 0x19, 0x04, 0x86, 0x19, 0xa5, 0x25, 0x00, 0x80,
    0x25, 0x04, 0x80, 0x25, 0x01, 0xb7, 0x91, 0x06, 0x81, 0x91, 0x0d, 0x80,
    0x91, 0x96, 0x24, 0x08, 0x86, 0x24, 0x00, 0x86, 0x24, 0x00, 0x86, 0x24,
    0x00, 0x86, 0x24, 0x00, 0x86, 0x24, 0x00, 0x86, 0x24, 0x00, 0x86, 0x24,
    0x00, 0x86, 0x24, 0x00, 0x9f, 0x1c, 0xcf, 0x18, 0x2f, 0x99, 0x2d, 0x00,
    0xd8, 0x2d, 0x0b, 0xe0, 0x75, 0x2d, 0x19, 0x8b, 0x18, 0x03, 0x84, 0x18,
    0x80, 0x2d, 0x80, 0x18, 0x80, 0x2d, 0x98, 0x18, 0x88, 0x2d, 0x83, 0x35,
    0x81, 0x2e, 0x87, 0x18, 0x83, 0x2d, 0x83, 0x18, 0x00, 0xd5, 0x33, 0x01,
    0x81, 0x35, 0x81, 0x18, 0x82, 0x33, 0x80, 0x18, 0xd9, 0x3b, 0x81, 0x18,
    0x82, 0x3b, 0x04, 0xaa, 0x0d, 0x00, 0xdd, 0x2e, 0x00, 0x8f, 0x18, 0x9a,
    0x0d, 0x04, 0xa3, 0x18, 0x0b, 0x8f, 0x3b, 0x9e, 0x2e, 0x00, 0xbf, 0x18,
    0x9e, 0x2e, 0xd0, 0x18, 0xae, 0x3b, 0x80, 0x18, 0xd7, 0x3b, 0xe0, 0x47,
    0x18, 0xf0, 0x09, 0x55, 0x2d, 0x09, 0xbf, 0x18, 0xf0, 0x41, 0x8f, 0x2d,
    0x0f, 0xe4, 0x2c, 0x97, 0x02, 0xb6, 0x97, 0x08, 0xaf, 0x47, 0xe0, 0xcb,
    0x94, 0x13, 0xdf, 0x1c, 0xd7, 0x08, 0x07, 0xa1, 0x18, 0xe0, 0x05, 0x42,
    0x82, 0x18, 0xb4, 0x42, 0x01, 0x84, 0x42, 0x2f, 0x88, 0x42, 0xab, 0x83,
    0x03, 0x89, 0x18, 0x05, 0xb7, 0x73, 0x07, 0xc5, 0x79, 0x07, 0x8b, 0x79,
    0x05, 0x9f, 0x1e, 0xad, 0x3c, 0x80, 0x18, 0x80, 0x3c, 0xa3, 0x76, 0x0a,
    0x80, 0x76, 0x9c, 0x2e, 0x02, 0xcd, 0x38, 0x00, 0x80, 0x18, 0x89, 0x38,
    0x03, 0x81, 0x38, 0x9e, 0x5b, 0x00, 0xb6, 0x16, 0x08, 0x8d, 0x16, 0x01,
    0x89, 0x16, 0x01, 0x83, 0x16, 0x9f, 0x5b, 0xc2, 0x89, 0x17, 0x84, 0x89,
    0x96, 0x52, 0x09, 0x85, 0x24, 0x01, 0x85, 0x24, 0x01, 0x85, 0x24, 0x08,
    0x86, 0x24, 0x00, 0x86, 0x24, 0x00, 0xaa, 0x42, 0x80, 0x18, 0x88, 0x42,
    0x80, 0x29, 0x81, 0x42, 0x07, 0xcf, 0x17, 0xad, 0x52, 0x01, 0x89, 0x52,
    0x05, 0xf0, 0x1b, 0x43, 0x2e, 0x0b, 0x96, 0x2e, 0x03, 0xb0, 0x2e, 0x70,
    0x10, 0xa3, 0xe1, 0x0d, 0x2d, 0x01, 0xe0, 0x09, 0x2d, 0x25, 0x86, 0x42,
    0x0b, 0x84, 0x05, 0x04, 0x99, 0x32, 0x00, 0x84, 0x32, 0x00, 0x80, 0x32,
    0x00, 0x81, 0x32, 0x00, 0x81, 0x32, 0x00, 0x89, 0x32, 0xe0, 0x11, 0x04,
    0x10, 0xe1, 0x0a, 0x04, 0x81, 0x18, 0x0f, 0xbf, 0x04, 0x01, 0xb5, 0x04,
    0x27, 0x8d, 0x04, 0x01, 0x8f, 0x35, 0x89, 0x18, 0x05, 0x8d, 0x35, 0x81,
    0x1c, 0xa2, 0x18, 0x00, 0x92, 0x18, 0x00, 0x83, 0x18, 0x03, 0x84, 0x04,
    0x00, 0xe0, 0x26, 0x04, 0x01, 0x80, 0x18, 0x00, 0x9f, 0x18, 0x99, 0x42,
    0x85, 0x18, 0x99, 0x42, 0x8a, 0x18, 0x89, 0x3b, 0x80, 0x18, 0xac, 0x3b,
    0x81, 0x18, 0x9e, 0x2e, 0x02, 0x85, 0x2e, 0x01, 0x85, 0x2e, 0x01, 0x85,
    0x2e, 0x01, 0x82, 0x2e, 0x02, 0x86, 0x18, 0x00, 0x86, 0x18, 0x09, 0x84,
    0x18, 0x01, 0x8b, 0x46, 0x00, 0x99, 0x46, 0x00, 0x92, 0x46, 0x00, 0x81,
    0x46, 0x00, 0x8e, 0x46, 0x01, 0x8d, 0x46, 0x21, 0xe0, 0x1a, 0x46, 0x04,
    0x82, 0x18, 0x03, 0xac, 0x18, 0x02, 0x88, 0x18, 0xce, 0x29, 0x00, 0x8b,
    0x18, 0x03, 0x80, 0x29, 0x2e, 0xac, 0x18, 0x80, 0x35, 0x60, 0x21, 0x9c,
    0x48, 0x02, 0xb0, 0x13, 0x0e, 0x80, 0x35, 0x9a, 0x18, 0x03, 0xa3, 0x66,
    0x08, 0x82, 0x66, 0x9a, 0x27, 0x04, 0xaa, 0x68, 0x04, 0x9d, 0x93, 0x00,
    0x80, 0x93, 0xa3, 0x69, 0x03, 0x8d, 0x69, 0x29, 0xcf, 0x1d, 0xaf, 0x7b,
    0x9d, 0x6f, 0x01, 0x89, 0x6f, 0x05, 0xa3, 0x6e, 0x03, 0xa3, 0x6e, 0x03,
    0xa7, 0x22, 0x07, 0xb3, 0x14, 0x0a, 0x80, 0x14, 0x60, 0x2f, 0xe0, 0xd6,
    0x45, 0x08, 0x95, 0x45, 0x09, 0x87, 0x45, 0x60, 0x37, 0x85, 0x1b, 0x01,
    0x80, 0x1b, 0x00, 0xab, 0x1b, 0x00, 0x81, 0x1b, 0x02, 0x80, 0x1b, 0x01,
    0x80, 0x1b, 0x95, 0x34, 0x00, 0x88, 0x34, 0x9f, 0x71, 0x9e, 0x5c, 0x07,
    0x88, 0x5c, 0x2f, 0x92, 0x31, 0x00, 0x81, 0x31, 0x04, 0x84, 0x31, 0x9b,
    0x74, 0x02, 0x80, 0x74, 0x99, 0x49, 0x04, 0x80, 0x49, 0x3f, 0x9f, 0x55,
    0x97, 0x54, 0x03, 0x93, 0x54, 0x01, 0xad, 0x54, 0x83, 0x3d, 0x00, 0x81,
    0x3d, 0x04, 0x87, 0x3d, 0x00, 0x82, 0x3d, 0x00, 0x9c, 0x3d, 0x01, 0x82,
    0x3d, 0x03, 0x89, 0x3d, 0x06, 0x88, 0x3d, 0x06, 0x9f, 0x6b, 0x9f, 0x67,
    0x1f, 0xa6, 0x4e, 0x03, 0x8b, 0x4e, 0x08, 0xb5, 0x06, 0x02, 0x86, 0x06,
    0x95, 0x37, 0x01, 0x87, 0x37, 0x92, 0x36, 0x04, 0x87, 0x36, 0x91, 0x75,
    0x06, 0x83, 0x75, 0x0b, 0x86, 0x75, 0x4f, 0xc8, 0x6c, 0x36, 0xb2, 0x65,
    0x0c, 0xb2, 0x65, 0x06, 0x85, 0x65, 0xa7, 0x2f, 0x07, 0x89, 0x2f, 0x60,
    0xc5, 0x9e, 0x04, 0x60, 0x20, 0xa7, 0x6a, 0x07, 0xa9, 0x7f, 0x60, 0x25,
    0x96, 0x23, 0x08, 0xcd, 0x0e, 0x03, 0x9d, 0x0e, 0x0e, 0x80, 0x0e, 0xc1,
    0x39, 0x0a, 0x80, 0x39, 0x01, 0x98, 0x80, 0x06, 0x89, 0x80, 0x05, 0xb4,
    0x15, 0x00, 0x90, 0x15, 0x08, 0xa6, 0x4b, 0x08, 0xcd, 0x7a, 0x01, 0x8f,
    0x7a, 0x00, 0x93, 0x7e, 0x0a, 0x91, 0x3f, 0x00, 0xab, 0x3f, 0x40, 0x86,
    0x5a, 0x00, 0x80, 0x5a, 0x00, 0x83, 0x5a, 0x00, 0x8e, 0x5a, 0x00, 0x8a,
    0x5a, 0x05, 0xba, 0x40, 0x04, 0x89, 0x40, 0x05, 0x83, 0x28, 0x00, 0x87,
    0x28, 0x01, 0x81, 0x28, 0x01, 0x95, 0x28, 0x00, 0x86, 0x28, 0x00, 0x81,
    0x28, 0x00, 0x84, 0x28, 0x00, 0x80, 0x35, 0x88, 0x28, 0x01, 0x81, 0x28,
    0x01, 0x82, 0x28, 0x01, 0x80, 0x28, 0x05, 0x80, 0x28, 0x04, 0x86, 0x28,
    0x01, 0x86, 0x28, 0x02, 0x84, 0x28, 0x60, 0x2a, 0xd9, 0x5f, 0x00, 0x80,
    0x5f, 0x00, 0x82, 0x5f, 0x1f, 0xc7, 0x92, 0x07, 0x89, 0x92, 0x60, 0x45,
    0xb5, 0x7c, 0x01, 0xa5, 0x7c, 0x21, 0xc4, 0x57, 0x0a, 0x89, 0x57, 0x05,
    0x8c, 0x58, 0x12, 0xb8, 0x8a, 0x06, 0x89, 0x8a, 0x35, 0x9a, 0x02, 0x01,
    0x8e, 0x02, 0x03, 0x8f, 0x02, 0x60, 0x5f, 0xbb, 0x1f, 0x60, 0x03, 0xd2,
    0x96, 0x0b, 0x80, 0x96, 0x60, 0x3f, 0x87, 0x5d, 0x01, 0xad, 0x5d, 0x01,
    0x8a, 0x5d, 0x1a, 0xc7, 0x98, 0x07, 0xd2, 0x81, 0x1c, 0xb8, 0x72, 0x60,
    0xa6, 0x88, 0x0c, 0x00, 0xac, 0x0c, 0x00, 0x8d, 0x0c, 0x09, 0x9c, 0x0c,
    0x02, 0x9f, 0x4f, 0x01, 0x95, 0x4f, 0x00, 0x8d, 0x4f, 0x48, 0x86, 0x50,
    0x00, 0x81, 0x50, 0x00, 0xab, 0x50, 0x02, 0x80, 0x50, 0x00, 0x81, 0x50,
    0x00, 0x88, 0x50, 0x07, 0x89, 0x50, 0x05, 0x85, 0x2b, 0x00, 0x81, 0x2b,
    0x00, 0xa4, 0x2b, 0x00, 0x81, 0x2b, 0x00, 0x85, 0x2b, 0x06, 0x89, 0x2b,
    0x60, 0xd5, 0x98, 0x4a, 0x60, 0x66, 0xb1, 0x8b, 0x0c, 0x80, 0x8b, 0xe3,
    0x39, 0x1a, 0x60, 0x05, 0xe0, 0x0e, 0x1a, 0x00, 0x84, 0x1a, 0x0a, 0xe0,
    0x63, 0x1a, 0x6a, 0x5b, 0xe3, 0xce, 0x21, 0x00, 0x88, 0x21, 0x6f, 0x66,
    0xe1, 0xe6, 0x03, 0x70, 0x11, 0x58, 0xe1, 0xd8, 0x08, 0x06, 0x9e, 0x59,
    0x00, 0x89, 0x59, 0x03, 0x81, 0x59, 0x5f, 0x9d, 0x09, 0x01, 0x85, 0x09,
    0x09, 0xc5, 0x70, 0x09, 0x89, 0x70, 0x00, 0x86, 0x70, 0x00, 0x94, 0x70,
    0x04, 0x92, 0x70, 0x62, 0x4f, 0xda, 0x51, 0x60, 0x04, 0xca, 0x56, 0x03,
    0xb8, 0x56, 0x06, 0x90, 0x56, 0x3f, 0x80, 0x8c, 0x80, 0x61, 0x81, 0x18,
    0x1b, 0xf0, 0x07, 0x97, 0x8c, 0x07, 0xe2, 0x92, 0x8c, 0x70, 0x14, 0xac,
    0x80, 0x3b, 0xe0, 0xbd, 0x33, 0x30, 0x82, 0x33, 0x10, 0x83, 0x3b, 0x07,
    0xe1, 0x2b, 0x61, 0x68, 0xa3, 0xe0, 0x0a, 0x20, 0x04, 0x8c, 0x20, 0x02,
    0x88, 0x20, 0x06, 0x89, 0x20, 0x01, 0x83, 0x20, 0x83, 0x18, 0x70, 0x02,
    0xfb, 0xe0, 0x95, 0x18, 0x09, 0xa6, 0x18, 0x01, 0xbd, 0x18, 0x82, 0x35,
    0x90, 0x18, 0x87, 0x35, 0x81, 0x18, 0x86, 0x35, 0x9d, 0x18, 0x83, 0x35,
    0xba, 0x18, 0x16, 0xc5, 0x29, 0x60, 0x39, 0x93, 0x18, 0x0b, 0xd6, 0x18,
    0x08, 0x98, 0x18, 0x60, 0x26, 0xd4, 0x18, 0x00, 0xc6, 0x18, 0x00, 0x81,
    0x18, 0x01, 0x80, 0x18, 0x01, 0x81, 0x18, 0x01, 0x83, 0x18, 0x00, 0x8b,
    0x18, 0x00, 0x80, 0x18, 0x00, 0x86, 0x18, 0x00, 0xc0, 0x18, 0x00, 0x83,
    0x18, 0x01, 0x87, 0x18, 0x00, 0x86, 0x18, 0x00, 0x9b, 0x18, 0x00, 0x83,
    0x18, 0x00, 0x84, 0x18, 0x00, 0x80, 0x18, 0x02, 0x86, 0x18, 0x00, 0xe0,
    0xf3, 0x18, 0x01, 0xe0, 0xc3, 0x18, 0x01, 0xb1, 0x18, 0xe2, 0x2b, 0x7d,
    0x0e, 0x84, 0x7d, 0x00, 0x8e, 0x7d, 0x64, 0xef, 0x86, 0x26, 0x00, 0x90,
    0x26, 0x01, 0x86, 0x26, 0x00, 0x81, 0x26, 0x00, 0x84, 0x26, 0x60, 0x74,
    0xac, 0x62, 0x02, 0x8d, 0x62, 0x01, 0x89, 0x62, 0x03, 0x81, 0x62, 0x61,
    0x0f, 0xb9, 0x95, 0x04, 0x80, 0x95, 0x64, 0x9f, 0xe0, 0x64, 0x53, 0x01,
    0x8f, 0x53, 0x28, 0xcb, 0x01, 0x03, 0x89, 0x01, 0x03, 0x81, 0x01, 0x62,
    0xb0, 0xc3, 0x18, 0x4b, 0xbc, 0x18, 0x60, 0x61, 0x83, 0x04, 0x00, 0x9a,
    0x04, 0x00, 0x81, 0x04, 0x00, 0x80, 0x04, 0x01, 0x80, 0x04, 0x00, 0x89,
    0x04, 0x00, 0x83, 0x04, 0x00, 0x80, 0x04, 0x00, 0x80, 0x04, 0x05, 0x80,
    0x04, 0x03, 0x80, 0x04, 0x00, 0x80, 0x04, 0x00, 0x80, 0x04, 0x00, 0x82,
    0x04, 0x00, 0x81, 0x04, 0x00, 0x80, 0x04, 0x01, 0x80, 0x04, 0x00, 0x80,
    0x04, 0x00, 0x80, 0x04, 0x00, 0x80, 0x04, 0x00, 0x80, 0x04, 0x00, 0x81,
    0x04, 0x00, 0x80, 0x04, 0x01, 0x83, 0x04, 0x00, 0x86, 0x04, 0x00, 0x83,
    0x04, 0x00, 0x83, 0x04, 0x00, 0x80, 0x04, 0x00, 0x89, 0x04, 0x00, 0x90,
    0x04, 0x04, 0x82, 0x04, 0x00, 0x84, 0x04, 0x00, 0x90, 0x04, 0x33, 0x81,
    0x04, 0x60, 0xad, 0xab, 0x18, 0x03, 0xe0, 0x03, 0x18, 0x0b, 0x8e, 0x18,
    0x01, 0x8e, 0x18, 0x00, 0x8e, 0x18, 0x00, 0xa4, 0x18, 0x09, 0x8c, 0x18,
    0x02, 0xdc, 0x18, 0x02, 0xbc, 0x18, 0x38, 0x99, 0x18, 0x80, 0x33, 0x81,
    0x18, 0x0c, 0xab, 0x18, 0x03, 0x88, 0x18, 0x06, 0x81, 0x18, 0x0d, 0x85,
    0x18, 0x60, 0x39, 0xe3, 0x75, 0x18, 0x09, 0x8c, 0x18, 0x02, 0x8a, 0x18,
    0x04, 0xe0, 0x13, 0x18, 0x0b, 0xd8, 0x18, 0x06, 0x8b, 0x18, 0x13, 0x8b,
    0x18, 0x03, 0xb7, 0x18, 0x07, 0x89, 0x18, 0x05, 0xa7, 0x18, 0x07, 0x9d,
    0x18, 0x51, 0x8b, 0x18, 0x00, 0xe0, 0x04, 0x18, 0x00, 0x83, 0x18, 0x02,
    0xa8, 0x18, 0x01, 0x85, 0x18, 0x02, 0x9c, 0x18, 0x01, 0xe0, 0x26, 0x18,
    0x0b, 0x8d, 0x18, 0x01, 0x83, 0x18, 0x03, 0x82, 0x18, 0x04, 0x82, 0x18,
    0x0c, 0x85, 0x18, 0x65, 0x09, 0xf0, 0x96, 0x76, 0x2d, 0x28, 0xef, 0xd4,
    0x2d, 0x0a, 0xe0, 0x7d, 0x2d, 0x01, 0xf0, 0x06, 0x21, 0x2d, 0x0d, 0xf0,
    0x0c, 0xd0, 0x2d, 0x6b, 0xbe, 0xe1, 0xbd, 0x2d, 0x7a, 0xf5, 0x82, 0x80,
    0x18, 0x1d, 0xdf, 0x18, 0x60, 0x1f, 0xe0, 0x8f, 0x35,
};

static const uint8_t unicode_script_ext_table[789] = {
    0x82, 0xc1, 0x00, 0x00, 0x01, 0x29, 0x01, 0x00, 0x00, 0x01, 0x29, 0x1c,
    0x00, 0x0c, 0x01, 0x42, 0x80, 0x92, 0x00, 0x00, 0x02, 0x1c, 0x68, 0x00,
    0x02, 0x1c, 0x26, 0x01, 0x02, 0x1c, 0x42, 0x00, 0x02, 0x1c, 0x26, 0x80,
    0x80, 0x00, 0x00, 0x02, 0x05, 0x25, 0x80, 0x01, 0x00, 0x00, 0x04, 0x04,
    0x2f, 0x84, 0x8e, 0x0d, 0x00, 0x00, 0x04, 0x04, 0x2f, 0x84, 0x8e, 0x00,
    0x03, 0x04, 0x84, 0x8e, 0x01, 0x00, 0x00, 0x04, 0x04, 0x2f, 0x84, 0x8e,
    0x1f, 0x00, 0x00, 0x08, 0x01, 0x04, 0x4d, 0x4e, 0x75, 0x2f, 0x7f, 0x84,
    0x09, 0x00, 0x0a, 0x02, 0x04, 0x84, 0x09, 0x00, 0x09, 0x02, 0x04, 0x8e,
    0x05, 0x00, 0x00, 0x02, 0x04, 0x84, 0x62, 0x00, 0x00, 0x02, 0x04, 0x2f,
    0x81, 0xfb, 0x00, 0x00, 0x0d, 0x0b, 0x1e, 0x28, 0x2a, 0x2c, 0x3a, 0x42,
    0x4c, 0x6d, 0x7a, 0x8b, 0x8d, 0x92, 0x00, 0x0c, 0x0b, 0x1e, 0x28, 0x2a,
    0x2c, 0x3a, 0x42, 0x4c, 0x6d, 0x8b, 0x8d, 0x92, 0x10, 0x00, 0x00, 0x14,
    0x0b, 0x1e, 0x1f, 0x2b, 0x50, 0x28, 0x2a, 0x2c, 0x3a, 0x4b, 0x4c, 0x5d,
    0x6d, 0x40, 0x7e, 0x83, 0x8a, 0x8b, 0x8d, 0x92, 0x00, 0x15, 0x0b, 0x1e,
    0x1f, 0x2b, 0x50, 0x28, 0x2a, 0x2c, 0x3a, 0x44, 0x4b, 0x4c, 0x5d, 0x6d,
    0x40, 0x7e, 0x83, 0x8a, 0x8b, 0x8d, 0x92, 0x09, 0x04, 0x1e, 0x1f, 0x39,
    0x4b, 0x75, 0x00, 0x09, 0x03, 0x0b, 0x15, 0x83, 0x75, 0x00, 0x09, 0x02,
    0x2c, 0x5a, 0x75, 0x00, 0x09, 0x02, 0x2a, 0x3f, 0x80, 0x75, 0x00, 0x0d,
    0x02, 0x28, 0x8b, 0x80, 0x71, 0x00, 0x09, 0x02, 0x3a, 0x5d, 0x82, 0xcf,
    0x00, 0x09, 0x03, 0x15, 0x5b, 0x87, 0x80, 0x30, 0x00, 0x00, 0x02, 0x25,
    0x42, 0x85, 0xb8, 0x00, 0x01, 0x04, 0x11, 0x30, 0x86, 0x85, 0x80, 0x4a,
    0x00, 0x01, 0x02, 0x58, 0x73, 0x00, 0x00, 0x00, 0x02, 0x58, 0x73, 0x84,
    0x49, 0x00, 0x00, 0x04, 0x0b, 0x1e, 0x28, 0x3a, 0x00, 0x01, 0x1e, 0x00,
    0x04, 0x0b, 0x1e, 0x28, 0x3a, 0x00, 0x02, 0x1e, 0x28, 0x00, 0x01, 0x1e,
    0x01, 0x02, 0x0b, 0x1e, 0x00, 0x02, 0x1e, 0x7a, 0x00, 0x02, 0x0b, 0x1e,
    0x00, 0x02, 0x1e, 0x7a, 0x00, 0x06, 0x1e, 0x3a, 0x4c, 0x6d, 0x8b, 0x8d,
    0x00, 0x01, 0x1e, 0x01, 0x02, 0x1e, 0x7a, 0x01, 0x01, 0x1e, 0x00, 0x02,
    0x1e, 0x7a, 0x00, 0x02, 0x0b, 0x1e, 0x06, 0x01, 0x1e, 0x00, 0x02, 0x1e,
    0x5d, 0x00, 0x02, 0x0b, 0x1e, 0x01, 0x01, 0x1e, 0x00, 0x02, 0x0b, 0x1e,
    0x03, 0x01, 0x1e, 0x00, 0x08, 0x0b, 0x1e, 0x28, 0x3a, 0x5d, 0x6d, 0x8d,
    0x92, 0x00, 0x02, 0x1e, 0x28, 0x00, 0x03, 0x1e, 0x28, 0x3a, 0x01, 0x02,
    0x0b, 0x1e, 0x00, 0x01, 0x0b, 0x01, 0x02, 0x1e, 0x28, 0x00, 0x01, 0x5d,
    0x80, 0x44, 0x00, 0x01, 0x01, 0x29, 0x81, 0xec, 0x00, 0x00, 0x02, 0x42,
    0x58, 0x80, 0x3f, 0x00, 0x00, 0x03, 0x1e, 0x28, 0x42, 0x8c, 0xd1, 0x00,
    0x00, 0x02, 0x1c, 0x26, 0x81, 0x3c, 0x00, 0x01, 0x06, 0x0d, 0x2e, 0x2d,
    0x33, 0x3b, 0x97, 0x00, 0x05, 0x0d, 0x2e, 0x2d, 0x33, 0x3b, 0x01, 0x00,
    0x00, 0x01, 0x2d, 0x00, 0x00, 0x09, 0x06, 0x0d, 0x2e, 0x2d, 0x33, 0x3b,
    0x97, 0x00, 0x00, 0x00, 0x05, 0x0d, 0x2e, 0x2d, 0x33, 0x3b, 0x07, 0x06,
    0x0d, 0x2e, 0x2d, 0x33, 0x3b, 0x97, 0x03, 0x05, 0x0d, 0x2e, 0x2d, 0x33,
    0x3b, 0x09, 0x00, 0x03, 0x02, 0x0d, 0x2d, 0x01, 0x00, 0x00, 0x05, 0x0d,
    0x2e, 0x2d, 0x33, 0x3b, 0x04, 0x02, 0x33, 0x3b, 0x00, 0x00, 0x00, 0x05,
    0x0d, 0x2e, 0x2d, 0x33, 0x3b, 0x03, 0x00, 0x01, 0x03, 0x2d, 0x33, 0x3b,
    0x01, 0x01, 0x2d, 0x58, 0x00, 0x03, 0x02, 0x33, 0x3b, 0x02, 0x00, 0x00,
    0x02, 0x33, 0x3b, 0x59, 0x00, 0x00, 0x06, 0x0d, 0x2e, 0x2d, 0x33, 0x3b,
    0x97, 0x00, 0x02, 0x33, 0x3b, 0x80, 0x12, 0x00, 0x0f, 0x01, 0x2d, 0x1f,
    0x00, 0x23, 0x01, 0x2d, 0x3b, 0x00, 0x27, 0x01, 0x2d, 0x37, 0x00, 0x30,
    0x01, 0x2d, 0x0e, 0x00, 0x0b, 0x01, 0x2d, 0x32, 0x00, 0x00, 0x01, 0x2d,
    0x57, 0x00, 0x18, 0x01, 0x2d, 0x09, 0x00, 0x04, 0x01, 0x2d, 0x5f, 0x00,
    0x1e, 0x01, 0x2d, 0xc0, 0x31, 0xef, 0x00, 0x00, 0x02, 0x1c, 0x26, 0x81,
    0x3f, 0x00, 0x02, 0x0e, 0x1e, 0x1f, 0x2a, 0x2c, 0x3f, 0x3a, 0x39, 0x4b,
    0x4c, 0x57, 0x5d, 0x40, 0x8a, 0x92, 0x02, 0x0d, 0x1e, 0x1f, 0x2a, 0x2c,
    0x3f, 0x3a, 0x39, 0x4b, 0x57, 0x5d, 0x40, 0x8a, 0x92, 0x03, 0x0b, 0x1e,
    0x1f, 0x2a, 0x2c, 0x3f, 0x39, 0x4b, 0x57, 0x40, 0x8a, 0x92, 0x80, 0x36,
    0x00, 0x00, 0x02, 0x0b, 0x1e, 0x00, 0x00, 0x00, 0x02, 0x1e, 0x8b, 0x39,
    0x00, 0x00, 0x03, 0x3c, 0x42, 0x5b, 0x80, 0x1f, 0x00, 0x00, 0x02, 0x10,
    0x38, 0xc0, 0x13, 0xa1, 0x00, 0x00, 0x02, 0x04, 0x8e, 0x09, 0x00, 0x00,
    0x02, 0x04, 0x8e, 0x46, 0x00, 0x01, 0x05, 0x0d, 0x2e, 0x2d, 0x33, 0x3b,
    0x80, 0x99, 0x00, 0x04, 0x06, 0x0d, 0x2e, 0x2d, 0x33, 0x3b, 0x97, 0x09,
    0x00, 0x00, 0x02, 0x33, 0x3b, 0x2c, 0x00, 0x01, 0x02, 0x33, 0x3b, 0x80,
    0xdf, 0x00, 0x02, 0x02, 0x1b, 0x46, 0x03, 0x00, 0x2c, 0x03, 0x1b, 0x45,
    0x46, 0x02, 0x00, 0x08, 0x02, 0x1b, 0x46, 0x81, 0x1f, 0x00, 0x1b, 0x02,
    0x04, 0x19, 0x8f, 0x84, 0x00, 0x00, 0x02, 0x28, 0x8b, 0x00, 0x00, 0x00,
    0x02, 0x28, 0x8b, 0x36, 0x00, 0x01, 0x02, 0x28, 0x8b, 0x8c, 0x12, 0x00,
    0x01, 0x02, 0x28, 0x8b, 0x00, 0x00, 0x00, 0x02, 0x28, 0x8b, 0xc0, 0x5c,
    0x4b, 0x00, 0x03, 0x01, 0x20, 0x96, 0x3b, 0x00, 0x11, 0x01, 0x2d, 0x9e,
    0x5d, 0x00, 0x01, 0x01, 0x2d, 0xce, 0xcd, 0x2d, 0x00,
};

static const uint8_t unicode_prop_Hyphen_table[28] = {
    0xac, 0x80, 0xfe, 0x80, 0x44, 0xdb, 0x80, 0x52, 0x7a, 0x80,
    0x48, 0x08, 0x81, 0x4e, 0x04, 0x80, 0x42, 0xe2, 0x80, 0x60,
    0xcd, 0x66, 0x80, 0x40, 0xa8, 0x80, 0xd6, 0x80,
};

static const uint8_t unicode_prop_Other_Math_table[200] = {
    0xdd, 0x80, 0x43, 0x70, 0x11, 0x80, 0x99, 0x09, 0x81, 0x5c, 0x1f, 0x80,
    0x9a, 0x82, 0x8a, 0x80, 0x9f, 0x83, 0x97, 0x81, 0x8d, 0x81, 0xc0, 0x8c,
    0x18, 0x11, 0x1c, 0x91, 0x03, 0x01, 0x89, 0x00, 0x14, 0x28, 0x11, 0x09,
    0x02, 0x05, 0x13, 0x24, 0xca, 0x21, 0x18, 0x08, 0x08, 0x00, 0x21, 0x0b,
    0x0b, 0x91, 0x09, 0x00, 0x06, 0x00, 0x29, 0x41, 0x21, 0x83, 0x40, 0xa7,
    0x08, 0x80, 0x97, 0x80, 0x90, 0x80, 0x41, 0xbc, 0x81, 0x8b, 0x88, 0x24,
    0x21, 0x09, 0x14, 0x8d, 0x00, 0x01, 0x85, 0x97, 0x81, 0xb8, 0x00, 0x80,
    0x9c, 0x83, 0x88, 0x81, 0x41, 0x55, 0x81, 0x9e, 0x89, 0x41, 0x92, 0x95,
    0xbe, 0x83, 0x9f, 0x81, 0x60, 0xd4, 0x62, 0x00, 0x03, 0x80, 0x40, 0xd2,
    0x00, 0x80, 0x60, 0xd4, 0xc0, 0xd4, 0x80, 0xc6, 0x01, 0x08, 0x09, 0x0b,
    0x80, 0x8b, 0x00, 0x06, 0x80, 0xc0, 0x03, 0x0f, 0x06, 0x80, 0x9b, 0x03,
    0x04, 0x00, 0x16, 0x80, 0x41, 0x53, 0x81, 0x98, 0x80, 0x98, 0x80, 0x9e,
    0x80, 0x98, 0x80, 0x9e, 0x80, 0x98, 0x80, 0x9e, 0x80, 0x98, 0x80, 0x9e,
    0x80, 0x98, 0x07, 0x81, 0xb1, 0x55, 0xff, 0x18, 0x9a, 0x01, 0x00, 0x08,
    0x80, 0x89, 0x03, 0x00, 0x00, 0x28, 0x18, 0x00, 0x00, 0x02, 0x01, 0x00,
    0x08, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x0b, 0x06, 0x03, 0x03, 0x00,
    0x80, 0x89, 0x80, 0x90, 0x22, 0x04, 0x80, 0x90,
};

static const uint8_t unicode_prop_Other_Alphabetic_table[396] = {
    0x43, 0x44, 0x80, 0x42, 0x69, 0x8d, 0x00, 0x01, 0x01, 0x00, 0xc7, 0x8a,
    0xaf, 0x8c, 0x06, 0x8f, 0x80, 0xe4, 0x33, 0x19, 0x0b, 0x80, 0xa2, 0x80,
    0x9d, 0x8f, 0xe5, 0x8a, 0xe4, 0x0a, 0x88, 0x02, 0x03, 0x40, 0xa6, 0x8b,
    0x16, 0x85, 0x93, 0xb5, 0x09, 0x8e, 0x01, 0x22, 0x89, 0x81, 0x9c, 0x82,
    0xb9, 0x31, 0x09, 0x81, 0x89, 0x80, 0x89, 0x81, 0x9c, 0x82, 0xb9, 0x23,
    0x09, 0x0b, 0x80, 0x9d, 0x0a, 0x80, 0x8a, 0x82, 0xb9, 0x38, 0x10, 0x81,
    0x94, 0x81, 0x95, 0x13, 0x82, 0xb9, 0x31, 0x09, 0x81, 0x88, 0x81, 0x89,
    0x81, 0x9d, 0x80, 0xba, 0x22, 0x10, 0x82, 0x89, 0x80, 0xa7, 0x83, 0xb9,
    0x30, 0x10, 0x17, 0x81, 0x8a, 0x81, 0x9c, 0x82, 0xb9, 0x30, 0x10, 0x17,
    0x81, 0x8a, 0x81, 0x9b, 0x83, 0xb9, 0x30, 0x10, 0x82, 0x89, 0x80, 0x89,
    0x81, 0x9d, 0x81, 0xca, 0x28, 0x00, 0x87, 0x91, 0x81, 0xbc, 0x01, 0x86,
    0x91, 0x80, 0xe2, 0x01, 0x28, 0x81, 0x8f, 0x80, 0x40, 0xa2, 0x90, 0x8a,
    0x8a, 0x80, 0xa3, 0xed, 0x8b, 0x00, 0x0b, 0x96, 0x1b, 0x10, 0x11, 0x32,
    0x83, 0x8c, 0x8b, 0x00, 0x89, 0x83, 0x46, 0x73, 0x81, 0x9d, 0x81, 0x9d,
    0x81, 0x9d, 0x81, 0xc1, 0x92, 0x40, 0xbb, 0x81, 0xa1, 0x80, 0xf5, 0x8b,
    0x83, 0x88, 0x40, 0xdd, 0x84, 0xb8, 0x89, 0x81, 0x93, 0x40, 0x8a, 0x84,
    0xaf, 0x8e, 0xbb, 0x82, 0x9d, 0x88, 0x09, 0xb8, 0x8a, 0xb1, 0x92, 0x41,
    0xaf, 0x8d, 0x46, 0xc0, 0xb3, 0x48, 0xf5, 0x9f, 0x60, 0x78, 0x73, 0x87,
    0xa1, 0x81, 0x41, 0x61, 0x07, 0x80, 0x96, 0x84, 0xd7, 0x81, 0xb1, 0x8f,
    0x00, 0xb8, 0x80, 0xa5, 0x84, 0x9b, 0x8b, 0xac, 0x83, 0xaf, 0x8b, 0xa4,
    0x80, 0xc2, 0x8d, 0x8b, 0x07, 0x81, 0xac, 0x82, 0xb1, 0x00, 0x11, 0x0c,
    0x80, 0xab, 0x24, 0x80, 0x40, 0xec, 0x87, 0x60, 0x4f, 0x32, 0x80, 0x48,
    0x56, 0x84, 0x46, 0x85, 0x10, 0x0c, 0x83, 0x43, 0x13, 0x83, 0x42, 0xd7,
    0x82, 0xb4, 0x8d, 0xbb, 0x80, 0xac, 0x88, 0xc6, 0x82, 0xa3, 0x8b, 0x91,
    0x81, 0xb8, 0x82, 0xaf, 0x8c, 0xeb, 0x88, 0x08, 0x28, 0x40, 0x9f, 0x89,
    0x96, 0x83, 0xb9, 0x31, 0x09, 0x81, 0x89, 0x80, 0x89, 0x81, 0x40, 0xd0,
    0x8c, 0x02, 0xe9, 0x91, 0x40, 0xec, 0x31, 0x86, 0x9c, 0x81, 0xd1, 0x8e,
    0x00, 0xe9, 0x8a, 0xe6, 0x8d, 0x41, 0x00, 0x8c, 0x41, 0x97, 0x31, 0x2b,
    0x80, 0x9b, 0x89, 0xa9, 0x20, 0x83, 0x91, 0x8a, 0xad, 0x8d, 0x41, 0x96,
    0x38, 0x86, 0xd2, 0x95, 0x80, 0x8d, 0xf9, 0x2a, 0x00, 0x08, 0x10, 0x02,
    0x80, 0xc1, 0x20, 0x08, 0x83, 0x41, 0x5b, 0x83, 0x60, 0x50, 0x57, 0x00,
    0xb6, 0x33, 0x60, 0x4d, 0x0a, 0x80, 0x60, 0x23, 0x60, 0x30, 0x90, 0x0e,
    0x01, 0x04, 0x49, 0x1b, 0x80, 0x47, 0xe7, 0x99, 0x85, 0x99, 0x85, 0x99,
};

static const uint8_t unicode_prop_Other_Lowercase_table[51] = {
    0x40, 0xa9, 0x80, 0x8e, 0x80, 0x41, 0xf4, 0x88, 0x31, 0x9d, 0x84,
    0xdf, 0x80, 0xb3, 0x80, 0x59, 0xb0, 0xbe, 0x8c, 0x80, 0xa1, 0xa4,
    0x42, 0xb0, 0x80, 0x8c, 0x80, 0x8f, 0x8c, 0x40, 0xd2, 0x8f, 0x43,
    0x4f, 0x99, 0x47, 0x91, 0x81, 0x60, 0x7a, 0x1d, 0x81, 0x40, 0xd1,
    0x80, 0x40, 0x86, 0x81, 0x43, 0x61, 0x83,
};

static const uint8_t unicode_prop_Other_Uppercase_table[15] = {
    0x60, 0x21, 0x5f, 0x8f, 0x43, 0x45, 0x99, 0x61,
    0xcc, 0x5f, 0x99, 0x85, 0x99, 0x85, 0x99,
};

static const uint8_t unicode_prop_Other_Grapheme_Extend_table[62] = {
    0x49, 0xbd, 0x80, 0x97, 0x80, 0x41, 0x65, 0x80, 0x97, 0x80, 0xe5,
    0x80, 0x97, 0x80, 0x40, 0xe9, 0x80, 0x91, 0x81, 0xe6, 0x80, 0x97,
    0x80, 0xf6, 0x80, 0x8e, 0x80, 0x4d, 0x54, 0x80, 0x44, 0xd5, 0x80,
    0x50, 0x20, 0x81, 0x60, 0xcf, 0x6d, 0x81, 0x53, 0x9d, 0x80, 0x97,
    0x80, 0x41, 0x57, 0x80, 0x8b, 0x80, 0x40, 0xf0, 0x80, 0x60, 0xbb,
    0xb4, 0x07, 0x84, 0x6c, 0x2e, 0xac, 0xdf,
};

static const uint8_t unicode_prop_Other_Default_Ignorable_Code_Point_table[32] =
    {
        0x43, 0x4e, 0x80, 0x4e, 0x0e, 0x81, 0x46, 0x52, 0x81, 0x48, 0xae,
        0x80, 0x50, 0xfd, 0x80, 0x60, 0xce, 0x3a, 0x80, 0xce, 0x88, 0x6d,
        0x00, 0x06, 0x00, 0x9d, 0xdf, 0xff, 0x40, 0xef, 0x4e, 0x0f,
};

static const uint8_t unicode_prop_Other_ID_Start_table[11] = {
    0x58, 0x84, 0x81, 0x48, 0x90, 0x80, 0x94, 0x80, 0x4f, 0x6b, 0x81,
};

static const uint8_t unicode_prop_Other_ID_Continue_table[12] = {
    0x40, 0xb6, 0x80, 0x42, 0xce, 0x80, 0x4f, 0xe0, 0x88, 0x46, 0x67, 0x80,
};

static const uint8_t unicode_prop_Prepended_Concatenation_Mark_table[17] = {
    0x45, 0xff, 0x85, 0x40, 0xd6, 0x80, 0xb0, 0x80, 0x41,
    0xd1, 0x80, 0x61, 0x07, 0xd9, 0x80, 0x8e, 0x80,
};

static const uint8_t unicode_prop_XID_Start1_table[31] = {
    0x43, 0x79, 0x80, 0x4a, 0xb7, 0x80, 0xfe, 0x80, 0x60, 0x21, 0xe6,
    0x81, 0x60, 0xcb, 0xc0, 0x85, 0x41, 0x95, 0x81, 0xf3, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x41, 0x1e, 0x81,
};

static const uint8_t unicode_prop_XID_Continue1_table[23] = {
    0x43, 0x79, 0x80, 0x60, 0x2d, 0x1f, 0x81, 0x60, 0xcb, 0xc0, 0x85, 0x41,
    0x95, 0x81, 0xf3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80,
};

static const uint8_t unicode_prop_Changes_When_Titlecased1_table[22] = {
    0x41, 0xc3, 0x08, 0x08, 0x81, 0xa4, 0x81, 0x4e, 0xdc, 0xaa, 0x0a,
    0x4e, 0x87, 0x3f, 0x3f, 0x87, 0x8b, 0x80, 0x8e, 0x80, 0xae, 0x80,
};

static const uint8_t unicode_prop_Changes_When_Casefolded1_table[33] = {
    0x40, 0xde, 0x80, 0xcf, 0x80, 0x97, 0x80, 0x44, 0x3c, 0x80, 0x59,
    0x11, 0x80, 0x40, 0xe4, 0x3f, 0x3f, 0x87, 0x89, 0x11, 0x05, 0x02,
    0x11, 0x80, 0xa9, 0x11, 0x80, 0x60, 0xdb, 0x07, 0x86, 0x8b, 0x84,
};

static const uint8_t unicode_prop_Changes_When_NFKC_Casefolded1_table[436] = {
    0x40, 0x9f, 0x06, 0x00, 0x01, 0x00, 0x01, 0x12, 0x10, 0x82, 0x9f, 0x80,
    0xcf, 0x01, 0x80, 0x8b, 0x07, 0x80, 0xfb, 0x01, 0x01, 0x80, 0xa5, 0x80,
    0x40, 0xbb, 0x88, 0x9e, 0x29, 0x84, 0xda, 0x08, 0x81, 0x89, 0x80, 0xa3,
    0x04, 0x02, 0x04, 0x08, 0x80, 0xc9, 0x82, 0x9c, 0x80, 0x41, 0x93, 0x80,
    0x40, 0x93, 0x80, 0xd7, 0x83, 0x42, 0xde, 0x87, 0xfb, 0x08, 0x80, 0xd2,
    0x01, 0x80, 0xa1, 0x11, 0x80, 0x40, 0xfc, 0x81, 0x42, 0xd4, 0x80, 0xfe,
    0x80, 0xa7, 0x81, 0xad, 0x80, 0xb5, 0x80, 0x88, 0x03, 0x03, 0x03, 0x80,
    0x8b, 0x80, 0x88, 0x00, 0x26, 0x80, 0x90, 0x80, 0x88, 0x03, 0x03, 0x03,
    0x80, 0x8b, 0x80, 0x41, 0x41, 0x80, 0xe1, 0x81, 0x46, 0x52, 0x81, 0xd4,
    0x83, 0x45, 0x1c, 0x10, 0x8a, 0x80, 0x91, 0x80, 0x9b, 0x8c, 0x80, 0xa1,
    0xa4, 0x40, 0xd9, 0x80, 0x40, 0xd5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x01, 0x3f, 0x3f, 0x87, 0x89, 0x11, 0x04, 0x00, 0x29, 0x04, 0x12, 0x80,
    0x88, 0x12, 0x80, 0x88, 0x11, 0x11, 0x04, 0x08, 0x8f, 0x00, 0x20, 0x8b,
    0x12, 0x2a, 0x08, 0x0b, 0x00, 0x07, 0x82, 0x8c, 0x06, 0x92, 0x81, 0x9a,
    0x80, 0x8c, 0x8a, 0x80, 0xd6, 0x18, 0x10, 0x8a, 0x01, 0x0c, 0x0a, 0x00,
    0x10, 0x11, 0x02, 0x06, 0x05, 0x1c, 0x85, 0x8f, 0x8f, 0x8f, 0x88, 0x80,
    0x40, 0xa1, 0x08, 0x81, 0x40, 0xf7, 0x81, 0x41, 0x34, 0xd5, 0x99, 0x9a,
    0x45, 0x20, 0x80, 0xe6, 0x82, 0xe4, 0x80, 0x41, 0x9e, 0x81, 0x40, 0xf0,
    0x80, 0x41, 0x2e, 0x80, 0xd2, 0x80, 0x8b, 0x40, 0xd5, 0xa9, 0x80, 0xb4,
    0x00, 0x82, 0xdf, 0x09, 0x80, 0xde, 0x80, 0xb0, 0xdd, 0x82, 0x8d, 0xdf,
    0x9e, 0x80, 0xa7, 0x87, 0xae, 0x80, 0x41, 0x7f, 0x60, 0x72, 0x9b, 0x81,
    0x40, 0xd1, 0x80, 0x40, 0x86, 0x81, 0x43, 0x61, 0x83, 0x60, 0x4d, 0x9f,
    0x41, 0x0d, 0x08, 0x00, 0x81, 0x89, 0x00, 0x00, 0x09, 0x82, 0xc3, 0x81,
    0xe9, 0xa5, 0x86, 0x8b, 0x24, 0x00, 0x97, 0x04, 0x00, 0x01, 0x01, 0x80,
    0xeb, 0xa0, 0x41, 0x6a, 0x91, 0xbf, 0x81, 0xb5, 0xa7, 0x8c, 0x82, 0x99,
    0x95, 0x94, 0x81, 0x8b, 0x80, 0x92, 0x03, 0x1a, 0x00, 0x80, 0x40, 0x86,
    0x08, 0x80, 0x9f, 0x99, 0x40, 0x83, 0x15, 0x0d, 0x0d, 0x0a, 0x16, 0x06,
    0x80, 0x88, 0x60, 0xbc, 0xa6, 0x83, 0x54, 0xb9, 0x86, 0x8d, 0x87, 0xbf,
    0x85, 0x42, 0x3e, 0xd4, 0x80, 0xc6, 0x01, 0x08, 0x09, 0x0b, 0x80, 0x8b,
    0x00, 0x06, 0x80, 0xc0, 0x03, 0x0f, 0x06, 0x80, 0x9b, 0x03, 0x04, 0x00,
    0x16, 0x80, 0x41, 0x53, 0x81, 0x41, 0x23, 0x81, 0xb1, 0x55, 0xff, 0x18,
    0x9a, 0x01, 0x00, 0x08, 0x80, 0x89, 0x03, 0x00, 0x00, 0x28, 0x18, 0x00,
    0x00, 0x02, 0x01, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x0b,
    0x06, 0x03, 0x03, 0x00, 0x80, 0x89, 0x80, 0x90, 0x22, 0x04, 0x80, 0x90,
    0x42, 0x43, 0x8a, 0x84, 0x9e, 0x80, 0x9f, 0x99, 0x82, 0xa2, 0x80, 0xee,
    0x82, 0x8c, 0xab, 0x83, 0x88, 0x31, 0x61, 0x05, 0xad, 0x42, 0x1d, 0x6b,
    0x05, 0xe1, 0x4f, 0xff,
};

static const uint8_t unicode_prop_ASCII_Hex_Digit_table[5] = {
    0xaf, 0x89, 0x35, 0x99, 0x85,
};

static const uint8_t unicode_prop_Bidi_Control_table[10] = {
    0x46, 0x1b, 0x80, 0x59, 0xf0, 0x81, 0x99, 0x84, 0xb6, 0x83,
};

static const uint8_t unicode_prop_Dash_table[50] = {
    0xac, 0x80, 0x45, 0x5b, 0x80, 0xb2, 0x80, 0x4e, 0x40, 0x80,
    0x44, 0x04, 0x80, 0x48, 0x08, 0x85, 0xbc, 0x80, 0xa6, 0x80,
    0x8e, 0x80, 0x41, 0x85, 0x80, 0x4c, 0x03, 0x01, 0x80, 0x9e,
    0x0b, 0x80, 0x41, 0xda, 0x80, 0x92, 0x80, 0xee, 0x80, 0x60,
    0xcd, 0x8f, 0x81, 0xa4, 0x80, 0x89, 0x80, 0x40, 0xa8, 0x80,
};

static const uint8_t unicode_prop_Deprecated_table[23] = {
    0x41, 0x48, 0x80, 0x45, 0x28, 0x80, 0x49, 0x02, 0x00, 0x80, 0x48, 0x28,
    0x81, 0x48, 0xc4, 0x85, 0x42, 0xb8, 0x81, 0x6d, 0xdc, 0xd5, 0x80,
};

static const uint8_t unicode_prop_Diacritic_table[350] = {
    0xdd, 0x00, 0x80, 0xc6, 0x05, 0x03, 0x01, 0x81, 0x41, 0xf6, 0x40, 0x9e,
    0x07, 0x25, 0x90, 0x0b, 0x80, 0x88, 0x81, 0x40, 0xfc, 0x84, 0x40, 0xd0,
    0x80, 0xb6, 0x90, 0x80, 0x9a, 0x00, 0x01, 0x00, 0x40, 0x85, 0x3b, 0x81,
    0x40, 0x85, 0x0b, 0x0a, 0x82, 0xc2, 0x9a, 0xda, 0x8a, 0xb9, 0x8a, 0xa1,
    0x81, 0x40, 0xc8, 0x9b, 0xbc, 0x80, 0x8f, 0x02, 0x83, 0x9b, 0x80, 0xc9,
    0x80, 0x8f, 0x80, 0xed, 0x80, 0x8f, 0x80, 0xed, 0x80, 0x8f, 0x80, 0xae,
    0x82, 0xbb, 0x80, 0x8f, 0x80, 0xfe, 0x80, 0xfe, 0x80, 0xed, 0x80, 0x8f,
    0x80, 0xec, 0x81, 0x8f, 0x80, 0xfb, 0x80, 0xfb, 0x28, 0x80, 0xea, 0x80,
    0x8c, 0x84, 0xca, 0x81, 0x9a, 0x00, 0x00, 0x03, 0x81, 0xc1, 0x10, 0x81,
    0xbd, 0x80, 0xef, 0x00, 0x81, 0xa7, 0x0b, 0x84, 0x98, 0x30, 0x80, 0x89,
    0x81, 0x42, 0xc0, 0x82, 0x44, 0x68, 0x8a, 0x88, 0x80, 0x41, 0x5a, 0x82,
    0x41, 0x38, 0x39, 0x80, 0xaf, 0x8d, 0xf5, 0x80, 0x8e, 0x80, 0xa5, 0x88,
    0xb5, 0x81, 0x40, 0x89, 0x81, 0xbf, 0x85, 0xd1, 0x98, 0x18, 0x28, 0x0a,
    0xb1, 0xbe, 0xd8, 0x8b, 0xa4, 0x22, 0x82, 0x41, 0xbc, 0x00, 0x82, 0x8a,
    0x82, 0x8c, 0x82, 0x8c, 0x82, 0x8c, 0x81, 0x4c, 0xef, 0x82, 0x41, 0x3c,
    0x80, 0x41, 0xf9, 0x85, 0xe8, 0x83, 0xde, 0x80, 0x60, 0x75, 0x71, 0x80,
    0x8b, 0x08, 0x80, 0x9b, 0x81, 0xd1, 0x81, 0x8d, 0xa1, 0xe5, 0x82, 0xec,
    0x81, 0x40, 0xc9, 0x80, 0x9a, 0x91, 0xb8, 0x83, 0xa3, 0x80, 0xde, 0x80,
    0x8b, 0x80, 0xa3, 0x80, 0x40, 0x94, 0x82, 0xc0, 0x83, 0xb2, 0x80, 0xe3,
    0x84, 0x40, 0x8b, 0x81, 0x60, 0x4f, 0x2f, 0x80, 0x43, 0x00, 0x8f, 0x41,
    0x0d, 0x00, 0x80, 0xae, 0x80, 0xac, 0x81, 0xc2, 0x80, 0x42, 0xfb, 0x80,
    0x48, 0x03, 0x81, 0x42, 0x3a, 0x85, 0x42, 0x1d, 0x8a, 0x41, 0x67, 0x81,
    0xf7, 0x81, 0xbd, 0x80, 0xcb, 0x80, 0x88, 0x82, 0xe7, 0x81, 0x40, 0xb1,
    0x81, 0xd0, 0x80, 0x8f, 0x80, 0x97, 0x32, 0x84, 0x40, 0xcc, 0x02, 0x80,
    0xfa, 0x81, 0x40, 0xfa, 0x81, 0xfd, 0x80, 0xf5, 0x81, 0xf2, 0x80, 0x41,
    0x0c, 0x81, 0x41, 0xa4, 0x80, 0xd2, 0x80, 0x91, 0x80, 0xd0, 0x80, 0x41,
    0xa4, 0x80, 0x41, 0x01, 0x00, 0x81, 0xd0, 0x80, 0x60, 0x4d, 0x57, 0x84,
    0xba, 0x86, 0x44, 0x57, 0x90, 0x60, 0x61, 0xc6, 0x12, 0x2f, 0x39, 0x86,
    0x9d, 0x83, 0x4f, 0x81, 0x86, 0x41, 0xb4, 0x83, 0x45, 0xdf, 0x86, 0xec,
    0x10, 0x82,
};

static const uint8_t unicode_prop_Extender_table[86] = {
    0x40, 0xb6, 0x80, 0x42, 0x17, 0x81, 0x43, 0x6d, 0x80, 0x41, 0xb8,
    0x80, 0x46, 0x4a, 0x80, 0xfe, 0x80, 0x49, 0x42, 0x80, 0xb7, 0x80,
    0x42, 0x62, 0x80, 0x41, 0x8d, 0x80, 0xc3, 0x80, 0x53, 0x88, 0x80,
    0xaa, 0x84, 0xe6, 0x81, 0xdc, 0x82, 0x60, 0x6f, 0x15, 0x80, 0x45,
    0xf5, 0x80, 0x43, 0xc1, 0x80, 0x95, 0x80, 0x40, 0x88, 0x80, 0xeb,
    0x80, 0x94, 0x81, 0x60, 0x54, 0x7a, 0x80, 0x53, 0xeb, 0x80, 0x42,
    0x67, 0x82, 0x44, 0xce, 0x80, 0x60, 0x50, 0xa8, 0x81, 0x44, 0x9b,
    0x08, 0x80, 0x60, 0x71, 0x57, 0x81, 0x48, 0x05, 0x82,
};

static const uint8_t unicode_prop_Hex_Digit_table[12] = {
    0xaf, 0x89, 0x35, 0x99, 0x85, 0x60, 0xfe, 0xa8, 0x89, 0x35, 0x99, 0x85,
};

static const uint8_t unicode_prop_IDS_Binary_Operator_table[5] = {
    0x60, 0x2f, 0xef, 0x09, 0x87,
};

static const uint8_t unicode_prop_IDS_Trinary_Operator_table[4] = {
    0x60,
    0x2f,
    0xf1,
    0x81,
};

static const uint8_t unicode_prop_Ideographic_table[58] = {
    0x60, 0x30, 0x05, 0x81, 0x98, 0x88, 0x8d, 0x82, 0x43, 0xc4, 0x59, 0xb5,
    0xc9, 0x60, 0x51, 0xef, 0x60, 0x59, 0x0f, 0x41, 0x6d, 0x81, 0xe9, 0x60,
    0x75, 0x25, 0x57, 0xf7, 0x87, 0x42, 0xf2, 0x60, 0x26, 0x7c, 0x41, 0x8b,
    0x60, 0x4d, 0x03, 0x60, 0xa6, 0xd6, 0xa8, 0x50, 0x34, 0x8a, 0x40, 0xdd,
    0x81, 0x56, 0x81, 0x8d, 0x5d, 0x30, 0x4c, 0x1e, 0x42, 0x1d,
};

static const uint8_t unicode_prop_Join_Control_table[4] = {
    0x60,
    0x20,
    0x0b,
    0x81,
};

static const uint8_t unicode_prop_Logical_Order_Exception_table[15] = {
    0x4e, 0x3f, 0x84, 0xfa, 0x84, 0x4a, 0xef, 0x11,
    0x80, 0x60, 0x90, 0xf9, 0x09, 0x00, 0x81,
};

static const uint8_t unicode_prop_Noncharacter_Code_Point_table[71] = {
    0x60, 0xfd, 0xcf, 0x9f, 0x42, 0x0d, 0x81, 0x60, 0xff, 0xfd, 0x81, 0x60,
    0xff, 0xfd, 0x81, 0x60, 0xff, 0xfd, 0x81, 0x60, 0xff, 0xfd, 0x81, 0x60,
    0xff, 0xfd, 0x81, 0x60, 0xff, 0xfd, 0x81, 0x60, 0xff, 0xfd, 0x81, 0x60,
    0xff, 0xfd, 0x81, 0x60, 0xff, 0xfd, 0x81, 0x60, 0xff, 0xfd, 0x81, 0x60,
    0xff, 0xfd, 0x81, 0x60, 0xff, 0xfd, 0x81, 0x60, 0xff, 0xfd, 0x81, 0x60,
    0xff, 0xfd, 0x81, 0x60, 0xff, 0xfd, 0x81, 0x60, 0xff, 0xfd, 0x81,
};

static const uint8_t unicode_prop_Pattern_Syntax_table[58] = {
    0xa0, 0x8e, 0x89, 0x86, 0x99, 0x18, 0x80, 0x99, 0x83, 0xa1, 0x30, 0x00,
    0x08, 0x00, 0x0b, 0x03, 0x02, 0x80, 0x96, 0x80, 0x9e, 0x80, 0x5f, 0x17,
    0x97, 0x87, 0x8e, 0x81, 0x92, 0x80, 0x89, 0x41, 0x30, 0x42, 0xcf, 0x40,
    0x9f, 0x42, 0x75, 0x9d, 0x44, 0x6b, 0x41, 0xff, 0xff, 0x41, 0x80, 0x13,
    0x98, 0x8e, 0x80, 0x60, 0xcd, 0x0c, 0x81, 0x41, 0x04, 0x81,
};

static const uint8_t unicode_prop_Pattern_White_Space_table[11] = {
    0x88, 0x84, 0x91, 0x80, 0xe3, 0x80, 0x5f, 0x87, 0x81, 0x97, 0x81,
};

static const uint8_t unicode_prop_Quotation_Mark_table[31] = {
    0xa1, 0x03, 0x80, 0x40, 0x82, 0x80, 0x8e, 0x80, 0x5f, 0x5b, 0x87,
    0x98, 0x81, 0x4e, 0x06, 0x80, 0x41, 0xc8, 0x83, 0x8c, 0x82, 0x60,
    0xce, 0x20, 0x83, 0x40, 0xbc, 0x03, 0x80, 0xd9, 0x81,
};

static const uint8_t unicode_prop_Radical_table[9] = {
    0x60, 0x2e, 0x7f, 0x99, 0x80, 0xd8, 0x8b, 0x40, 0xd5,
};

static const uint8_t unicode_prop_Regional_Indicator_table[4] = {
    0x61,
    0xf1,
    0xe5,
    0x99,
};

static const uint8_t unicode_prop_Sentence_Terminal_table[184] = {
    0xa0, 0x80, 0x8b, 0x80, 0x8f, 0x80, 0x45, 0x48, 0x80, 0x40, 0x93, 0x81,
    0x40, 0xb3, 0x80, 0xaa, 0x82, 0x40, 0xf5, 0x80, 0xbc, 0x00, 0x02, 0x81,
    0x41, 0x24, 0x81, 0x46, 0xe3, 0x81, 0x43, 0x15, 0x03, 0x81, 0x43, 0x04,
    0x80, 0x40, 0xc5, 0x81, 0x40, 0xcb, 0x04, 0x80, 0x41, 0x39, 0x81, 0x41,
    0x61, 0x83, 0x40, 0xad, 0x09, 0x81, 0x40, 0xda, 0x81, 0xc0, 0x81, 0x43,
    0xbb, 0x81, 0x88, 0x82, 0x4d, 0xe3, 0x80, 0x8c, 0x80, 0x41, 0xc4, 0x80,
    0x60, 0x74, 0xfb, 0x80, 0x41, 0x0d, 0x81, 0x40, 0xe2, 0x02, 0x80, 0x41,
    0x7d, 0x81, 0xd5, 0x81, 0xde, 0x80, 0x40, 0x97, 0x81, 0x40, 0x92, 0x82,
    0x40, 0x8f, 0x81, 0x40, 0xf8, 0x80, 0x60, 0x52, 0x65, 0x02, 0x81, 0x40,
    0xa8, 0x80, 0x8b, 0x80, 0x8f, 0x80, 0xc0, 0x80, 0x4a, 0xf3, 0x81, 0x44,
    0xfc, 0x84, 0x40, 0xec, 0x81, 0xf4, 0x83, 0xfe, 0x82, 0x40, 0x80, 0x0d,
    0x80, 0x8f, 0x81, 0xd7, 0x08, 0x81, 0xeb, 0x80, 0x41, 0xa0, 0x81, 0x41,
    0x74, 0x0c, 0x8e, 0xe8, 0x81, 0x40, 0xf8, 0x82, 0x43, 0x02, 0x81, 0xd6,
    0x81, 0x41, 0xa3, 0x81, 0x42, 0xb3, 0x81, 0x60, 0x4b, 0x74, 0x81, 0x40,
    0x84, 0x80, 0xc0, 0x81, 0x8a, 0x80, 0x43, 0x52, 0x80, 0x60, 0x4e, 0x05,
    0x80, 0x5d, 0xe7, 0x80,
};

static const uint8_t unicode_prop_Soft_Dotted_table[71] = {
    0xe8, 0x81, 0x40, 0xc3, 0x80, 0x41, 0x18, 0x80, 0x9d, 0x80, 0xb3, 0x80,
    0x93, 0x80, 0x41, 0x3f, 0x80, 0xe1, 0x00, 0x80, 0x59, 0x08, 0x80, 0xb2,
    0x80, 0x8c, 0x02, 0x80, 0x40, 0x83, 0x80, 0x40, 0x9c, 0x80, 0x41, 0xa4,
    0x80, 0x40, 0xd5, 0x81, 0x4b, 0x31, 0x80, 0x61, 0xa7, 0xa4, 0x81, 0xb1,
    0x81, 0xb1, 0x81, 0xb1, 0x81, 0xb1, 0x81, 0xb1, 0x81, 0xb1, 0x81, 0xb1,
    0x81, 0xb1, 0x81, 0xb1, 0x81, 0xb1, 0x81, 0xb1, 0x81, 0xb1, 0x81,
};

static const uint8_t unicode_prop_Terminal_Punctuation_table[237] = {
    0xa0, 0x80, 0x89, 0x00, 0x80, 0x8a, 0x0a, 0x80, 0x43, 0x3d, 0x07, 0x80,
    0x42, 0x00, 0x80, 0xb8, 0x80, 0xc7, 0x80, 0x8d, 0x01, 0x81, 0x40, 0xb3,
    0x80, 0xaa, 0x8a, 0x00, 0x40, 0xea, 0x81, 0xb5, 0x8e, 0x9e, 0x80, 0x41,
    0x04, 0x81, 0x44, 0xf3, 0x81, 0x40, 0xab, 0x03, 0x85, 0x41, 0x36, 0x81,
    0x43, 0x14, 0x87, 0x43, 0x04, 0x80, 0xfb, 0x82, 0xc6, 0x81, 0x40, 0x9c,
    0x12, 0x80, 0xa6, 0x19, 0x81, 0x41, 0x39, 0x81, 0x41, 0x61, 0x83, 0x40,
    0xad, 0x08, 0x82, 0x40, 0xda, 0x84, 0xbd, 0x81, 0x43, 0xbb, 0x81, 0x88,
    0x82, 0x4d, 0xe3, 0x80, 0x8c, 0x03, 0x80, 0x89, 0x00, 0x81, 0x41, 0xb0,
    0x81, 0x60, 0x74, 0xfa, 0x81, 0x41, 0x0c, 0x82, 0x40, 0xe2, 0x84, 0x41,
    0x7d, 0x81, 0xd5, 0x81, 0xde, 0x80, 0x40, 0x96, 0x82, 0x40, 0x92, 0x82,
    0xfe, 0x80, 0x8f, 0x81, 0x40, 0xf8, 0x80, 0x60, 0x52, 0x63, 0x10, 0x83,
    0x40, 0xa8, 0x80, 0x89, 0x00, 0x80, 0x8a, 0x0a, 0x80, 0xc0, 0x01, 0x80,
    0x44, 0x39, 0x80, 0xaf, 0x80, 0x44, 0x85, 0x80, 0x40, 0xc6, 0x80, 0x41,
    0x35, 0x81, 0x40, 0x97, 0x85, 0xc3, 0x85, 0xd8, 0x83, 0x43, 0xb7, 0x84,
    0x40, 0xec, 0x86, 0xef, 0x83, 0xfe, 0x82, 0x40, 0x80, 0x0d, 0x80, 0x8f,
    0x81, 0xd7, 0x84, 0xeb, 0x80, 0x41, 0xa0, 0x82, 0x8c, 0x80, 0x41, 0x65,
    0x1a, 0x8e, 0xe8, 0x81, 0x40, 0xf8, 0x82, 0x43, 0x02, 0x81, 0xd6, 0x0b,
    0x81, 0x41, 0x9d, 0x82, 0xac, 0x80, 0x42, 0x84, 0x81, 0x45, 0x76, 0x84,
    0x60, 0x45, 0xf8, 0x81, 0x40, 0x84, 0x80, 0xc0, 0x82, 0x89, 0x80, 0x43,
    0x51, 0x81, 0x60, 0x4e, 0x05, 0x80, 0x5d, 0xe6, 0x83,
};

static const uint8_t unicode_prop_Unified_Ideograph_table[38] = {
    0x60, 0x33, 0xff, 0x59, 0xb5, 0xc9, 0x60, 0x51, 0xef, 0x60,
    0x5a, 0x1d, 0x08, 0x00, 0x81, 0x89, 0x00, 0x00, 0x09, 0x82,
    0x61, 0x05, 0xd5, 0x60, 0xa6, 0xd6, 0xa8, 0x50, 0x34, 0x8a,
    0x40, 0xdd, 0x81, 0x56, 0x81, 0x8d, 0x5d, 0x30,
};

static const uint8_t unicode_prop_Variation_Selector_table[12] = {
    0x58, 0x0a, 0x82, 0x60, 0xe5, 0xf1, 0x8f, 0x6d, 0x02, 0xef, 0x40, 0xef,
};

static const uint8_t unicode_prop_White_Space_table[22] = {
    0x88, 0x84, 0x91, 0x80, 0xe3, 0x80, 0x99, 0x80, 0x55, 0xde, 0x80,
    0x49, 0x7e, 0x8a, 0x9c, 0x0c, 0x80, 0xae, 0x80, 0x4f, 0x9f, 0x80,
};

static const uint8_t unicode_prop_Bidi_Mirrored_table[171] = {
    0xa7, 0x81, 0x91, 0x00, 0x80, 0x9b, 0x00, 0x80, 0x9c, 0x00, 0x80, 0xac,
    0x80, 0x8e, 0x80, 0x4e, 0x7d, 0x83, 0x47, 0x5c, 0x81, 0x49, 0x9b, 0x81,
    0x89, 0x81, 0xb5, 0x81, 0x8d, 0x81, 0x40, 0xb0, 0x80, 0x40, 0xbf, 0x1a,
    0x2a, 0x02, 0x0a, 0x18, 0x18, 0x00, 0x03, 0x88, 0x20, 0x80, 0x91, 0x23,
    0x88, 0x08, 0x00, 0x39, 0x9e, 0x0b, 0x20, 0x88, 0x09, 0x92, 0x21, 0x88,
    0x21, 0x0b, 0x97, 0x81, 0x8f, 0x3b, 0x93, 0x0e, 0x81, 0x44, 0x3c, 0x8d,
    0xc9, 0x01, 0x18, 0x08, 0x14, 0x1c, 0x12, 0x8d, 0x41, 0x92, 0x95, 0x0d,
    0x80, 0x8d, 0x38, 0x35, 0x10, 0x1c, 0x01, 0x0c, 0x18, 0x02, 0x09, 0x89,
    0x29, 0x81, 0x8b, 0x92, 0x03, 0x08, 0x00, 0x08, 0x03, 0x21, 0x2a, 0x97,
    0x81, 0x8a, 0x0b, 0x18, 0x09, 0x0b, 0xaa, 0x0f, 0x80, 0xa7, 0x20, 0x00,
    0x14, 0x22, 0x18, 0x14, 0x00, 0x40, 0xff, 0x80, 0x42, 0x02, 0x1a, 0x08,
    0x81, 0x8d, 0x09, 0x89, 0x41, 0xdd, 0x89, 0x0f, 0x60, 0xce, 0x3c, 0x2c,
    0x81, 0x40, 0xa1, 0x81, 0x91, 0x00, 0x80, 0x9b, 0x00, 0x80, 0x9c, 0x00,
    0x00, 0x08, 0x81, 0x60, 0xd7, 0x76, 0x80, 0xb8, 0x80, 0xb8, 0x80, 0xb8,
    0x80, 0xb8, 0x80,
};

static const uint8_t unicode_prop_Emoji_table[236] = {
    0xa2, 0x05, 0x04, 0x89, 0xee, 0x03, 0x80, 0x5f, 0x8c, 0x80, 0x8b, 0x80,
    0x40, 0xd7, 0x80, 0x95, 0x80, 0xd9, 0x85, 0x8e, 0x81, 0x41, 0x6e, 0x81,
    0x8b, 0x80, 0x40, 0xa5, 0x80, 0x98, 0x8a, 0x1a, 0x40, 0xc6, 0x80, 0x40,
    0xe6, 0x81, 0x89, 0x80, 0x88, 0x80, 0xb9, 0x18, 0x84, 0x88, 0x01, 0x01,
    0x09, 0x03, 0x01, 0x00, 0x09, 0x02, 0x02, 0x0f, 0x14, 0x00, 0x04, 0x8b,
    0x8a, 0x09, 0x00, 0x08, 0x80, 0x91, 0x01, 0x81, 0x91, 0x28, 0x00, 0x0a,
    0x0f, 0x0b, 0x81, 0x8a, 0x0c, 0x09, 0x04, 0x08, 0x00, 0x81, 0x93, 0x0c,
    0x28, 0x19, 0x03, 0x01, 0x01, 0x28, 0x01, 0x00, 0x00, 0x05, 0x02, 0x05,
    0x80, 0x89, 0x81, 0x8e, 0x01, 0x03, 0x00, 0x03, 0x10, 0x80, 0x8a, 0x81,
    0xaf, 0x82, 0x88, 0x80, 0x8d, 0x80, 0x8d, 0x80, 0x41, 0x73, 0x81, 0x41,
    0xce, 0x82, 0x92, 0x81, 0xb2, 0x03, 0x80, 0x44, 0xd9, 0x80, 0x8b, 0x80,
    0x42, 0x58, 0x00, 0x80, 0x61, 0xbd, 0x69, 0x80, 0x40, 0xc9, 0x80, 0x40,
    0x9f, 0x81, 0x8b, 0x81, 0x8d, 0x01, 0x89, 0xca, 0x99, 0x01, 0x96, 0x80,
    0x93, 0x01, 0x88, 0x94, 0x81, 0x40, 0xad, 0xa1, 0x81, 0xef, 0x09, 0x02,
    0x81, 0xd2, 0x0a, 0x80, 0x41, 0x06, 0x80, 0xbe, 0x8a, 0x28, 0x97, 0x31,
    0x0f, 0x8b, 0x01, 0x19, 0x03, 0x81, 0x8c, 0x09, 0x07, 0x81, 0x88, 0x04,
    0x82, 0x8b, 0x17, 0x11, 0x00, 0x03, 0x05, 0x02, 0x05, 0xd5, 0xaf, 0xc5,
    0x27, 0x08, 0x89, 0x2a, 0x00, 0x0a, 0x01, 0x87, 0x40, 0xe4, 0x8b, 0x41,
    0x20, 0xad, 0x80, 0x89, 0x80, 0xaa, 0x03, 0x82, 0xa8, 0x0d, 0x82, 0x9c,
    0x81, 0xb2, 0xef, 0x1b, 0x14, 0x82, 0x8c, 0x85,
};

static const uint8_t unicode_prop_Emoji_Component_table[28] = {
    0xa2, 0x05, 0x04, 0x89, 0x5f, 0xd2, 0x80, 0x40, 0xd4, 0x80,
    0x60, 0xdd, 0x2a, 0x80, 0x60, 0xf3, 0xd5, 0x99, 0x41, 0xfa,
    0x84, 0x45, 0xaf, 0x83, 0x6c, 0x06, 0x6b, 0xdf,
};

static const uint8_t unicode_prop_Emoji_Modifier_table[4] = {
    0x61,
    0xf3,
    0xfa,
    0x84,
};

static const uint8_t unicode_prop_Emoji_Modifier_Base_table[63] = {
    0x60, 0x26, 0x1c, 0x80, 0x40, 0xda, 0x80, 0x8f, 0x83, 0x61, 0xcc,
    0x76, 0x80, 0xbb, 0x11, 0x01, 0x82, 0xf4, 0x09, 0x8a, 0x94, 0x92,
    0x10, 0x1a, 0x02, 0x30, 0x00, 0x97, 0x80, 0x40, 0xc8, 0x0b, 0x80,
    0x94, 0x03, 0x81, 0x40, 0xad, 0x12, 0x84, 0xd2, 0x80, 0x8f, 0x82,
    0x88, 0x80, 0x8a, 0x80, 0x42, 0x41, 0x07, 0x3d, 0x80, 0x88, 0x89,
    0x0a, 0xf5, 0x08, 0x08, 0x80, 0x90, 0x10, 0x8c,
};

static const uint8_t unicode_prop_Emoji_Presentation_table[143] = {
    0x60, 0x23, 0x19, 0x81, 0x40, 0xcc, 0x1a, 0x01, 0x80, 0x42, 0x08, 0x81,
    0x94, 0x81, 0xb1, 0x8b, 0xaa, 0x80, 0x92, 0x80, 0x8c, 0x07, 0x81, 0x90,
    0x0c, 0x0f, 0x04, 0x80, 0x94, 0x06, 0x08, 0x03, 0x01, 0x06, 0x03, 0x81,
    0x9b, 0x80, 0xa2, 0x00, 0x03, 0x10, 0x80, 0xbc, 0x82, 0x97, 0x80, 0x8d,
    0x80, 0x43, 0x5a, 0x81, 0xb2, 0x03, 0x80, 0x61, 0xc4, 0xad, 0x80, 0x40,
    0xc9, 0x80, 0x40, 0xbd, 0x01, 0x89, 0xca, 0x99, 0x00, 0x97, 0x80, 0x93,
    0x01, 0x20, 0x82, 0x94, 0x81, 0x40, 0xad, 0xa0, 0x8b, 0x88, 0x80, 0xc5,
    0x80, 0x95, 0x8b, 0xaa, 0x1c, 0x8b, 0x90, 0x10, 0x82, 0xc6, 0x00, 0x80,
    0x40, 0xba, 0x81, 0xbe, 0x8c, 0x18, 0x97, 0x91, 0x80, 0x99, 0x81, 0x8c,
    0x80, 0xd5, 0xd4, 0xaf, 0xc5, 0x28, 0x12, 0x08, 0x94, 0x0e, 0x86, 0x40,
    0xe4, 0x8b, 0x41, 0x20, 0xad, 0x80, 0x89, 0x80, 0xaa, 0x03, 0x82, 0xa8,
    0x0d, 0x82, 0x9c, 0x81, 0xb2, 0xef, 0x1b, 0x14, 0x82, 0x8c, 0x85,
};

static const uint8_t unicode_prop_Extended_Pictographic_table[152] = {
    0x40, 0xa8, 0x03, 0x80, 0x5f, 0x8c, 0x80, 0x8b, 0x80, 0x40, 0xd7, 0x80,
    0x95, 0x80, 0xd9, 0x85, 0x8e, 0x81, 0x41, 0x6e, 0x81, 0x8b, 0x80, 0xde,
    0x80, 0xc5, 0x80, 0x98, 0x8a, 0x1a, 0x40, 0xc6, 0x80, 0x40, 0xe6, 0x81,
    0x89, 0x80, 0x88, 0x80, 0xb9, 0x18, 0x28, 0x8b, 0x80, 0xf1, 0x89, 0xf5,
    0x81, 0x8a, 0x00, 0x00, 0x28, 0x10, 0x28, 0x89, 0x81, 0x8e, 0x01, 0x03,
    0x00, 0x03, 0x10, 0x80, 0x8a, 0x84, 0xac, 0x82, 0x88, 0x80, 0x8d, 0x80,
    0x8d, 0x80, 0x41, 0x73, 0x81, 0x41, 0xce, 0x82, 0x92, 0x81, 0xb2, 0x03,
    0x80, 0x44, 0xd9, 0x80, 0x8b, 0x80, 0x42, 0x58, 0x00, 0x80, 0x61, 0xbd,
    0x65, 0x40, 0xff, 0x8c, 0x82, 0x9e, 0x80, 0xbb, 0x85, 0x8b, 0x81, 0x8d,
    0x01, 0x89, 0x91, 0xb8, 0x9a, 0x8e, 0x89, 0x80, 0x93, 0x01, 0x88, 0x03,
    0x88, 0x41, 0xb1, 0x84, 0x41, 0x3d, 0x87, 0x41, 0x09, 0xaf, 0xff, 0xf3,
    0x8b, 0xd4, 0xaa, 0x8b, 0x83, 0xb7, 0x87, 0x89, 0x85, 0xa7, 0x87, 0x9d,
    0xd1, 0x8b, 0xae, 0x80, 0x89, 0x80, 0x46, 0xb6,
};

static const uint8_t unicode_prop_Default_Ignorable_Code_Point_table[51] = {
    0x40, 0xac, 0x80, 0x42, 0xa0, 0x80, 0x42, 0xcb, 0x80, 0x4b, 0x41,
    0x81, 0x46, 0x52, 0x81, 0xd4, 0x83, 0x47, 0xfb, 0x84, 0x99, 0x84,
    0xb0, 0x8f, 0x50, 0xf3, 0x80, 0x60, 0xcc, 0x9a, 0x8f, 0x40, 0xee,
    0x80, 0x40, 0x9f, 0x80, 0xce, 0x88, 0x60, 0xbc, 0xa6, 0x83, 0x54,
    0xce, 0x87, 0x6c, 0x2e, 0x84, 0x4f, 0xff,
};

typedef enum {
  UNICODE_PROP_Hyphen,
  UNICODE_PROP_Other_Math,
  UNICODE_PROP_Other_Alphabetic,
  UNICODE_PROP_Other_Lowercase,
  UNICODE_PROP_Other_Uppercase,
  UNICODE_PROP_Other_Grapheme_Extend,
  UNICODE_PROP_Other_Default_Ignorable_Code_Point,
  UNICODE_PROP_Other_ID_Start,
  UNICODE_PROP_Other_ID_Continue,
  UNICODE_PROP_Prepended_Concatenation_Mark,
  UNICODE_PROP_ID_Continue1,
  UNICODE_PROP_XID_Start1,
  UNICODE_PROP_XID_Continue1,
  UNICODE_PROP_Changes_When_Titlecased1,
  UNICODE_PROP_Changes_When_Casefolded1,
  UNICODE_PROP_Changes_When_NFKC_Casefolded1,
  UNICODE_PROP_ASCII_Hex_Digit,
  UNICODE_PROP_Bidi_Control,
  UNICODE_PROP_Dash,
  UNICODE_PROP_Deprecated,
  UNICODE_PROP_Diacritic,
  UNICODE_PROP_Extender,
  UNICODE_PROP_Hex_Digit,
  UNICODE_PROP_IDS_Binary_Operator,
  UNICODE_PROP_IDS_Trinary_Operator,
  UNICODE_PROP_Ideographic,
  UNICODE_PROP_Join_Control,
  UNICODE_PROP_Logical_Order_Exception,
  UNICODE_PROP_Noncharacter_Code_Point,
  UNICODE_PROP_Pattern_Syntax,
  UNICODE_PROP_Pattern_White_Space,
  UNICODE_PROP_Quotation_Mark,
  UNICODE_PROP_Radical,
  UNICODE_PROP_Regional_Indicator,
  UNICODE_PROP_Sentence_Terminal,
  UNICODE_PROP_Soft_Dotted,
  UNICODE_PROP_Terminal_Punctuation,
  UNICODE_PROP_Unified_Ideograph,
  UNICODE_PROP_Variation_Selector,
  UNICODE_PROP_White_Space,
  UNICODE_PROP_Bidi_Mirrored,
  UNICODE_PROP_Emoji,
  UNICODE_PROP_Emoji_Component,
  UNICODE_PROP_Emoji_Modifier,
  UNICODE_PROP_Emoji_Modifier_Base,
  UNICODE_PROP_Emoji_Presentation,
  UNICODE_PROP_Extended_Pictographic,
  UNICODE_PROP_Default_Ignorable_Code_Point,
  UNICODE_PROP_ID_Start,
  UNICODE_PROP_Case_Ignorable,
  UNICODE_PROP_ASCII,
  UNICODE_PROP_Alphabetic,
  UNICODE_PROP_Any,
  UNICODE_PROP_Assigned,
  UNICODE_PROP_Cased,
  UNICODE_PROP_Changes_When_Casefolded,
  UNICODE_PROP_Changes_When_Casemapped,
  UNICODE_PROP_Changes_When_Lowercased,
  UNICODE_PROP_Changes_When_NFKC_Casefolded,
  UNICODE_PROP_Changes_When_Titlecased,
  UNICODE_PROP_Changes_When_Uppercased,
  UNICODE_PROP_Grapheme_Base,
  UNICODE_PROP_Grapheme_Extend,
  UNICODE_PROP_ID_Continue,
  UNICODE_PROP_Lowercase,
  UNICODE_PROP_Math,
  UNICODE_PROP_Uppercase,
  UNICODE_PROP_XID_Continue,
  UNICODE_PROP_XID_Start,
  UNICODE_PROP_Cased1,
  UNICODE_PROP_COUNT,
} UnicodePropertyEnum;

static const char unicode_prop_name_table[] =
    "ASCII_Hex_Digit,AHex"
    "\0"
    "Bidi_Control,Bidi_C"
    "\0"
    "Dash"
    "\0"
    "Deprecated,Dep"
    "\0"
    "Diacritic,Dia"
    "\0"
    "Extender,Ext"
    "\0"
    "Hex_Digit,Hex"
    "\0"
    "IDS_Binary_Operator,IDSB"
    "\0"
    "IDS_Trinary_Operator,IDST"
    "\0"
    "Ideographic,Ideo"
    "\0"
    "Join_Control,Join_C"
    "\0"
    "Logical_Order_Exception,LOE"
    "\0"
    "Noncharacter_Code_Point,NChar"
    "\0"
    "Pattern_Syntax,Pat_Syn"
    "\0"
    "Pattern_White_Space,Pat_WS"
    "\0"
    "Quotation_Mark,QMark"
    "\0"
    "Radical"
    "\0"
    "Regional_Indicator,RI"
    "\0"
    "Sentence_Terminal,STerm"
    "\0"
    "Soft_Dotted,SD"
    "\0"
    "Terminal_Punctuation,Term"
    "\0"
    "Unified_Ideograph,UIdeo"
    "\0"
    "Variation_Selector,VS"
    "\0"
    "White_Space,space"
    "\0"
    "Bidi_Mirrored,Bidi_M"
    "\0"
    "Emoji"
    "\0"
    "Emoji_Component"
    "\0"
    "Emoji_Modifier"
    "\0"
    "Emoji_Modifier_Base"
    "\0"
    "Emoji_Presentation"
    "\0"
    "Extended_Pictographic"
    "\0"
    "Default_Ignorable_Code_Point,DI"
    "\0"
    "ID_Start,IDS"
    "\0"
    "Case_Ignorable,CI"
    "\0"
    "ASCII"
    "\0"
    "Alphabetic,Alpha"
    "\0"
    "Any"
    "\0"
    "Assigned"
    "\0"
    "Cased"
    "\0"
    "Changes_When_Casefolded,CWCF"
    "\0"
    "Changes_When_Casemapped,CWCM"
    "\0"
    "Changes_When_Lowercased,CWL"
    "\0"
    "Changes_When_NFKC_Casefolded,CWKCF"
    "\0"
    "Changes_When_Titlecased,CWT"
    "\0"
    "Changes_When_Uppercased,CWU"
    "\0"
    "Grapheme_Base,Gr_Base"
    "\0"
    "Grapheme_Extend,Gr_Ext"
    "\0"
    "ID_Continue,IDC"
    "\0"
    "Lowercase,Lower"
    "\0"
    "Math"
    "\0"
    "Uppercase,Upper"
    "\0"
    "XID_Continue,XIDC"
    "\0"
    "XID_Start,XIDS"
    "\0";

static const uint8_t* const unicode_prop_table[] = {
    unicode_prop_Hyphen_table,
    unicode_prop_Other_Math_table,
    unicode_prop_Other_Alphabetic_table,
    unicode_prop_Other_Lowercase_table,
    unicode_prop_Other_Uppercase_table,
    unicode_prop_Other_Grapheme_Extend_table,
    unicode_prop_Other_Default_Ignorable_Code_Point_table,
    unicode_prop_Other_ID_Start_table,
    unicode_prop_Other_ID_Continue_table,
    unicode_prop_Prepended_Concatenation_Mark_table,
    unicode_prop_ID_Continue1_table,
    unicode_prop_XID_Start1_table,
    unicode_prop_XID_Continue1_table,
    unicode_prop_Changes_When_Titlecased1_table,
    unicode_prop_Changes_When_Casefolded1_table,
    unicode_prop_Changes_When_NFKC_Casefolded1_table,
    unicode_prop_ASCII_Hex_Digit_table,
    unicode_prop_Bidi_Control_table,
    unicode_prop_Dash_table,
    unicode_prop_Deprecated_table,
    unicode_prop_Diacritic_table,
    unicode_prop_Extender_table,
    unicode_prop_Hex_Digit_table,
    unicode_prop_IDS_Binary_Operator_table,
    unicode_prop_IDS_Trinary_Operator_table,
    unicode_prop_Ideographic_table,
    unicode_prop_Join_Control_table,
    unicode_prop_Logical_Order_Exception_table,
    unicode_prop_Noncharacter_Code_Point_table,
    unicode_prop_Pattern_Syntax_table,
    unicode_prop_Pattern_White_Space_table,
    unicode_prop_Quotation_Mark_table,
    unicode_prop_Radical_table,
    unicode_prop_Regional_Indicator_table,
    unicode_prop_Sentence_Terminal_table,
    unicode_prop_Soft_Dotted_table,
    unicode_prop_Terminal_Punctuation_table,
    unicode_prop_Unified_Ideograph_table,
    unicode_prop_Variation_Selector_table,
    unicode_prop_White_Space_table,
    unicode_prop_Bidi_Mirrored_table,
    unicode_prop_Emoji_table,
    unicode_prop_Emoji_Component_table,
    unicode_prop_Emoji_Modifier_table,
    unicode_prop_Emoji_Modifier_Base_table,
    unicode_prop_Emoji_Presentation_table,
    unicode_prop_Extended_Pictographic_table,
    unicode_prop_Default_Ignorable_Code_Point_table,
    unicode_prop_ID_Start_table,
    unicode_prop_Case_Ignorable_table,
};

static const uint16_t unicode_prop_len_table[] = {
    countof(unicode_prop_Hyphen_table),
    countof(unicode_prop_Other_Math_table),
    countof(unicode_prop_Other_Alphabetic_table),
    countof(unicode_prop_Other_Lowercase_table),
    countof(unicode_prop_Other_Uppercase_table),
    countof(unicode_prop_Other_Grapheme_Extend_table),
    countof(unicode_prop_Other_Default_Ignorable_Code_Point_table),
    countof(unicode_prop_Other_ID_Start_table),
    countof(unicode_prop_Other_ID_Continue_table),
    countof(unicode_prop_Prepended_Concatenation_Mark_table),
    countof(unicode_prop_ID_Continue1_table),
    countof(unicode_prop_XID_Start1_table),
    countof(unicode_prop_XID_Continue1_table),
    countof(unicode_prop_Changes_When_Titlecased1_table),
    countof(unicode_prop_Changes_When_Casefolded1_table),
    countof(unicode_prop_Changes_When_NFKC_Casefolded1_table),
    countof(unicode_prop_ASCII_Hex_Digit_table),
    countof(unicode_prop_Bidi_Control_table),
    countof(unicode_prop_Dash_table),
    countof(unicode_prop_Deprecated_table),
    countof(unicode_prop_Diacritic_table),
    countof(unicode_prop_Extender_table),
    countof(unicode_prop_Hex_Digit_table),
    countof(unicode_prop_IDS_Binary_Operator_table),
    countof(unicode_prop_IDS_Trinary_Operator_table),
    countof(unicode_prop_Ideographic_table),
    countof(unicode_prop_Join_Control_table),
    countof(unicode_prop_Logical_Order_Exception_table),
    countof(unicode_prop_Noncharacter_Code_Point_table),
    countof(unicode_prop_Pattern_Syntax_table),
    countof(unicode_prop_Pattern_White_Space_table),
    countof(unicode_prop_Quotation_Mark_table),
    countof(unicode_prop_Radical_table),
    countof(unicode_prop_Regional_Indicator_table),
    countof(unicode_prop_Sentence_Terminal_table),
    countof(unicode_prop_Soft_Dotted_table),
    countof(unicode_prop_Terminal_Punctuation_table),
    countof(unicode_prop_Unified_Ideograph_table),
    countof(unicode_prop_Variation_Selector_table),
    countof(unicode_prop_White_Space_table),
    countof(unicode_prop_Bidi_Mirrored_table),
    countof(unicode_prop_Emoji_table),
    countof(unicode_prop_Emoji_Component_table),
    countof(unicode_prop_Emoji_Modifier_table),
    countof(unicode_prop_Emoji_Modifier_Base_table),
    countof(unicode_prop_Emoji_Presentation_table),
    countof(unicode_prop_Extended_Pictographic_table),
    countof(unicode_prop_Default_Ignorable_Code_Point_table),
    countof(unicode_prop_ID_Start_table),
    countof(unicode_prop_Case_Ignorable_table),
};

#endif /* CONFIG_ALL_UNICODE */
